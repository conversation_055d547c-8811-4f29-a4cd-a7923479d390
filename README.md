
# WhatNext? - Betting Strategy Simulator

## Project Overview

This application helps users simulate and track their betting strategies based on predefined rules. Users can input game hands (Player or Banker), see dynamic bet suggestions, track outcomes in real-time, and visualize profit/loss trends across game columns.

## Key Features

- **Game Hand Inputs**: Enter hands (P for Player, B for Banker) and see outcomes dynamically.
- **Customizable Betting Settings**:
  - Adjustable base bet amount
  - Configurable raise bet amount
  - Flexible row count for column organization
  - Multiple bet strategies and prediction logic
  - Optional extension factor for "Extend" prediction logic
  - Configurable reset betting hands
- **Dynamic Bet Suggestions**: Get recommended bet amounts based on previous outcomes.
- **Profit/Loss Tracking**: See real-time calculations of cumulative profit/loss.
- **Visual Organization**: View hands and bets in a column-based layout with color-coded win/loss indicators.
- **Recovery Mode**: Visual indicators when the system is recovering from losses.
- **Data Export**: Export game history for further analysis.

## Prediction Logic Options

- **Same**: Predicts the same outcome as the previous hand with recovery mode for losing columns.
- **Same No Recovery**: Similar to "Same" but without recovery mode functionality.
- **Mirror**: Predicts the opposite of what would have been predicted with Same logic.
- **Anti-Mirror**: Predicts the opposite of Mirror prediction.
- **Extend**: Extends patterns vertically by a configurable factor (2x or 3x).
- **Win-Loss**: Records simple win/loss outcomes rather than Player/Banker hands.

## Next Bet Amount Logic

The simulator calculates the next bet amount differently based on the selected prediction logic:

### For "Same" and "Same No Recovery" Logic:
- **Row 1 of new columns**: (Column's last row loss + RBA) + (Previous Column's row 1 loss + RBA) + RBA
- **Other rows**: Maximum of:
  - Base Bet (BB)
  - Same Row Previous Column Loss + Raise Bet Amount (SRPCL + RBA)
  - Previous Loss in Same Column + Raise Bet Amount (PLSC + RBA)
  - Previous Column's Last Row Loss + Raise Bet Amount (PCLRL + RBA)
  - Previous Column's Row 1 Loss + Raise Bet Amount (PCR1L + RBA)

### Recovery Mode:
When a column has all losses, recovery mode activates with:
- Initial recovery bet: (Column Loss Amount + (Row Count × Base Bet)) ÷ (Row Count - 1)
- Continuing recovery: (Column Loss Amount × 2 + (Row Count × Base Bet)) ÷ (Row Count - 1)

## Detailed Documentation

For more detailed information about the prediction logics, betting calculations, and UI components, see the [Prediction Logics Documentation](./docs/PredictionLogics.md).

## Technologies Used

This project is built with:

- Vite - Fast build tool and development server
- TypeScript - Type-safe JavaScript
- React - UI component library
- shadcn-ui - Pre-built UI components
- Tailwind CSS - Utility-first CSS framework
- React Query - Data fetching and state management
- Recharts - Data visualization
- Vitest - Testing framework

## Getting Started

To run this project locally:

```sh
# Clone the repository
git clone <repository-url>

# Navigate to the project directory
cd betting-strategy-simulator

# Install dependencies
npm install

# Start the development server
npm run dev
```

## Application Structure

- **Components**: UI building blocks for the application
- **Hooks**: Custom React hooks for state management and logic
- **Utils**: Utility functions for bet calculations and data processing
- **Types**: TypeScript type definitions

## Testing

This project includes comprehensive test coverage using Vitest and React Testing Library:

```sh
# Run tests
npm test
```

## Deployment

This application can be deployed using Lovable's built-in deployment feature or exported to your preferred hosting platform.

## License

This project is available for personal and commercial use.
