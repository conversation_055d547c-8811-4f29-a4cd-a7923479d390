
# Betting Strategy Simulator - Prediction Logic Documentation

## Table of Contents
1. [Overview](#overview)
2. [Prediction Logic Types](#prediction-logic-types)
3. [Implementation Architecture](#implementation-architecture)
4. [Prediction Logic Implementations](#prediction-logic-implementations)
5. [Bet Settings](#bet-settings)
6. [Game Statistics](#game-statistics)
7. [Continuous Losses Analysis](#continuous-losses-analysis)
8. [Game Grid Display Logic](#game-grid-display-logic)
9. [Next Bet Amount Logic](#next-bet-amount-logic)
10. [Next Bet Amount Evidence](#next-bet-amount-evidence)
11. [Game Controller Panel](#game-controller-panel)

## Overview

The Betting Strategy Simulator implements multiple prediction logic strategies to determine bet amounts and predict outcomes. This document provides a comprehensive explanation of how each component functions within these prediction logics.

## Prediction Logic Types

The application supports six different prediction logic types:

1. **Same**: Predicts the same outcome as the previous hand. Includes recovery mode when a column has all losses.
2. **Same No Recovery**: Similar to "Same" but without the recovery mode feature.
3. **Mirror**: Predicts the next hand based on patterns from previous columns.
4. **Anti-Mirror**: Inverts the Mirror prediction logic.
5. **Extend**: Extends patterns vertically by a configurable factor (2x or 3x).
6. **Win-Loss**: Records simple win/loss outcomes rather than Player/Banker hands.
7. **Win-Loss-Prev-Rows**: Similar to Win-Loss but with consideration of previous rows when calculating bet amounts.

## Implementation Architecture

The prediction logic system follows a modular architecture with complete isolation between different prediction strategies:

### Core Router Component
- **File**: `src/utils/betCalculationService.ts`
- **Purpose**: Acts as a router that identifies which prediction logic is being used and directs the calculation to the appropriate specialized function.
- **Functions**:
  - `calculateNextBet()`: Routes to the appropriate calculation function based on prediction logic
  - `determineResult()`: Routes to the appropriate result determination function based on prediction logic

### Client vs Server Processing
- **Client-side Logics**: win-loss, win-loss-prev-rows, same-no-recovery, extend
- **Server-side Logics**: same, mirror, anti-mirror (via Supabase edge functions)

## Prediction Logic Implementations

### Win-Loss Logic
- **Primary File**: `src/utils/winLossCalculation.ts`
- **Key Functions**:
  - `calculateWinLossNextBet()`: Calculates the next bet amount
  - `determineWinLossResult()`: Determines if a hand is a win or loss
  - `getPreviousLossInColumn()`: Gets previous loss in the same column
  - `getSameRowPreviousColumnLoss()`: Gets same row previous column loss
  - `getPreviousColumnLastRowLoss()`: Gets the last row loss from previous column
  - `getConsecutiveLosses()`: Gets number of consecutive losses
  - `getWinLossNextBetDetails()`: Provides detailed calculation information for display

### Win-Loss-Prev-Rows Logic
- **Primary File**: `src/utils/winLossPrevRowsCalculation.ts`
- **Key Functions**:
  - `calculateWinLossPrevRowsNextBet()`: Calculates the next bet amount
  - `determineWinLossPrevRowsResult()`: Determines if a hand is a win or loss
  - `calculateNextPositionForWinLossPrevRows()`: Calculates the next position
  - `getSameRowPreviousColumnLossesForWinLossPrevRows()`: Gets sum of previous column losses
  - `countLossesAfterLastWinInRow()`: Counts losses after last win in the same row
  - `getWinLossPrevRowsNextBetDetails()`: Provides detailed calculation information for display

### Same-No-Recovery Logic
- **Primary File**: `src/utils/sameNoRecoveryCalculation.ts`
- **Key Functions**:
  - `calculateSameNoRecoveryNextBet()`: Calculates the next bet amount
  - `determineSameNoRecoveryResult()`: Determines if a hand is a win or loss
  - `getPreviousColumnRow1Loss()`: Gets previous column's Row 1 loss
  - `getSameRowPreviousColumnLoss()`: Gets same row previous column loss
  - `getPreviousLossInColumn()`: Gets previous loss in the same column
  - `getPreviousColumnLastRowLoss()`: Gets the last row loss from previous column

### Extend Logic
- **Primary File**: `src/utils/extendPredictionCalculation.ts`
- **Key Functions**:
  - `calculateExtendNextBet()`: Calculates the next bet amount
  - `determineExtendResult()`: Determines if a hand is a win or loss
  - `isPatternCell()`: Checks if a position is a pattern cell
  - `getPatternSourceRow()`: Gets the source row for a pattern cell
  - `calculateNextPositionForExtend()`: Calculates the next position
  - `wasSameRowPreviousColumnWin()`: Checks if same row in previous column was a win
  - `wasRow3WinInCurrentColumn()`: Checks if Row 3 in the current column was a win
  - `getRow3LossInCurrentColumn()`: Gets the Row 3 loss in current column
  - `getPreviousLossInColumn()`: Gets previous loss in the same column
  - `getSameRowPreviousColumnLoss()`: Gets same row's previous column loss
  - `getPreviousColumnLastRowLoss()`: Gets previous column's last row loss

### Same Logic (Server-side)
- **Implemented in**: Supabase edge function (`supabase/functions/calculate-next-bet/index.ts`)
- **Accessed via**: `betCalculationService.ts` which calls the Supabase function

### Mirror Logic (Server-side)
- **Implemented in**: Supabase edge function (`supabase/functions/calculate-next-bet/index.ts`)
- **Accessed via**: `betCalculationService.ts` which calls the Supabase function

### Anti-Mirror Logic (Server-side)
- **Implemented in**: Supabase edge function (`supabase/functions/calculate-next-bet/index.ts`)
- **Accessed via**: `betCalculationService.ts` which calls the Supabase function

## Bet Settings

The Bet Settings panel controls the core parameters of the betting system:

### Parameters
- **Base Bet Amount**: The starting bet amount for each new betting sequence (default: 1000).
- **Raise Bet Amount**: The amount added to previous losses when calculating the next bet (default: 1000).
- **Row Count**: Determines the number of rows in each column (options: 3, 4, 6, 8).
- **Bet Strategy**: Either "Loss" or "Win" mode, determining how results are calculated.
- **Prediction Logic**: Selects the active prediction algorithm.
- **Reset Betting Hands**: Number of hands after which to reset betting to base amount if profitable.
- **Extension Factor**: For "Extend" prediction logic, sets the pattern repetition factor (2x or 3x).

### Implementation
- When prediction logic is changed, the application recalculates the next bet amount immediately.
- Row count changes are only permitted when the game is reset to avoid disrupting active games.
- Extension factor changes are also restricted to when the game is reset.

## Game Statistics

The Game Statistics panel provides real-time performance metrics:

### Displayed Statistics
- **Total Hands**: Count of all betting hands played (excluding non-betting hands).
- **Win Rate**: Percentage of hands that resulted in wins.
- **Profit/Loss**: Current cumulative profit or loss amount.
- **Win/Loss Streak**: Current consecutive wins or losses.
- **Longest Win/Loss Streak**: Highest consecutive wins or losses recorded.
- **Total Columns**: Number of columns with at least one hand entry.
- **Column Win/Loss Rate**: Percentage of columns with majority wins/losses.

### Implementation
- Statistics update in real-time after each hand is played.
- Column-based metrics provide insights into the effectiveness of the strategy across game sections.
- For "Extend" logic, pattern cells are excluded from statistical calculations.

## Continuous Losses Analysis

The Continuous Losses Analysis panel focuses on tracking loss patterns:

### Metrics Tracked
- **Continuous Losses by Column**: Count of columns with consecutive loss patterns.
- **Recovery Mode Count**: Number of times recovery mode was activated.
- **Highest Loss Amount**: Maximum loss recorded in a single hand.
- **Highest Recovery Bet**: Maximum recovery bet amount used.
- **Total Recovery Amount**: Sum of all recovery mode bets.

### Implementation
- For "Same" prediction logic, recovery mode activates when all bets in a column result in losses.
- Recovery bets are calculated using the formula: (Loss Amount + Base Bet × Row Count) ÷ (Row Count - 1)
- For continuing recovery, the loss amount is doubled before calculation.

## Game Grid Display Logic

The Game Grid visualizes the game progress with color-coded cells:

### Grid Elements
- **Row 1 Cells**: Display Player (blue) or Banker (red) hands without bet amounts.
- **Pattern Cells**: In "Extend" mode, pattern cells match row 1 colors.
- **Betting Cells**: Color-coded green for wins and red for losses.
- **Cell Data**: Shows hand value (P/B or W/L), bet amount, and special indicators.
- **Special Indicators**: Dots for reset points, recovery mode status, and pattern cells.

### Display Modes
- **Detailed View**: Shows complete bet information, hand results, and indicators.
- **Simple View**: Condensed grid with essential information only.

### Implementation
- Grid automatically scrolls to show the latest column.
- "Extend" logic displays pattern cells at calculated positions.
- Pattern cells in "Extend" mode are auto-populated based on source cell values.
- Grid dimensions adjust based on row count and prediction logic.

## Next Bet Amount Logic

The Next Bet Amount calculations differ by prediction logic type:

### "Same" Logic (Server-side)
1. For Row 1 (first row of a column): Next bet is calculated by checking:
   - (Column's last row loss + RBA) + (Previous Column's row 1 loss + RBA) + RBA
   
2. For other rows, the next bet is the maximum of:
   - Base Bet (BB)
   - Same Row Previous Column Loss + Raise Bet Amount (SRPCL + RBA)
   - Previous Loss in Same Column + Raise Bet Amount (PLSC + RBA)
   - Previous Column's Last Row Loss + Raise Bet Amount (PCLRL + RBA)
   - Previous Column's Row 1 Loss + Raise Bet Amount (PCR1L + RBA)

3. Recovery Mode (in "Same" only):
   - Initial recovery bet: (Column Loss Amount + (Row Count × Base Bet)) ÷ (Row Count - 1)
   - Continuing recovery: (Column Loss Amount × 2 + (Row Count × Base Bet)) ÷ (Row Count - 1)

### "Same No Recovery" Logic
1. For Column 1, Row 1: Base Bet
2. For Column 1, Row 2+:
   - If previous row was a win: Base Bet
   - Otherwise: Previous Loss in Column + RBA
3. For Column 2+, Row 1: No betting (observation row)
4. For Column 2+, Row 2:
   - RBA + (PCLRL + RBA) + (SRPCL + RBA)
5. For Column 2+, Row 3+:
   - RBA + (SRPCL + RBA) + (PLSC + RBA)

### "Win-Loss" Logic
1. For Column 1, Row 1: Base Bet
2. For Column 1, Row 2+:
   - Base Bet + (RBA * consecutive losses)
3. For Column 2+, Row 1:
   - RBA + SRPC + (PCLRL + RBA)
4. For Column 2+, Row 2+:
   - If previous row was a win: Base Bet + SRPC
   - Otherwise: RBA + SRPC + (PLSC + RBA)

### "Win-Loss-Prev-Rows" Logic
1. For Column 1 or after reset: Base Bet
2. For any row in Column 1: Base Bet
3. For other columns:
   - If previous column in same row was a win: Base Bet
   - Otherwise: Sum of previous losses + (RBA * loss count) + RBA
4. Special handling for Row 5 when Row 3 in same column is a loss:
   - (Row 3 Loss + RBA) + RBA

### "Mirror" and "Anti-Mirror" Logic (Server-side)
1. First hand in new column: Base Bet
2. After loss: Previous Bet + Raise Bet Amount
3. After win: Base Bet

### "Extend" Logic
1. **Pattern Cells (Row 1 and Row 4)**: No bet (0)
2. **Column 1**:
   - Row 2 and Row 3: Base Bet
   - Row 5 (if Row 3 has a loss): Row 3 Loss + (RBA × 2)
   - Row 5 (if Row 3 is a win): Base Bet
   - Other rows: Previous Loss in Same Column + RBA
3. **Column 2-3, Row 2**:
   - If same row previous column was a win: Base Bet
   - If previous row in same column was a win: Base Bet
   - Otherwise: RBA + (SRPC + RBA) + (PLSC + RBA)
4. **Column 4+, Row 2**:
   - Formula: RBA + (SRPC + RBA) + (PCLRL + RBA)
5. **Row 3**:
   - If previous wins exist (same row prev column or prev row same column): Base Bet
   - Otherwise: RBA + (SRPC + RBA) + (PLSC + RBA)
6. **Row 5**:
   - If Row 3 in current column has a loss: (Row 3 Loss) + (RBA × 2)
   - If Row 3 is a win but SRPC exists: RBA + (SRPC + RBA)
   - If Row 3 is a win and no SRPC: Base Bet
7. **Other Rows**:
   - Reset to Base Bet after any win in previous row or same row previous column
   - Otherwise: RBA + (SRPC + RBA) + (PLSC + RBA)

## Next Bet Amount Evidence

The Next Bet Amount Evidence panel displays the calculation details:

### "Same" and "Same No Recovery" Evidence Display
- **BBA**: Base Bet Amount
- **SRPC + RBA**: Same Row Previous Column Loss + Raise Bet Amount
- **PLSC + RBA**: Previous Loss in Same Column + Raise Bet Amount
- **PCLRL + RBA**: Previous Column's Last Row Loss + Raise Bet Amount
- **PCR1L + RBA**: Previous Column's Row 1 Loss + Raise Bet Amount
- **Next Bet**: The final calculated bet amount

### "Mirror" and "Anti-Mirror" Evidence Display
- **Base Bet**: The configured base bet amount
- **Previous Result**: Win, Loss, or First (for first hand)
- **Logic**: Mirror or Anti-Mirror indicator
- **Next Bet**: The final calculated bet amount

### "Extend" Evidence Display
- **BBA**: Base Bet Amount
- **Win Checks**: Previous row/column win checks results
- **RBA**: Raise Bet Amount (starting value)
- **SRPC + RBA**: Same Row Previous Column Loss + Raise Bet Amount
- **PLSC + RBA**: Previous Loss in Same Column + Raise Bet Amount
- **PCLRL + RBA**: Previous Column's Last Row Loss + Raise Bet Amount
- **Row 3 Loss + (RBA × 2)**: For Row 5 calculations
- **Next Bet**: The final calculated bet amount

### "Win-Loss" and "Win-Loss-Prev-Rows" Evidence Display
- Similar to "Same No Recovery" but with different column labels reflecting the win-loss nature.
- For Win-Loss-Prev-Rows, includes loss count and sum of previous losses.

### Recovery Mode Evidence Display
- **Total Loss**: Sum of losses in the previous column
- **Formula**: (Total Loss + (Row Count × Base Bet)) ÷ (Row Count - 1)
- **Per Row**: Calculated amount per betting row
- **Recovery Bet**: Final recovery bet amount

## Game Controller Panel

The Game Controller panel provides the game interaction interface:

### Components
- **Next Bet Display**: Shows the calculated next bet amount
- **Recovery Mode Indicator**: Appears when recovery mode is active
- **Bet Calculation Details**: Expandable panel showing calculation evidence
- **Player/Banker Buttons**: For entering hand results (or Win/Loss in Win-Loss mode)
- **Undo Button**: Reverses the last action
- **Reset Button**: Clears all game data with confirmation dialog

### Behavior by Prediction Logic
- **Same**: Full calculation details with recovery mode support
- **Same No Recovery**: Full calculation details without recovery mode
- **Mirror/Anti-Mirror**: Shows prediction for next hand
- **Extend**: Shows calculation with extension factor consideration
- **Win-Loss** and **Win-Loss-Prev-Rows**: Changes button labels to "Win" and "Loss" instead of "Player" and "Banker"

### Recovery Mode Indication
- Orange background with warning icon when recovery mode is active
- Detailed recovery calculation shown in the evidence panel
- Toast notifications for recovery mode activation, updates, and deactivation

### Toast Notifications
- Provides feedback for actions like hand entry, recovery mode changes, and errors
- Shows specific messages based on prediction logic and current game state
- Alerts user when bet strategies are reset or recovery mode is changed
