# WhatNext Betting System PRD

## Product Overview
WhatNext is a sophisticated betting system application that implements multiple prediction logics for baccarat and similar games. The system tracks outcomes in a grid-based interface and calculates optimal betting amounts based on various strategies.

## Core Features

### 1. Multiple Prediction Logics
- Same Recovery Logic
- Extend Logic
- Extend-2-1-1 Logic (clone of Extend)
- Support for future prediction logics

### 2. Grid-Based Tracking System
- Dynamic grid expansion based on prediction logic
- Win/loss outcome tracking
- Support for "No Bet" cells
- Visual indicators for bet outcomes

### 3. Betting Amount Calculation
- Base Bet Amount (BBA) as foundational betting unit
- Complex formulas based on previous outcomes
- Recovery mechanisms after losses
- Reset mechanisms after consecutive wins

## Detailed Requirements

### Same Recovery Logic
#### Core Functionality
- Implement BBA (Base Bet Amount) as the foundational betting unit
- Track win/loss outcomes in the grid system
- Implement "No Bet" cell logic for specific conditions
- Calculate next bet amounts using specific formulas

#### Key Formulas
- First Column Formula: ((PLSC if loss + RBA) + (PCLRL if loss + RBA))
- Subsequent Columns Formula: ((SRPC if loss + RBA) + (PLSC if loss + RBA) + (PCLRL if loss + RBA))
- Reset Formula After Consecutive Losses: Total Losses before a win + (Number of total losses × BBA)
- Use BBA after two consecutive wins (excluding No Bet cells)

#### No Bet Rules
- Rule 1: When both cells in a column show losses, the corresponding cell in the next column becomes No Bet
- Rule 2: When a No Bet cell is a loss, the next cell is also No Bet
- Rule 3: Skip No Bet cells when determining reference cells

#### Special Cases
- Handle consecutive losses leading to reset formula application
- Implement logic for wins after consecutive losses
- Handle multiple "No Bet" cells in sequence
- Reset to BBA after two consecutive wins

### Extend Logic
#### Grid Dimensions
- When "Extend by 2" is selected:
  - 3 Rows layout transforms to 6 Rows × 12 Columns
  - 4 Rows layout transforms to 8 Rows × 10 Columns
- When "Extend by 3" is selected:
  - 3 Rows layout transforms to 9 Rows × 10 Columns
  - 4 Rows layout transforms to 12 Rows × 8 Columns

#### Pattern Logic
- For "Extend by 2" (using 3 Rows base example):
  - First 3 rows follow the original pattern
  - Row 4 copies the value from row 3 (becomes a No Bet cell)
  - Rows 5-6 follow the row 4 value pattern
- For "Extend by 3" (using 3 Rows base example):
  - First 3 rows follow the original pattern
  - Row 4 copies the value from row 3 (becomes a No Bet cell)
  - Rows 5-6 follow the row 4 value pattern
  - Row 7 copies the value from row 6 (becomes a No Bet cell)
  - Rows 8-9 follow the row 7 value pattern

#### Cell Coloring Logic
- If a No Bet cell contains "B":
  - Following Bet cells with "B" are colored Green
  - Following Bet cells with "P" are colored Red
- If a No Bet cell contains "P":
  - Following Bet cells with "P" are colored Green
  - Following Bet cells with "B" are colored Red

#### Next Bet Amount Logic
- For "Extend by 2" Configuration:
  - When using 3 Rows base (resulting in 6 Rows):
    - Row 3's win/loss result determines the bet amount for Row 5
    - Row 6's result affects the next column's betting pattern
  - When using 4 Rows base (resulting in 8 Rows):
    - Row 4's win/loss result determines the bet amount for Row 6
    - Row 8's result affects the next column's betting pattern
- For "Extend by 3" Configuration:
  - When using 3 Rows base (resulting in 9 Rows):
    - Row 3's win/loss result determines the bet amount for Row 5
    - Row 6's win/loss result determines the bet amount for Row 8
    - Row 9's result affects the next column's betting pattern
  - When using 4 Rows base (resulting in 12 Rows):
    - Row 4's win/loss result determines the bet amount for Row 6
    - Row 8's win/loss result determines the bet amount for Row 10
    - Row 12's result affects the next column's betting pattern

### Extend-2-1-1 Logic
- Clone of the original Extend logic with identical functionality
- Separate implementation to allow for future independent modifications
- Includes all formulas, grid expansions, and bet calculations from Extend logic

## Technical Requirements

### UI Components
- Dynamic grid display with proper cell coloring
- Bet amount calculation display
- Prediction logic selector
- Base Bet Amount input
- Game controls (reset, undo, etc.)

### State Management
- Track current grid state
- Store win/loss history
- Calculate and display next bet amounts
- Provide detailed bet calculation evidence

### Testing
- Unit tests for all prediction logics
- Integration tests for UI components
- End-to-end tests for complete betting workflows
- Special case testing for edge conditions

## Future Enhancements
- Additional prediction logics
- Statistical analysis of betting outcomes
- Performance optimizations for large grids
- Mobile-responsive design
- User accounts and saved betting sessions
