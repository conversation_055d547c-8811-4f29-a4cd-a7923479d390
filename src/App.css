
#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

/* Add styling for mobile device screens */
@media (max-width: 768px) {
  #root {
    padding: 1rem;
  }
  
  .table th, .table td {
    padding: 0.5rem;
  }
  
  /* Ensure controls are visible at the bottom of mobile screens */
  .game-controls-container {
    position: sticky;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 10;
    padding-bottom: env(safe-area-inset-bottom, 1rem);
    background-color: var(--background);
    border-top: 1px solid var(--border);
  }
}

/* Add padding for iOS safe area at the bottom */
.pb-safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom, 1rem);
}

/* Add font size for extra small text */
.text-2xs {
  font-size: 0.65rem;
  line-height: 1rem;
}

/* Make tooltip text a bit smaller for better UI */
.tooltip-content {
  font-size: 0.75rem;
}

/* Improve cell hover effects */
.table tbody tr td:hover {
  position: relative;
  z-index: 1;
}

/* Make scrollbars visible and usable with increased dimensions */
[data-radix-scroll-area-viewport]::-webkit-scrollbar {
  width: 10px !important;
  height: 10px !important;
  display: block !important;
}

[data-radix-scroll-area-viewport]::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

[data-radix-scroll-area-viewport]::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  min-width: 40px;
  min-height: 40px;
}

[data-radix-scroll-area-viewport]::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* Ensure horizontal scrolling works smoothly */
.min-w-max {
  min-width: max-content;
}

/* Force visible scrollbars on mobile and touch devices */
@media (hover: none) {
  [data-radix-scroll-area-viewport] {
    -webkit-overflow-scrolling: touch;
    overflow-x: auto !important;
    scrollbar-width: auto !important;
  }
  
  .ScrollAreaRoot {
    overflow: auto !important;
  }
  
  /* Better touch scrolling for tables */
  .table-container {
    -webkit-overflow-scrolling: touch;
    overflow-x: auto !important;
  }
  
  /* Force horizontal scroll to be visible */
  [data-orientation="horizontal"] {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
  }
}

/* Improved scrolling for touch devices */
html.touch {
  overflow-x: hidden;
}

html.touch * {
  -webkit-tap-highlight-color: transparent;
}

html.touch [data-radix-scroll-area-viewport] {
  overflow: auto !important;
  -webkit-overflow-scrolling: touch;
}
