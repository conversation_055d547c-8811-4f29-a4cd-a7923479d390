
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import BetSettings from './BetSettings';
import { GameEntry, PredictionLogicOption } from '@/types/game';

describe('BetSettings', () => {
  // Define predictionLogicOptions for tests
  const predictionLogicOptions: PredictionLogicOption[] = [
    { value: 'same', label: 'Same' },
    { value: 'same-no-recovery', label: 'Same (No Recovery)' },
    { value: 'mirror', label: 'Mirror' },
    { value: 'anti-mirror', label: 'Anti-Mirror' },
    { value: 'extend', label: 'Extend' },
    { value: 'win-loss', label: 'Win Loss' },
    { value: 'win-loss-prev-rows', label: 'Win Loss - Previous Rows' }
  ];
  
  const mockProps = {
    baseBet: 1000,
    raiseBet: 1000,
    rowCount: 4,
    betStrategy: 'loss' as const,
    onBaseBetChange: vi.fn(),
    onRaiseBetChange: vi.fn(),
    onRowCountChange: vi.fn(),
    onBetStrategyChange: vi.fn(),
    entries: [] as GameEntry[],
    predictionLogic: 'same' as const,
    onPredictionLogicChange: vi.fn(),
    resetBettingHands: 3,
    onResetBettingHandsChange: vi.fn(),
    layout: 'horizontal' as const,
    predictionLogicOptions: predictionLogicOptions,
    hideBetStrategy: false,
    hideResetBettingHands: false
  };
  
  beforeEach(() => {
    vi.clearAllMocks();
  });
  
  it('renders all betting settings correctly', () => {
    render(<BetSettings {...mockProps} />);
    
    expect(screen.getByText('Base Bet')).toBeInTheDocument();
    expect(screen.getByText('Raise Bet')).toBeInTheDocument();
    expect(screen.getByText('Row Count')).toBeInTheDocument();
    expect(screen.getByText('Bet Strategy')).toBeInTheDocument();
    expect(screen.getByText('Prediction Logic')).toBeInTheDocument();
    expect(screen.getByText('Reset After Betting Hands')).toBeInTheDocument();
  });
  
  it('displays current values correctly', () => {
    render(<BetSettings {...mockProps} />);
    
    // Get input elements and check their values
    const baseBetInput = screen.getByLabelText('Base Bet') as HTMLInputElement;
    const raiseBetInput = screen.getByLabelText('Raise Bet') as HTMLInputElement;
    
    expect(baseBetInput.value).toBe('1000');
    expect(raiseBetInput.value).toBe('1000');
  });
  
  it('calls change handlers when values are updated', () => {
    // Create custom props with isEditing state for testing
    const customProps = {
      ...mockProps,
      // Setting entries to empty array makes the component editable
      entries: []
    };
    
    const { container } = render(<BetSettings {...customProps} />);
    
    // First click the Edit button to enable editing
    const editButton = screen.getByText('Edit');
    fireEvent.click(editButton);
    
    // Now update base bet
    const baseBetInput = screen.getByLabelText('Base Bet') as HTMLInputElement;
    fireEvent.change(baseBetInput, { target: { value: '2000' } });
    
    // Save the changes
    const saveButton = screen.getByText('Save');
    fireEvent.click(saveButton);
    
    expect(mockProps.onBaseBetChange).toHaveBeenCalledWith(2000);
    
    // Reset test state
    vi.clearAllMocks();
    
    // Test editing again for raise bet
    fireEvent.click(screen.getByText('Edit'));
    
    // Update raise bet
    const raiseBetInput = screen.getByLabelText('Raise Bet') as HTMLInputElement;
    fireEvent.change(raiseBetInput, { target: { value: '3000' } });
    
    // Save the changes
    fireEvent.click(screen.getByText('Save'));
    
    expect(mockProps.onRaiseBetChange).toHaveBeenCalledWith(3000);
  });
  
  it('applies horizontal layout when specified', () => {
    render(<BetSettings {...mockProps} layout="horizontal" />);
    
    // Check that horizontal layout class is applied
    const formGrid = screen.getByTestId('bet-settings-form');
    expect(formGrid).toHaveClass('grid-cols-2 md:grid-cols-3 lg:grid-cols-6');
  });
  
  it('applies vertical layout when specified', () => {
    render(<BetSettings {...mockProps} layout="vertical" />);
    
    // Check that vertical layout class is applied
    const formGrid = screen.getByTestId('bet-settings-form');
    expect(formGrid).toHaveClass('grid-cols-1');
  });
  
  it('disables row count selector when entries exist', () => {
    // Create a valid GameEntry object that matches the interface
    const entryWithValidType: GameEntry = { 
      id: 1, 
      columnNumber: 1, 
      rowNumber: 1,
      handValue: 'P', 
      result: 'N', 
      betAmount: 0, 
      cumulativeProfitLoss: 0 
    };
    
    render(<BetSettings {...mockProps} entries={[entryWithValidType]} />);
    
    // Check that row count selector is disabled
    const rowCountSelect = screen.getByRole('combobox', { name: 'Row Count' });
    expect(rowCountSelect).toBeDisabled();
  });
  
  it('enables row count selector when no entries exist', () => {
    render(<BetSettings {...mockProps} entries={[]} />);
    
    // Check that row count selector is enabled
    const rowCountSelect = screen.getByRole('combobox', { name: 'Row Count' });
    expect(rowCountSelect).not.toBeDisabled();
  });
  
  it('hides bet strategy when hideBetStrategy is true', () => {
    render(<BetSettings {...mockProps} hideBetStrategy={true} />);
    expect(screen.queryByText('Bet Strategy')).not.toBeInTheDocument();
  });
  
  it('hides reset betting hands when hideResetBettingHands is true', () => {
    render(<BetSettings {...mockProps} hideResetBettingHands={true} />);
    expect(screen.queryByText('Reset After Betting Hands')).not.toBeInTheDocument();
  });
});
