
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import GameContainer from './GameContainer';
import { GameEntry } from '@/types/game';
import { exportToPDF } from '@/utils/pdf';

// Mock the SimpleGameGrid component
vi.mock('./SimpleGameGrid', () => ({
  default: vi.fn(() => <div data-testid="mock-simple-game-grid">Mock SimpleGameGrid</div>)
}));

// Mock the exportToPDF function
vi.mock('@/utils/pdf', () => ({
  exportToPDF: vi.fn()
}));

describe('GameContainer', () => {
  const mockEntries: GameEntry[] = [
    { id: 1, columnNumber: 1, rowNumber: 1, handValue: 'P', result: 'N', betAmount: 0, cumulativeProfitLoss: 0 },
    { id: 2, columnNumber: 1, rowNumber: 2, handValue: 'P', result: 'W', betAmount: 1000, cumulativeProfitLoss: 1000 },
    { id: 3, columnNumber: 1, rowNumber: 3, handValue: 'B', result: 'L', betAmount: 2000, cumulativeProfitLoss: -1000 },
  ];

  const mockHandSelect = vi.fn();
  
  beforeEach(() => {
    vi.clearAllMocks();
  });
  
  it('renders correctly with entries', () => {
    render(
      <GameContainer 
        entries={mockEntries} 
        rowCount={4} 
        onHandSelect={mockHandSelect}
        predictionLogic="same"
        resetIndices={[]}
      />
    );
    
    expect(screen.getByText('Game Progress')).toBeInTheDocument();
    expect(screen.getByText('Detailed')).toBeInTheDocument();
    expect(screen.getByText('Export')).toBeInTheDocument();
  });
  
  it('toggles between detailed and simple view', () => {
    render(
      <GameContainer 
        entries={mockEntries} 
        rowCount={4} 
        onHandSelect={mockHandSelect}
        predictionLogic="same"
        resetIndices={[]}
      />
    );
    
    // Default is detailed view
    expect(screen.getByText('Detailed')).toBeInTheDocument();
    
    // Toggle to simple view
    fireEvent.click(screen.getByText('Detailed'));
    expect(screen.getByText('Simple')).toBeInTheDocument();
    
    // Toggle back to detailed view
    fireEvent.click(screen.getByText('Simple'));
    expect(screen.getByText('Detailed')).toBeInTheDocument();
  });
  
  it('calls exportToPDF when export button is clicked', () => {
    render(
      <GameContainer 
        entries={mockEntries} 
        rowCount={4} 
        onHandSelect={mockHandSelect}
        predictionLogic="same"
        resetIndices={[]}
      />
    );
    
    fireEvent.click(screen.getByText('Export'));
    expect(exportToPDF).toHaveBeenCalledWith(mockEntries, true);
  });
  
  it('disables export button when no entries', () => {
    render(
      <GameContainer 
        entries={[]} 
        rowCount={4} 
        onHandSelect={mockHandSelect}
        predictionLogic="same"
        resetIndices={[]}
      />
    );
    
    const exportButton = screen.getByText('Export').closest('button');
    expect(exportButton).toBeDisabled();
  });
  
  it('calls onHandSelect when a hand is selected', () => {
    render(
      <GameContainer 
        entries={mockEntries} 
        rowCount={4} 
        onHandSelect={mockHandSelect}
        predictionLogic="same"
        resetIndices={[]}
      />
    );
    
    // Verify the props are passed correctly
    expect(mockHandSelect).not.toHaveBeenCalled();
  });
});
