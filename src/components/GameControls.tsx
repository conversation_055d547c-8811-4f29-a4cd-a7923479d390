import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';
import { HelpCircle, ChevronDown, ChevronUp, Undo, RotateCcw, AlertTriangle, ThumbsUp, ThumbsDown } from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { PredictionLogicType } from '@/types/game';
import { GameEntry } from '@/types/game';

interface GameControlsProps {
  onPlayerSelect: () => void;
  onBankerSelect: () => void;
  onUndo: () => void;
  onReset: () => void;
  disabled?: boolean;
  nextBetAmount?: number;
  betStrategy?: 'loss' | 'win';
  predictionLogic?: PredictionLogicType;
  mirrorPrediction?: 'P' | 'B' | null;
  calculationDetails?: {
    baseBet: number;
    sameRowPreviousLoss: number;
    previousColumnLastLoss: number;
    previousLossInColumn: number;
    raiseBet: number;
    previousColumnRow1Loss: number;
    lossCount: number;
    // New properties for enhanced Same Recovery logic
    hasConsecutiveLosses?: boolean;
    isPreviousNoBetLoss?: boolean;
    inSeedRecovery?: boolean;
    seedRecoveryDetails?: {
      totalLossAmount: number;
      lossCount: number;
      recoveryBetAmount: number;
    };
    hasConsecutiveWins?: boolean;
    srpcValue?: number;
  };
  recoveryModeActive?: boolean;
  recoveryModeDetails?: {
    columnLossAmount: number;
    rowCount: number;
    baseBet: number;
  };
  entries?: GameEntry[];
  rowCount?: number;
  willResetOnWin?: boolean;
}

const GameControls = ({ 
  onPlayerSelect, 
  onBankerSelect, 
  onUndo, 
  onReset, 
  disabled = false,
  nextBetAmount = 0,
  betStrategy = 'loss',
  predictionLogic = 'same',
  mirrorPrediction = null,
  calculationDetails = {
    baseBet: 0,
    sameRowPreviousLoss: 0,
    previousColumnLastLoss: 0,
    previousLossInColumn: 0,
    raiseBet: 0,
    previousColumnRow1Loss: 0,
    lossCount: 0
  },
  recoveryModeActive = false,
  recoveryModeDetails = {
    columnLossAmount: 0,
    rowCount: 0,
    baseBet: 0
  },
  entries = [],
  rowCount = 3,
  willResetOnWin = false
}: GameControlsProps) => {
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    console.log('GameControls calculationDetails received:', calculationDetails);
    console.log('GameControls predictionLogic received:', predictionLogic);
    if (recoveryModeActive) {
      console.log('Recovery mode details:', recoveryModeDetails);
    }
  }, [calculationDetails, recoveryModeActive, recoveryModeDetails, predictionLogic]);

  const handleSelection = (type: 'P' | 'B') => {
    if (disabled) return;
    if (type === 'P') {
      onPlayerSelect();
    } else {
      onBankerSelect();
    }
  };

  const getResultLabel = () => betStrategy === 'loss' ? 'Loss' : 'Win';

  const handleReset = () => {
    console.log('Reset button clicked');
    onReset();
  };

  const handleUndo = () => {
    console.log('Undo button clicked');
    onUndo();
  };

  const isMirrorLogic = () => false;
  const isSameLogic = () => predictionLogic === 'same';
  const isSameNoRecoveryLogic = () => predictionLogic === 'same-no-recovery';
  const isSameRecoveryLogic = () => predictionLogic === 'same-recovery';
  const isExtendLogic = () => predictionLogic === 'extend';
  const isExtendResetLogic = () => predictionLogic === 'extend-reset';
  const isExtendPatternChangeLogic = () => predictionLogic === 'extend-pattern-change';
  const isExtend211Logic = () => predictionLogic === 'extend-2-1-1';
  const isExtend211RecoveryLogic = () => predictionLogic === 'extend-2-1-1-recovery';
  const isAnyExtendLogic = () => isExtendLogic() || isExtendResetLogic() || isExtendPatternChangeLogic() || isExtend211Logic() || isExtend211RecoveryLogic();
  const isWinLossLogic = () => predictionLogic === 'win-loss';
  const isWinLossPrevRowsLogic = () => predictionLogic === 'win-loss-prev-rows';

  const isInRecoveryMode = () => {
    if (!isExtend211RecoveryLogic() || entries.length === 0) return false;
    
    // Find the last recovery mode start
    const lastRecoveryStartIndex = entries.findIndex(entry => entry.recoveryModeStart === true);
    if (lastRecoveryStartIndex === -1) return false;
    
    // Find the last recovery mode end after the start
    const entriesAfterStart = entries.slice(lastRecoveryStartIndex);
    const lastRecoveryEndIndex = entriesAfterStart.findIndex(entry => entry.recoveryModeEnd === true);
    
    // If we found a start but no end, we're in recovery mode
    return lastRecoveryEndIndex === -1;
  };

  const getRecoveryModeDetails = () => {
    if (!isExtend211RecoveryLogic() || entries.length === 0) return null;
    
    const inRecoveryMode = isInRecoveryMode();
    if (!inRecoveryMode) return null;
    
    // Find the last recovery mode start
    let recoveryStartIndex = -1;
    for (let i = entries.length - 1; i >= 0; i--) {
      if (entries[i].recoveryModeStart === true) {
        recoveryStartIndex = i;
        break;
      }
    }
    
    if (recoveryStartIndex === -1) return null;
    
    // Get the 3 losses that triggered recovery mode
    const initialLosses: GameEntry[] = [];
    for (let i = recoveryStartIndex; i >= 0; i--) {
      if (entries[i].result === 'L' && !entries[i].isPatternCell) {
        initialLosses.push(entries[i]);
        if (initialLosses.length === 3) break;
      }
    }
    
    // Get losses that occurred after recovery mode started
    const lossesAfterRecovery: GameEntry[] = [];
    for (let i = entries.length - 1; i > recoveryStartIndex; i--) {
      if (entries[i].result === 'L' && !entries[i].isPatternCell) {
        lossesAfterRecovery.push(entries[i]);
      }
    }
    
    // Calculate recovery bet amount based on the number of losses after recovery mode started
    const raiseBet = calculationDetails?.raiseBet || 0;
    let recoveryBetAmount = 0;
    let sumOfLosses = 0;
    let lossesUsed: GameEntry[] = [];
    
    if (lossesAfterRecovery.length >= 2) {
      // If we have 2 or more losses after recovery mode started, use the sum of the last 2 losses
      const lastTwoLosses = lossesAfterRecovery.slice(0, 2);
      // Calculate sum of the last two losses + RBA*2 for each loss
      sumOfLosses = lastTwoLosses.reduce((sum, entry) => sum + entry.betAmount, 0) + (raiseBet * 2 * 2);
      recoveryBetAmount = Math.floor(sumOfLosses / 2 / 100) * 100;
      lossesUsed = lastTwoLosses;
    } else if (lossesAfterRecovery.length === 1) {
      // If we have 1 loss after recovery mode started, use that loss
      const lastLoss = lossesAfterRecovery[0];
      // Calculate sum of the last loss + RBA*2
      sumOfLosses = lastLoss.betAmount + (raiseBet * 2);
      recoveryBetAmount = Math.floor(sumOfLosses / 2 / 100) * 100;
      lossesUsed = [lastLoss];
    } else {
      // If we have no losses after recovery mode started, use the initial 3 losses
      // Calculate sum of the initial 3 losses + RBA*2 for each loss
      sumOfLosses = initialLosses.reduce((sum, entry) => sum + entry.betAmount, 0) + (raiseBet * 2 * 3);
      recoveryBetAmount = Math.floor(sumOfLosses / 2 / 100) * 100;
      lossesUsed = initialLosses;
    }
    
    // Count consecutive wins in recovery mode
    let consecutiveWins = 0;
    for (let i = entries.length - 1; i > recoveryStartIndex; i--) {
      if (entries[i].result === 'W' && !entries[i].isPatternCell) {
        consecutiveWins++;
      } else if (entries[i].result !== 'N') {
        break;
      }
    }
    
    return {
      recoveryBetAmount,
      sumOfLosses,
      consecutiveWins,
      winsNeededToExit: 2 - consecutiveWins,
      lossesAfterRecovery: lossesAfterRecovery.length,
      lossesUsedCount: lossesUsed.length,
      lossesUsed: lossesUsed.map(entry => entry.betAmount)
    };
  };

  const getCurrentPosition = () => {
    if (entries?.length === 0) {
      return { columnNumber: 1, rowNumber: 1 };
    }
    
    const lastEntry = entries[entries.length - 1];
    let nextColumn = lastEntry.columnNumber;
    let nextRow = lastEntry.rowNumber + 1;
    
    if (nextRow > rowCount) {
      nextColumn++;
      nextRow = 1;
    }
    
    return { columnNumber: nextColumn, rowNumber: nextRow };
  };

  const isRowCountVisible = (
    predictionLogic !== 'win-loss' && 
    predictionLogic !== 'win-loss-prev-rows'
  );

  const getButtonLabels = () => {
    if (isWinLossLogic() || isWinLossPrevRowsLogic()) {
      return {
        player: "Win",
        banker: "Loss",
        playerIcon: <ThumbsUp className="h-4 w-4 mr-1" />,
        bankerIcon: <ThumbsDown className="h-4 w-4 mr-1" />
      };
    }
    
    return {
      player: "Player",
      banker: "Banker",
      playerIcon: null,
      bankerIcon: null
    };
  };

  const buttonLabels = getButtonLabels();

  const renderCalculationTable = () => {
    if (isWinLossLogic()) {
      // Win-Loss logic table rendering
      return (
        <div className="rounded-lg border overflow-x-auto mb-2">
          <Table>
            <TableHeader>
              <TableRow className="hover:bg-transparent">
                <TableHead className="text-center text-white bg-blue-600 px-1 md:px-4 text-xs md:text-sm">
                  Bet Strategy
                  <Tooltip>
                    <TooltipTrigger className="ml-1">
                      <HelpCircle className="h-3 w-3 inline-block" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Win/Loss strategy</p>
                    </TooltipContent>
                  </Tooltip>
                </TableHead>
                <TableHead className="text-center text-white bg-blue-600 px-1 md:px-4 text-xs md:text-sm">
                  Losses
                  <Tooltip>
                    <TooltipTrigger className="ml-1">
                      <HelpCircle className="h-3 w-3 inline-block" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Total number of losses</p>
                    </TooltipContent>
                  </Tooltip>
                </TableHead>
                <TableHead className="text-center font-bold px-1 md:px-4 text-xs md:text-sm">Next Bet</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow className="hover:bg-transparent">
                <TableCell className="text-center font-medium text-xs md:text-sm px-1 md:px-4">
                  {betStrategy === 'loss' ? 'Loss' : 'Win'}
                </TableCell>
                <TableCell className="text-center font-medium text-xs md:text-sm px-1 md:px-4">
                  {calculationDetails.lossCount}
                </TableCell>
                <TableCell className="text-center font-bold text-blue-600 text-xs md:text-sm px-1 md:px-4">
                  ${nextBetAmount.toLocaleString()}
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>
      );
    }
    
    if (isSameNoRecoveryLogic() || isSameRecoveryLogic()) {
      // For Same-Recovery logic, check for the different scenarios
      if (isSameRecoveryLogic()) {
        // Check for consecutive losses
        if (calculationDetails.hasConsecutiveLosses) {
          return (
            <div className="rounded-lg border bg-yellow-50 dark:bg-yellow-950/20 overflow-x-auto mb-2">
              <Table>
                <TableHeader>
                  <TableRow className="hover:bg-transparent">
                    <TableHead className="text-center text-white bg-yellow-600 px-1 md:px-4 text-xs md:text-sm">
                      Status
                      <Tooltip>
                        <TooltipTrigger className="ml-1">
                          <HelpCircle className="h-3 w-3 inline-block" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Two consecutive losses detected</p>
                        </TooltipContent>
                      </Tooltip>
                    </TableHead>
                    <TableHead className="text-center text-white bg-yellow-600 px-1 md:px-4 text-xs md:text-sm">
                      Consecutive Losses
                      <Tooltip>
                        <TooltipTrigger className="ml-1">
                          <HelpCircle className="h-3 w-3 inline-block" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Number of consecutive losses</p>
                        </TooltipContent>
                      </Tooltip>
                    </TableHead>
                    <TableHead className="text-center font-bold px-1 md:px-4 text-xs md:text-sm">Next Bet</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow className="hover:bg-transparent">
                    <TableCell className="text-center font-medium text-xs md:text-sm px-1 md:px-4">
                      <Badge variant="outline" className="bg-yellow-100 text-yellow-800">No Bet</Badge>
                    </TableCell>
                    <TableCell className="text-center font-medium text-xs md:text-sm px-1 md:px-4">
                      {calculationDetails.lossCount}
                    </TableCell>
                    <TableCell className="text-center font-bold text-yellow-600 text-xs md:text-sm px-1 md:px-4">
                      $0
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>
          );
        }
        
        // Check for No Bet loss
        if (calculationDetails.isPreviousNoBetLoss) {
          return (
            <div className="rounded-lg border bg-yellow-50 dark:bg-yellow-950/20 overflow-x-auto mb-2">
              <Table>
                <TableHeader>
                  <TableRow className="hover:bg-transparent">
                    <TableHead className="text-center text-white bg-yellow-600 px-1 md:px-4 text-xs md:text-sm">
                      Status
                      <Tooltip>
                        <TooltipTrigger className="ml-1">
                          <HelpCircle className="h-3 w-3 inline-block" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Previous entry was a No Bet loss</p>
                        </TooltipContent>
                      </Tooltip>
                    </TableHead>
                    <TableHead className="text-center font-bold px-1 md:px-4 text-xs md:text-sm">Next Bet</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow className="hover:bg-transparent">
                    <TableCell className="text-center font-medium text-xs md:text-sm px-1 md:px-4">
                      <Badge variant="outline" className="bg-yellow-100 text-yellow-800">No Bet Continued</Badge>
                    </TableCell>
                    <TableCell className="text-center font-bold text-yellow-600 text-xs md:text-sm px-1 md:px-4">
                      $0
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>
          );
        }
        
        // Check for Seed Recovery mode
        if (calculationDetails.inSeedRecovery && calculationDetails.seedRecoveryDetails) {
          return (
            <div className="rounded-lg border bg-green-50 dark:bg-green-950/20 overflow-x-auto mb-2">
              <Table>
                <TableHeader>
                  <TableRow className="hover:bg-transparent">
                    <TableHead className="text-center text-white bg-green-600 px-1 md:px-4 text-xs md:text-sm">
                      Status
                      <Tooltip>
                        <TooltipTrigger className="ml-1">
                          <HelpCircle className="h-3 w-3 inline-block" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Recovery after No Bet win (Seed)</p>
                        </TooltipContent>
                      </Tooltip>
                    </TableHead>
                    <TableHead className="text-center text-white bg-green-600 px-1 md:px-4 text-xs md:text-sm">
                      Total Losses
                      <Tooltip>
                        <TooltipTrigger className="ml-1">
                          <HelpCircle className="h-3 w-3 inline-block" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Total loss amount before seed win</p>
                        </TooltipContent>
                      </Tooltip>
                    </TableHead>
                    <TableHead className="text-center text-white bg-green-600 px-1 md:px-4 text-xs md:text-sm">
                      Loss Count
                      <Tooltip>
                        <TooltipTrigger className="ml-1">
                          <HelpCircle className="h-3 w-3 inline-block" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Number of losses before seed win</p>
                        </TooltipContent>
                      </Tooltip>
                    </TableHead>
                    <TableHead className="text-center text-white bg-green-600 px-1 md:px-4 text-xs md:text-sm">
                      SRPC
                      <Tooltip>
                        <TooltipTrigger className="ml-1">
                          <HelpCircle className="h-3 w-3 inline-block" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Same Row Previous Column Loss</p>
                        </TooltipContent>
                      </Tooltip>
                    </TableHead>
                    <TableHead className="text-center font-bold px-1 md:px-4 text-xs md:text-sm">Recovery Bet</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow className="hover:bg-transparent">
                    <TableCell className="text-center font-medium text-xs md:text-sm px-1 md:px-4">
                      <Badge variant="outline" className="bg-green-100 text-green-800">Seed Recovery</Badge>
                    </TableCell>
                    <TableCell className="text-center font-medium text-xs md:text-sm px-1 md:px-4">
                      ${calculationDetails.seedRecoveryDetails.totalLossAmount.toLocaleString()}
                    </TableCell>
                    <TableCell className="text-center font-medium text-xs md:text-sm px-1 md:px-4">
                      {calculationDetails.seedRecoveryDetails.lossCount}
                    </TableCell>
                    <TableCell className="text-center font-medium text-xs md:text-sm px-1 md:px-4">
                      {calculationDetails.srpcValue > 0 ? 
                        `${calculationDetails.srpcValue} + ${calculationDetails.raiseBet} = ${calculationDetails.srpcValue + calculationDetails.raiseBet}` : 
                        '0'}
                    </TableCell>
                    <TableCell className="text-center font-bold text-green-600 text-xs md:text-sm px-1 md:px-4">
                      ${nextBetAmount.toLocaleString()}
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>
          );
        }
        
        // Check for consecutive wins
        if (calculationDetails.hasConsecutiveWins) {
          return (
            <div className="rounded-lg border bg-blue-50 dark:bg-blue-950/20 overflow-x-auto mb-2">
              <Table>
                <TableHeader>
                  <TableRow className="hover:bg-transparent">
                    <TableHead className="text-center text-white bg-blue-600 px-1 md:px-4 text-xs md:text-sm">
                      Status
                      <Tooltip>
                        <TooltipTrigger className="ml-1">
                          <HelpCircle className="h-3 w-3 inline-block" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Two consecutive wins detected</p>
                        </TooltipContent>
                      </Tooltip>
                    </TableHead>
                    <TableHead className="text-center text-white bg-blue-600 px-1 md:px-4 text-xs md:text-sm">
                      Base Bet
                      <Tooltip>
                        <TooltipTrigger className="ml-1">
                          <HelpCircle className="h-3 w-3 inline-block" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Reset to base bet amount</p>
                        </TooltipContent>
                      </Tooltip>
                    </TableHead>
                    <TableHead className="text-center font-bold px-1 md:px-4 text-xs md:text-sm">Next Bet</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow className="hover:bg-transparent">
                    <TableCell className="text-center font-medium text-xs md:text-sm px-1 md:px-4">
                      <Badge variant="outline" className="bg-blue-100 text-blue-800">Reset</Badge>
                    </TableCell>
                    <TableCell className="text-center font-medium text-xs md:text-sm px-1 md:px-4">
                      ${calculationDetails.baseBet.toLocaleString()}
                    </TableCell>
                    <TableCell className="text-center font-bold text-blue-600 text-xs md:text-sm px-1 md:px-4">
                      ${nextBetAmount.toLocaleString()}
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>
          );
        }
        
        // Check if we're in regular recovery mode
        if (recoveryModeActive) {
          return (
          <div className="rounded-lg border bg-red-50 dark:bg-red-950/20 overflow-x-auto mb-2">
            <Table>
              <TableHeader>
                <TableRow className="hover:bg-transparent">
                  <TableHead className="text-center text-white bg-red-600 px-1 md:px-4 text-xs md:text-sm">
                    Recovery Mode
                    <Tooltip>
                      <TooltipTrigger className="ml-1">
                        <HelpCircle className="h-3 w-3 inline-block" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Recovering from consecutive losses</p>
                      </TooltipContent>
                    </Tooltip>
                  </TableHead>
                  <TableHead className="text-center text-white bg-red-600 px-1 md:px-4 text-xs md:text-sm">
                    Total Loss
                    <Tooltip>
                      <TooltipTrigger className="ml-1">
                        <HelpCircle className="h-3 w-3 inline-block" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Total loss amount being recovered</p>
                      </TooltipContent>
                    </Tooltip>
                  </TableHead>
                  <TableHead className="text-center text-white bg-red-600 px-1 md:px-4 text-xs md:text-sm">
                    Wins Needed
                    <Tooltip>
                      <TooltipTrigger className="ml-1">
                        <HelpCircle className="h-3 w-3 inline-block" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Consecutive wins needed to exit recovery mode</p>
                      </TooltipContent>
                    </Tooltip>
                  </TableHead>
                  <TableHead className="text-center font-bold px-1 md:px-4 text-xs md:text-sm">Recovery Bet</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow className="hover:bg-transparent">
                  <TableCell className="text-center font-medium text-xs md:text-sm px-1 md:px-4">
                    <Badge variant="destructive">Active</Badge>
                  </TableCell>
                  <TableCell className="text-center font-medium text-xs md:text-sm px-1 md:px-4">
                    ${recoveryModeDetails.columnLossAmount.toLocaleString()}
                  </TableCell>
                  <TableCell className="text-center font-medium text-xs md:text-sm px-1 md:px-4">
                    2
                  </TableCell>
                  <TableCell className="text-center font-bold text-red-600 text-xs md:text-sm px-1 md:px-4">
                    ${nextBetAmount.toLocaleString()}
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
        );
      }
      
      }
      
      // For Same-No-Recovery or Same-Recovery (not in any special mode), show standard table
      return (
        <div className="rounded-lg border bg-blue-50 dark:bg-blue-950/20 overflow-x-auto mb-2">
          <Table>
            <TableHeader>
              <TableRow className="hover:bg-transparent">
                <TableHead className="text-center text-white bg-blue-600 px-1 md:px-4 text-xs md:text-sm">
                  SRPC + RBA
                  <Tooltip>
                    <TooltipTrigger className="ml-1">
                      <HelpCircle className="h-3 w-3 inline-block" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Same Row Previous Column Loss + Raise Bet Amount</p>
                    </TooltipContent>
                  </Tooltip>
                </TableHead>
                <TableHead className="text-center text-white bg-blue-600 px-1 md:px-4 text-xs md:text-sm">
                  PCLRL + RBA
                  <Tooltip>
                    <TooltipTrigger className="ml-1">
                      <HelpCircle className="h-3 w-3 inline-block" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Previous Column Last Row Loss + Raise Bet Amount</p>
                    </TooltipContent>
                  </Tooltip>
                </TableHead>
                <TableHead className="text-center text-white bg-blue-600 px-1 md:px-4 text-xs md:text-sm">
                  PLSC + RBA
                  <Tooltip>
                    <TooltipTrigger className="ml-1">
                      <HelpCircle className="h-3 w-3 inline-block" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Previous Loss in Same Column + Raise Bet Amount</p>
                    </TooltipContent>
                  </Tooltip>
                </TableHead>
                <TableHead className="text-center font-bold px-1 md:px-4 text-xs md:text-sm">Next Bet</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow className="hover:bg-transparent">
                <TableCell className="text-center font-medium text-xs md:text-sm px-1 md:px-4">
                  {calculationDetails.sameRowPreviousLoss > 0 ? 
                    `${calculationDetails.sameRowPreviousLoss} + ${calculationDetails.raiseBet} = ${calculationDetails.sameRowPreviousLoss + calculationDetails.raiseBet}` : 
                    '0'}
                </TableCell>
                <TableCell className="text-center font-medium text-xs md:text-sm px-1 md:px-4">
                  {calculationDetails.previousColumnLastLoss > 0 ? 
                    `${calculationDetails.previousColumnLastLoss} + ${calculationDetails.raiseBet} = ${calculationDetails.previousColumnLastLoss + calculationDetails.raiseBet}` : 
                    '0'}
                </TableCell>
                <TableCell className="text-center font-medium text-xs md:text-sm px-1 md:px-4">
                  {calculationDetails.previousLossInColumn > 0 ? 
                    `${calculationDetails.previousLossInColumn} + ${calculationDetails.raiseBet} = ${calculationDetails.previousLossInColumn + calculationDetails.raiseBet}` : 
                    '0'}
                </TableCell>
                <TableCell className="text-center font-bold text-blue-600 text-xs md:text-sm px-1 md:px-4">
                  ${nextBetAmount.toLocaleString()}
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>
      );
    }
    
    if (isWinLossPrevRowsLogic()) {
      // Win-Loss-Prev-Rows logic table rendering
      return (
        <div className="rounded-lg border overflow-x-auto mb-2">
          <Table>
            <TableHeader>
              <TableRow className="hover:bg-transparent">
                <TableHead className="text-center text-white bg-[#221F26] px-1 md:px-4 text-xs md:text-sm">
                  BBA
                  <Tooltip>
                    <TooltipTrigger className="ml-1">
                      <HelpCircle className="h-3 w-3 inline-block" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Base Bet Amount</p>
                    </TooltipContent>
                  </Tooltip>
                </TableHead>
                <TableHead className="text-center text-white bg-[#221F26] px-1 md:px-4 text-xs md:text-sm">
                  SRPC
                  <Tooltip>
                    <TooltipTrigger className="ml-1">
                      <HelpCircle className="h-3 w-3 inline-block" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Same Row Previous Column Loss</p>
                    </TooltipContent>
                  </Tooltip>
                </TableHead>
                <TableHead className="text-center text-white bg-[#221F26] px-1 md:px-4 text-xs md:text-sm">
                  PLSC
                  <Tooltip>
                    <TooltipTrigger className="ml-1">
                      <HelpCircle className="h-3 w-3 inline-block" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Previous Loss in Same Column</p>
                    </TooltipContent>
                  </Tooltip>
                </TableHead>
                <TableHead className="text-center text-white bg-[#221F26] px-1 md:px-4 text-xs md:text-sm">
                  PCLRL
                  <Tooltip>
                    <TooltipTrigger className="ml-1">
                      <HelpCircle className="h-3 w-3 inline-block" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Previous Column's Last Row Loss</p>
                    </TooltipContent>
                  </Tooltip>
                </TableHead>
                <TableHead className="text-center text-white bg-[#221F26] px-1 md:px-4 text-xs md:text-sm">
                  RBA
                  <Tooltip>
                    <TooltipTrigger className="ml-1">
                      <HelpCircle className="h-3 w-3 inline-block" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Raise Bet Amount</p>
                    </TooltipContent>
                  </Tooltip>
                </TableHead>
                <TableHead className="text-center font-bold px-1 md:px-4 text-xs md:text-sm">Next Bet</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow className="hover:bg-transparent">
                <TableCell className="text-center font-medium text-xs md:text-sm px-1 md:px-4">
                  {calculationDetails.baseBet.toLocaleString()}
                </TableCell>
                <TableCell className="text-center font-medium text-xs md:text-sm px-1 md:px-4">
                  {calculationDetails.sameRowPreviousLoss > 0 
                    ? calculationDetails.sameRowPreviousLoss.toLocaleString()
                    : 'NA'}
                </TableCell>
                <TableCell className="text-center font-medium text-xs md:text-sm px-1 md:px-4">
                  {calculationDetails.previousLossInColumn > 0 
                    ? calculationDetails.previousLossInColumn.toLocaleString()
                    : 'NA'}
                </TableCell>
                <TableCell className="text-center font-medium text-xs md:text-sm px-1 md:px-4">
                  {calculationDetails.previousColumnLastLoss > 0 
                    ? calculationDetails.previousColumnLastLoss.toLocaleString()
                    : 'NA'}
                </TableCell>
                <TableCell className="text-center font-medium text-xs md:text-sm px-1 md:px-4">
                  {calculationDetails.raiseBet.toLocaleString()}
                </TableCell>
                <TableCell className="text-center font-bold text-primary text-xs md:text-sm px-1 md:px-4">
                  {nextBetAmount.toLocaleString()}
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>
      );
    }
    
    // Add other logic types here
    
    return null;
  };

  return (
    <Card className="shadow-lg border border-gray-200 dark:border-gray-700 bg-white/95 dark:bg-gray-800/95 backdrop-blur">
      <CardContent className="p-3 sm:p-4">
        <div className="space-y-4">
          <div className="flex flex-col">
            <p className="text-sm font-medium text-muted-foreground">Next Bet</p>
            <div className="flex items-center">
              <p className="text-3xl font-bold text-blue-600">
                ${nextBetAmount.toLocaleString()}
              </p>
              <Button
                variant="ghost"
                size="sm"
                className="p-0 h-8 w-8 hover:bg-muted ml-1"
                onClick={() => setShowDetails(!showDetails)}
              >
                {showDetails ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
              </Button>
            </div>
          </div>
          
          {showDetails && (
            <TooltipProvider>
              {renderCalculationTable()}
            </TooltipProvider>
          )}

          <div className="flex gap-3 mb-4">
            <Button
              onClick={() => handleSelection('P')}
              disabled={disabled}
              size="lg"
              className="flex-1 h-16 text-xl font-bold tracking-wider hover:scale-[1.02] transition-all duration-200 bg-[#0d0b85] hover:bg-[#0d0b85]/90 shadow-md rounded-lg"
            >
              Player
            </Button>
            <Button
              onClick={() => handleSelection('B')}
              disabled={disabled}
              size="lg"
              className="flex-1 h-16 text-xl font-bold tracking-wider hover:scale-[1.02] transition-all duration-200 bg-[#ea384c] hover:bg-[#ea384c]/90 shadow-md rounded-lg"
            >
              Banker
            </Button>
          </div>

          <div className="flex justify-center gap-4">
            <Button
              onClick={handleUndo}
              variant="outline"
              className="w-24 h-10 gap-1 text-sm rounded-lg"
            >
              <Undo className="h-4 w-4" />
              Undo
            </Button>
            
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button
                  className="w-24 h-10 gap-1 bg-[#F97316] hover:bg-[#F97316]/90 text-sm rounded-lg"
                >
                  <RotateCcw className="h-4 w-4" />
                  Reset
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent className="w-11/12 max-w-md mx-auto">
                <AlertDialogHeader>
                  <AlertDialogTitle>Are you sure you want to reset?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This action will clear all your current game data and cannot be undone.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleReset}
                    className="bg-[#F97316] hover:bg-[#F97316]/90"
                  >
                    Reset
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default GameControls;
