
import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import GameGrid from './GameGrid';
import { GameEntry } from '@/types/game';

// Mock TooltipProvider since we're not testing tooltip functionality
vi.mock('@/components/ui/tooltip', () => ({
  TooltipProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  Tooltip: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  TooltipTrigger: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  TooltipContent: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

describe('GameGrid', () => {
  const mockHandleSelect = vi.fn();
  
  const sampleEntries: GameEntry[] = [
    { id: 1, columnNumber: 1, rowNumber: 1, handValue: 'P', result: 'N', betAmount: 0, cumulativeProfitLoss: 0 },
    { id: 2, columnNumber: 1, rowNumber: 2, handValue: 'P', result: 'W', betAmount: 1000, cumulativeProfitLoss: 1000 },
    { id: 3, columnNumber: 1, rowNumber: 3, handValue: 'B', result: 'L', betAmount: 2000, cumulativeProfitLoss: -1000 },
    { id: 4, columnNumber: 1, rowNumber: 4, handValue: 'P', result: 'W', betAmount: 1000, cumulativeProfitLoss: 0 },
    { id: 5, columnNumber: 2, rowNumber: 1, handValue: 'B', result: 'N', betAmount: 0, cumulativeProfitLoss: 0, recoveryMode: 'start' },
  ];
  
  const resetIndices = [3];
  
  it('renders the correct number of columns and rows', () => {
    render(
      <GameGrid 
        entries={sampleEntries} 
        rowCount={4} 
        columnCount={2} 
        onHandSelect={mockHandleSelect}
        resetIndices={resetIndices}
      />
    );
    
    // Check for column headers
    expect(screen.getByText('Column 1')).toBeInTheDocument();
    expect(screen.getByText('Column 2')).toBeInTheDocument();
    
    // Check for all cells (2 columns x 4 rows = 8 cells)
    const cells = screen.getAllByRole('cell');
    expect(cells.length).toBe(8);
  });
  
  it('displays game entries correctly', () => {
    render(
      <GameGrid 
        entries={sampleEntries} 
        rowCount={4} 
        columnCount={2} 
        onHandSelect={mockHandleSelect}
        resetIndices={resetIndices}
      />
    );
    
    // Check first row entries - use getAllByText since there are multiple matches
    const pElements = screen.getAllByText('P');
    const bElements = screen.getAllByText('B');
    expect(pElements.length).toBeGreaterThan(0);
    expect(bElements.length).toBeGreaterThan(0);
    
    // Check bet amounts are displayed - use getAllByText since there are multiple matches
    expect(screen.getAllByText('$1,000')[0]).toBeInTheDocument();
    expect(screen.getByText('$2,000')).toBeInTheDocument();
    
    // Check for "No Bet" text
    expect(screen.getAllByText('No Bet').length).toBeGreaterThan(0);
  });
  
  it('calls onHandSelect when clicking on first row', () => {
    render(
      <GameGrid 
        entries={sampleEntries} 
        rowCount={4} 
        columnCount={2} 
        onHandSelect={mockHandleSelect}
        resetIndices={resetIndices}
      />
    );
    
    // Clear any previous mock calls
    mockHandleSelect.mockClear();
    
    // Find and click the first cell (column 1, row 1)
    const cells = screen.getAllByRole('cell');
    fireEvent.click(cells[0]);
    
    // Check if onHandSelect was called with correct arguments
    expect(mockHandleSelect).toHaveBeenCalledWith(1, 1, 'B');
  });
  
  it('does not call onHandSelect when clicking on rows other than first row', () => {
    render(
      <GameGrid 
        entries={sampleEntries} 
        rowCount={4} 
        columnCount={2} 
        onHandSelect={mockHandleSelect}
        resetIndices={resetIndices}
      />
    );
    
    // Clear any previous mock calls
    mockHandleSelect.mockClear();
    
    // Find and click a cell in the second row (index 2)
    const cells = screen.getAllByRole('cell');
    // Click on the second row first column cell - index 2 (third cell)
    fireEvent.click(cells[2]);
    
    // Check that onHandSelect was not called
    expect(mockHandleSelect).not.toHaveBeenCalled();
  });
});
