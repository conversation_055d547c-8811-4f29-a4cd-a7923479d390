
import React from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { GameEntry, PredictionLogicType, ExtensionFactor } from '@/types/game';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { useGameGrid } from '@/hooks/useGameGrid';
import { ScrollArea } from '@/components/ui/scroll-area';
import { safeFormatNumber } from '@/utils/formatting';

interface GameGridProps {
  entries: GameEntry[];
  rowCount: number;
  columnCount: number;
  onHandSelect: (columnNumber: number, rowNumber: number, value: 'P' | 'B') => void;
  resetIndices?: number[];
  predictionLogic?: PredictionLogicType;
  extensionFactor?: ExtensionFactor;
  baseRowCount?: number;
}

const GameGrid = ({ 
  entries, 
  rowCount, 
  columnCount, 
  onHandSelect, 
  resetIndices = [],
  predictionLogic = 'same',
  extensionFactor = 2,
  baseRowCount = 3
}: GameGridProps) => {
  const columns = Array.from({ length: columnCount }, (_, i) => i + 1);
  const rows = Array.from({ length: rowCount }, (_, i) => i + 1);
  
  const {
    getEntryForPosition,
    isPatternCell,
    getPatternSection,
    getCellClassName,
    isResetPoint,
    getRecoveryStatus,
    getBetAmount,
    isWinLossMode,
    isExtendLogic,
  } = useGameGrid(
    entries,
    rowCount,
    predictionLogic,
    extensionFactor,
    baseRowCount
  );

  const handleCellClick = (columnNumber: number, rowNumber: number) => {
    if (isWinLossMode || rowNumber === 1 || (isExtendLogic() && isPatternCell(rowNumber))) {
      const entry = getEntryForPosition(columnNumber, rowNumber);
      const currentValue = entry?.handValue;
      const newValue = currentValue === 'P' ? 'B' : 'P';
      
      if (isExtendLogic()) {
        console.log('GameGrid.handleCellClick:', { columnNumber, rowNumber });
        console.log('Toggle hand value:', { currentValue, newValue });
      }
      
      onHandSelect(columnNumber, rowNumber, newValue);
    }
  };

  const getDisplayValue = (entry?: GameEntry) => {
    if (!entry || !entry.handValue) return '-';
    
    if (isWinLossMode) {
      return entry.handValue === 'P' ? 'W' : 'L';
    }
    
    return entry.handValue;
  };
  
  const getCellStyles = (entry?: GameEntry, row?: number) => {
    if (!entry || !row) return { bg: '', text: 'text-gray-800' };
    
    const isPattern = isPatternCell(row);
    const isFirstRow = row === 1;
    
    // Only color pattern cells or first row cells
    if ((isPattern && entry.handValue) || (isFirstRow && entry.handValue && !isWinLossMode)) {
      return {
        bg: entry.handValue === 'P' ? '#0d0b85' : '#ea384c',
        text: 'text-white'
      };
    }
    
    return {
      bg: '',
      text: 'text-gray-800'
    };
  };

  // Check if we should show reset indicators for this mode
  const shouldShowResetIndicators = predictionLogic === 'extend-reset';

  return (
    <div className="w-full">
      <ScrollArea className="h-full max-h-[70vh]" orientation="horizontal">
        <div className="w-full min-w-max">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="text-center w-10 sticky left-0 bg-background z-10">Row</TableHead>
                {columns.map(column => (
                  <TableHead key={column} className="text-center w-16" data-column={column}>Col {column}</TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {rows.map(row => (
                <TableRow key={row}>
                  <TableCell className="text-center font-medium bg-gray-100 sticky left-0 z-10">{row}</TableCell>
                  {columns.map(column => {
                    const entry = getEntryForPosition(column, row);
                    const styles = getCellStyles(entry, row);
                    const isPattern = isPatternCell(row);
                    
                    // Use isResetPoint function to determine if reset dot should be shown
                    // This will automatically exclude pattern cells
                    const wasReset = shouldShowResetIndicators && isResetPoint(entry);
                    
                    const recoveryStatus = getRecoveryStatus(entry);
                    // Get the correct bet amount for display
                    const displayBetAmount = entry ? getBetAmount(entry) : 0;
                    
                    let patternLabel = "";
                    if (!isWinLossMode) {
                      if (isExtendLogic() && isPattern) {
                        patternLabel = "No Bet";
                      } else if (row === 1) {
                        patternLabel = "No Bet";
                      }
                    }
                    
                    return (
                      <TableCell
                        key={`${column}-${row}`}
                        className={`text-center ${getCellClassName(entry, row)} relative`}
                        onClick={() => handleCellClick(column, row)}
                        style={{
                          cursor: (isWinLossMode || row === 1 || isPattern) ? 'pointer' : 'default',
                          backgroundColor: styles.bg || undefined
                        }}
                      >
                        <div className={`space-y-0.5 ${styles.text}`}>
                          <div className="text-base">{getDisplayValue(entry)}</div>
                          
                          {/* Only show bet amount for non-pattern cells */}
                          {entry && !isPattern && (
                            <div className={`text-2xs ${styles.text === 'text-white' ? 'text-white/80' : 'text-gray-500'}`}>
                              ${safeFormatNumber(displayBetAmount)}
                            </div>
                          )}
                          
                          {/* Show No Bet label for pattern cells and cells with result='N' */}
                          {((entry?.result === 'N' && !isPattern) || isPattern) && !isWinLossMode && (
                            <div className={`text-2xs ${styles.text === 'text-white' ? 'text-white/80' : 'text-gray-500'}`}>
                              {patternLabel || "No Bet"}
                            </div>
                          )}
                        </div>
                        
                        {/* Reset Indicator with Tooltip - Only show for non-pattern cells */}
                        {wasReset && (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <div className={`absolute top-0.5 right-0.5 w-2 h-2 rounded-full ${entry?.resetType === 'profit' ? 'bg-green-600' : 'bg-red-600'}`}></div>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Bet reset to base amount {entry?.resetType === 'profit' ? '(Profit threshold)' : '(Loss threshold)'}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}
                        
                        {/* Recovery Status Indicator with Tooltip */}
                        {recoveryStatus && !isPattern && (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <div className={`absolute ${wasReset ? 'top-3' : 'top-0.5'} ${wasReset ? 'right-0.5' : 'left-0.5'} w-2 h-2 rounded-full ${
                                  recoveryStatus === 'start' ? 'bg-amber-500' : 
                                  recoveryStatus === 'end' ? 'bg-green-500' : 
                                  'bg-purple-500'
                                }`}></div>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>
                                  {recoveryStatus === 'start' ? "Recovery mode started" : 
                                   recoveryStatus === 'end' ? "Recovery mode ended" : 
                                   "Recovery mode active"}
                                </p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}
                      </TableCell>
                    );
                  })}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </ScrollArea>
    </div>
  );
};

export default GameGrid;
