
import React, { ReactNode } from 'react';
import { useIsMobile } from '@/hooks/use-mobile';

interface GameLayoutProps {
  children: ReactNode;
}

const GameLayout = ({ children }: GameLayoutProps) => {
  const isMobile = useIsMobile();
  
  return (
    <div className="container mx-auto px-2 sm:px-4 py-4 sm:py-6 flex flex-col min-h-screen">
      <h1 className="text-2xl sm:text-3xl font-bold text-center mb-4 sm:mb-6 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">WhatNext?</h1>
      
      {/* Main Content Area */}
      <div className={`flex-1 flex flex-col space-y-4 sm:space-y-6 ${isMobile ? "pb-safe-area-bottom" : ""}`}>
        {children}
      </div>
      
      {/* Add viewport meta tag for mobile devices */}
      {isMobile && (
        <style dangerouslySetInnerHTML={{ __html: `
          @viewport {
            width: device-width;
            height: device-height;
            initial-scale: 1.0;
            maximum-scale: 1.0;
            user-scalable: no;
          }
        `}} />
      )}
    </div>
  );
};

export default GameLayout;
