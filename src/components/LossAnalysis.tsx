
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { GameEntry } from '@/types/game';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";

interface LossAnalysisProps {
  entries: GameEntry[];
}

const LossAnalysis = ({ entries }: LossAnalysisProps) => {
  // Calculate loss streaks
  const calculateLossStreaks = () => {
    const streaks: { [key: number]: number } = {};
    let currentStreak = 0;

    // Filter out 'N' results and process only betting hands
    const bettingEntries = entries.filter(entry => entry.result !== 'N');
    
    // Track resets to properly segment streaks
    let lastResetIndex = -1;

    bettingEntries.forEach((entry, index) => {
      // Check if this is a reset point
      if (entry.resetPoint) {
        // End any current streak before a reset point
        if (currentStreak >= 2) {
          streaks[currentStreak] = (streaks[currentStreak] || 0) + 1;
        }
        currentStreak = 0;
        lastResetIndex = index;
        return;
      }
      
      if (entry.result === 'L') {
        currentStreak++;
      } else {
        // When we hit a win, record the streak if it's at least 2 losses
        if (currentStreak >= 2) {
          streaks[currentStreak] = (streaks[currentStreak] || 0) + 1;
        }
        currentStreak = 0;
      }
    });

    // Handle the last streak if it's a loss streak
    if (currentStreak >= 2) {
      streaks[currentStreak] = (streaks[currentStreak] || 0) + 1;
    }

    return Object.entries(streaks)
      .map(([length, occurrences]) => ({
        length: parseInt(length),
        occurrences
      }))
      .sort((a, b) => a.length - b.length);
  };

  const lossStreaks = calculateLossStreaks();

  return (
    <Card className="border-0 shadow-none">
      <CardHeader className="px-0 pt-0">
        <CardTitle className="text-base font-semibold text-primary">Continuous Losses Analysis</CardTitle>
      </CardHeader>
      <CardContent className="px-0 pb-0">
        {lossStreaks.length > 0 ? (
          <Table>
            <TableHeader>
              <TableRow className="bg-muted/50">
                <TableHead className="font-semibold">Loss Streak</TableHead>
                <TableHead className="font-semibold">Occurrences</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {lossStreaks.map((streak, index) => (
                <TableRow key={index} className="hover:bg-muted/50">
                  <TableCell className="font-medium">
                    {streak.length} losses in a row
                  </TableCell>
                  <TableCell>
                    <Badge variant="secondary" className="font-semibold">
                      {streak.occurrences}
                    </Badge>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        ) : (
          <div className="py-4 text-center text-sm text-muted-foreground">
            No continuous losses recorded yet
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default LossAnalysis;
