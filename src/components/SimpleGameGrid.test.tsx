
import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import SimpleGameGrid from './SimpleGameGrid';
import { GameEntry } from '@/types/game';

describe('SimpleGameGrid', () => {
  const mockHandleSelect = vi.fn();
  
  const sampleEntries: GameEntry[] = [
    { id: 1, columnNumber: 1, rowNumber: 1, handValue: 'P', result: 'N', betAmount: 0, cumulativeProfitLoss: 0 },
    { id: 2, columnNumber: 1, rowNumber: 2, handValue: 'P', result: 'W', betAmount: 1000, cumulativeProfitLoss: 1000 },
    { id: 3, columnNumber: 1, rowNumber: 3, handValue: 'B', result: 'L', betAmount: 2000, cumulativeProfitLoss: -1000 },
    { id: 4, columnNumber: 1, rowNumber: 4, handValue: 'P', result: 'W', betAmount: 1000, cumulativeProfitLoss: 0 },
    { id: 5, columnNumber: 2, rowNumber: 1, handValue: 'B', result: 'N', betAmount: 0, cumulativeProfitLoss: 0, recoveryMode: 'start' },
  ];
  
  const resetIndices = [3];
  
  it('renders the correct number of columns and rows', () => {
    render(
      <SimpleGameGrid 
        entries={sampleEntries} 
        rowCount={4} 
        columnCount={2} 
        onHandSelect={mockHandleSelect}
        resetIndices={resetIndices}
      />
    );
    
    // Check for column headers
    expect(screen.getByText('Column 1')).toBeInTheDocument();
    expect(screen.getByText('Column 2')).toBeInTheDocument();
    
    // Check for all cells (2 columns x 4 rows = 8 cells)
    const cells = screen.getAllByRole('cell');
    expect(cells.length).toBe(8);
  });
  
  it('displays game entries correctly', () => {
    render(
      <SimpleGameGrid 
        entries={sampleEntries} 
        rowCount={4} 
        columnCount={2} 
        onHandSelect={mockHandleSelect}
        resetIndices={resetIndices}
      />
    );
    
    // Check first row entries - use getAllByText since there are multiple matches
    const pElements = screen.getAllByText('P');
    const bElements = screen.getAllByText('B');
    expect(pElements.length).toBeGreaterThan(0);
    expect(bElements.length).toBeGreaterThan(0);
    
    // Check bet amounts are displayed
    expect(screen.getAllByText('$1,000').length).toBeGreaterThan(0);
    expect(screen.getByText('$2,000')).toBeInTheDocument();
  });
  
  it('calls onHandSelect when clicking on first row', () => {
    render(
      <SimpleGameGrid 
        entries={sampleEntries} 
        rowCount={4} 
        columnCount={2} 
        onHandSelect={mockHandleSelect}
        resetIndices={resetIndices}
      />
    );
    
    // Clear any previous mock calls
    mockHandleSelect.mockClear();
    
    // Find and click the first cell (column 1, row 1)
    const cells = screen.getAllByRole('cell');
    fireEvent.click(cells[0]);
    
    // Check if onHandSelect was called with correct arguments
    expect(mockHandleSelect).toHaveBeenCalledWith(1, 1, 'B');
  });
  
  it('does not call onHandSelect when clicking on rows other than first row', () => {
    mockHandleSelect.mockClear(); // Clear previous calls
    
    // Fixed the type issue here - making sure handValue is explicitly 'P' | 'B' | null
    const entriesWithNoClickHandler: GameEntry[] = [
      ...sampleEntries,
      { id: 6, columnNumber: 2, rowNumber: 2, handValue: 'P', result: 'W', betAmount: 1000, cumulativeProfitLoss: 1000 },
    ];
    
    render(
      <SimpleGameGrid 
        entries={entriesWithNoClickHandler} 
        rowCount={4} 
        columnCount={2} 
        onHandSelect={mockHandleSelect}
        resetIndices={resetIndices}
      />
    );
    
    // Clear any previous mock calls
    mockHandleSelect.mockClear();
    
    // Find and click a cell in the second row (not the first row)
    const cells = screen.getAllByRole('cell');
    if (cells.length >= 3) {
      fireEvent.click(cells[2]); // This is the second row, not the first row
      // The onClick handler should only run for first row cells
      expect(mockHandleSelect).not.toHaveBeenCalled();
    }
  });
  
  it('correctly displays reset indicators', () => {
    render(
      <SimpleGameGrid 
        entries={sampleEntries} 
        rowCount={4} 
        columnCount={2} 
        onHandSelect={mockHandleSelect}
        resetIndices={resetIndices}
      />
    );
    
    // Check for reset indicators
    const resetIndicator = screen.getByTitle('Bet reset to base amount');
    expect(resetIndicator).toBeInTheDocument();
    expect(resetIndicator.textContent).toBe('R');
  });
  
  it('correctly displays recovery mode indicators', () => {
    render(
      <SimpleGameGrid 
        entries={sampleEntries} 
        rowCount={4} 
        columnCount={2} 
        onHandSelect={mockHandleSelect}
        resetIndices={resetIndices}
      />
    );
    
    // Check for recovery mode indicators
    const recoveryIndicator = screen.getByTitle('Recovery mode started');
    expect(recoveryIndicator).toBeInTheDocument();
    expect(recoveryIndicator.textContent).toBe('RS');
  });
});
