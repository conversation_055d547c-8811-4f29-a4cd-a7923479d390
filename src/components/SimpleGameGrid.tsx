import React from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { GameEntry, PredictionLogicType, ExtensionFactor } from '@/types/game';
import { useGameGrid } from '@/hooks/useGameGrid';
import { ScrollArea } from '@/components/ui/scroll-area';
import { safeFormatNumber } from '@/utils/formatting';

interface SimpleGameGridProps {
  entries: GameEntry[];
  rowCount: number;
  columnCount: number;
  onHandSelect: (columnNumber: number, rowNumber: number, value: 'P' | 'B') => void;
  resetIndices?: number[];
  predictionLogic?: PredictionLogicType;
  extensionFactor?: ExtensionFactor;
  baseRowCount?: number;
}

const SimpleGameGrid = ({ 
  entries, 
  rowCount, 
  columnCount, 
  onHandSelect, 
  resetIndices = [], 
  predictionLogic = 'same',
  extensionFactor = 2,
  baseRowCount = 3
}: SimpleGameGridProps) => {
  const columns = Array.from({ length: columnCount }, (_, i) => i + 1);
  const rows = Array.from({ length: rowCount }, (_, i) => i + 1);

  const {
    getEntryForPosition,
    isPatternCell,
    getPatternSection,
    getCellClassName,
    isResetPoint,
    getRecoveryStatus,
    getBetAmount,
    isWinLossMode,
    isSameNoRecoveryLogic,
    isExtendLogic,
  } = useGameGrid(
    entries,
    rowCount,
    predictionLogic,
    extensionFactor,
    baseRowCount
  );

  const handleCellClick = (columnNumber: number, rowNumber: number) => {
    if (isWinLossMode || rowNumber === 1 || (isExtendLogic() && isPatternCell(rowNumber))) {
      const entry = getEntryForPosition(columnNumber, rowNumber);
      const currentValue = entry?.handValue;
      const newValue = currentValue === 'P' ? 'B' : 'P';
      
      if (isExtendLogic()) {
        console.log(`SimpleGameGrid: Extend/Reset mode cell clicked at Col ${columnNumber}, Row ${rowNumber}`);
        console.log(`SimpleGameGrid: Current value: ${currentValue}, New value: ${newValue}`);
      } else if (isWinLossMode) {
        console.log(`SimpleGameGrid: Win-Loss cell clicked at Col ${columnNumber}, Row ${rowNumber}`);
        console.log(`SimpleGameGrid: Current value: ${currentValue}, New value: ${newValue}`);
      }
      
      onHandSelect(columnNumber, rowNumber, newValue);
    }
  };

  const getDisplayValue = (entry?: GameEntry) => {
    if (!entry || !entry.handValue) return '-';
    
    if (isWinLossMode) {
      return entry.handValue === 'P' ? 'W' : 'L';
    }
    
    return entry.handValue;
  };

  const getCellStyles = (entry?: GameEntry, row?: number) => {
    if (!entry || !row) return { bg: '', text: 'text-gray-800' };
    
    const isPattern = isPatternCell(row);
    const isFirstRow = row === 1;
    
    // Only color pattern cells or first row cells with the blue/red coloring
    if ((isPattern && entry.handValue) || (isFirstRow && entry.handValue && !isWinLossMode)) {
      return {
        bg: entry.handValue === 'P' ? '#0d0b85' : '#ea384c',
        text: 'text-white'
      };
    }
    
    return {
      bg: '',
      text: 'text-gray-800'
    };
  };

  // Check if we should show reset indicators for this mode
  const shouldShowResetIndicators = predictionLogic === 'extend-reset';

  return (
    <div className="w-full">
      <ScrollArea className="h-full max-h-[70vh]" orientation="horizontal">
        <div className="w-full min-w-max">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="text-center w-10 sticky left-0 bg-background z-10">Row</TableHead>
                {columns.map(column => (
                  <TableHead key={column} className="text-center w-14" data-column={column}>Col {column}</TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {rows.map(row => (
                <TableRow key={row}>
                  <TableCell className="text-center font-medium bg-gray-100 sticky left-0 z-10">{row}</TableCell>
                  {columns.map(column => {
                    const entry = getEntryForPosition(column, row);
                    const styles = getCellStyles(entry, row);
                    const isPattern = isPatternCell(row);
                    
                    // Only show reset indicator if the entry has resetPoint=true explicitly
                    const wasReset = shouldShowResetIndicators && entry?.resetPoint === true;
                    
                    const recoveryStatus = getRecoveryStatus(entry);
                    // Get the correct bet amount for display using our helper
                    const displayBetAmount = entry ? getBetAmount(entry) : 0;
                    
                    let cellLabel = "";
                    if (!isWinLossMode) {
                      if (isExtendLogic() && isPattern) {
                        cellLabel = "NB";
                      } else if (row === 1) {
                        cellLabel = "NB";
                      } else if (entry?.result === 'N') {
                        cellLabel = "NB";
                      }
                    }
                    
                    return (
                      <TableCell
                        key={`${column}-${row}`}
                        className={`text-center ${getCellClassName(entry, row)} ${entry && entries[entries.length - 1]?.id === entry.id ? 'border-2 border-blue-500' : ''} relative`}
                        onClick={() => handleCellClick(column, row)}
                        style={{
                          cursor: (isWinLossMode || row === 1 || isPattern) ? 'pointer' : 'default',
                          backgroundColor: styles.bg || undefined
                        }}
                      >
                        <div className={`text-base font-semibold ${styles.text}`}>
                          {getDisplayValue(entry)}
                        </div>
                        
                        {/* Only show bet amount for non-pattern cells */}
                        {entry && !isPattern && (
                          <div className={`text-2xs ${styles.text === 'text-white' ? 'text-white/80' : 'text-gray-500'}`}>
                            ${safeFormatNumber(displayBetAmount)}
                          </div>
                        )}
                        
                        {cellLabel && (
                          <div className="text-2xs opacity-70">
                            {cellLabel}
                          </div>
                        )}
                        
                        {/* Show reset indicator dot if this is a reset point */}
                        {wasReset && (
                          <div className="absolute top-1 right-1 w-2 h-2 rounded-full bg-yellow-500" 
                               title={`Reset Point (${entry.resetType})`}></div>
                        )}
                        
                        {/* Recovery mode indicators */}
                        {recoveryStatus === 'start' && (
                          <div className="absolute bottom-0 left-0 w-0 h-0 border-b-8 border-l-8 border-green-500 border-r-transparent border-t-transparent" 
                               title="Recovery mode start"></div>
                        )}
                        {recoveryStatus === 'active' && (
                          <div className="absolute bottom-0 left-0 w-2 h-2 bg-green-500 rounded-full"
                               title="Recovery mode active"></div>
                        )}
                        {recoveryStatus === 'end' && (
                          <div className="absolute bottom-0 right-0 w-0 h-0 border-b-8 border-r-8 border-green-500 border-l-transparent border-t-transparent"
                               title="Recovery mode end"></div>
                        )}
                      </TableCell>
                    );
                  })}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </ScrollArea>
    </div>
  );
};

export default SimpleGameGrid;
