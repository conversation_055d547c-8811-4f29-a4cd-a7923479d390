
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { GameEntry, PredictionLogicType } from '@/types/game';
import { Badge } from "@/components/ui/badge";

interface StatisticsProps {
  entries: GameEntry[];
  displayMode?: 'full' | 'compact';
  predictionLogic?: PredictionLogicType;
  willResetOnWin?: boolean;
}

const Statistics = ({ entries, displayMode = 'compact', predictionLogic = 'same', willResetOnWin = false }: StatisticsProps) => {
  const totalBets = entries.filter(entry => entry.result !== 'N').length;
  const totalWins = entries.filter(entry => entry.result === 'W').length;
  const totalLosses = entries.filter(entry => entry.result === 'L').length;
  
  // Calculate cumulative profit/loss based on prediction logic
  const calculateProfitLoss = () => {
    if (entries.length === 0) return 0;
    
    if (predictionLogic === 'win-loss-prev-rows') {
      // For win-loss-prev-rows, we need to calculate profit/loss from bet amounts and results
      let profitLoss = 0;
      entries.forEach(entry => {
        // Only consider entries that have a bet amount (not pattern cells or first row entries)
        if (entry.betAmount > 0) {
          if (entry.handValue === 'P') { // Win
            profitLoss += entry.betAmount;
          } else if (entry.handValue === 'B') { // Loss
            profitLoss -= entry.betAmount;
          }
        }
      });
      return profitLoss;
    }
    
    // For other prediction logics, use the stored cumulative value
    return entries[entries.length - 1].cumulativeProfitLoss;
  };
  
  // Calculate profit since last reset point
  const calculateProfitSinceLastReset = () => {
    if (entries.length === 0) return 0;
    
    // Find the last reset point
    let lastResetIndex = -1;
    for (let i = entries.length - 1; i >= 0; i--) {
      if (entries[i].resetPoint === true) {
        lastResetIndex = i;
        break;
      }
    }
    
    // If no reset found, profit since last reset is the same as total profit
    if (lastResetIndex === -1) {
      return calculateProfitLoss();
    }
    
    // Calculate profit from last reset point to current
    const startProfit = entries[lastResetIndex].cumulativeProfitLoss;
    const currentProfit = entries[entries.length - 1].cumulativeProfitLoss;
    return currentProfit - startProfit;
  };
  
  const currentProfitLoss = calculateProfitLoss();
  const profitSinceLastReset = calculateProfitSinceLastReset();
  const winRate = totalBets > 0 ? ((totalWins / totalBets) * 100).toFixed(1) : '0.0';
  
  // Recovery mode stats
  const recoveryModeEntries = entries.filter(entry => entry.recoveryMode !== undefined);
  const isInRecoveryMode = recoveryModeEntries.length > 0 && 
                           recoveryModeEntries[recoveryModeEntries.length - 1].recoveryMode !== 'end';

  // Find last reset entry for display
  const lastResetEntry = [...entries].reverse().find(entry => entry.resetPoint === true);
  const lastResetType = lastResetEntry?.resetType || null;
  const showLastResetInfo = lastResetEntry && predictionLogic === 'extend-reset';

  return (
    <Card className="border-0 shadow-none">
      <CardHeader className="px-0 pt-0">
        <CardTitle className="text-base font-semibold text-primary">Game Statistics</CardTitle>
      </CardHeader>
      <CardContent className="px-0 pb-0">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <div>
              <p className="text-sm text-muted-foreground">Total Hands</p>
              <p className="text-2xl font-semibold">{entries.length}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Total Bets</p>
              <p className="text-2xl font-semibold">{totalBets}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Win Rate</p>
              <p className="text-2xl font-semibold">{winRate}%</p>
            </div>
            {isInRecoveryMode && (
              <div>
                <p className="text-sm text-muted-foreground">Recovery Mode</p>
                <Badge variant="destructive" className="animate-pulse">Active</Badge>
              </div>
            )}
            {showLastResetInfo && (
              <div>
                <p className="text-sm text-muted-foreground">Last Reset</p>
                <Badge variant={lastResetType === 'profit' ? 'default' : 'destructive'}>
                  {lastResetType === 'profit' ? 'Profit' : 'Loss'}
                </Badge>
              </div>
            )}
            {willResetOnWin && predictionLogic === 'extend-reset' && (
              <div>
                <p className="text-sm text-muted-foreground">Next Win</p>
                <Badge variant="default" className="animate-pulse bg-green-600">Will Reset</Badge>
              </div>
            )}
          </div>
          <div className="space-y-2">
            <div>
              <p className="text-sm text-muted-foreground">Wins</p>
              <div className="flex items-center gap-2">
                <p className="text-2xl font-semibold text-green-600">{totalWins}</p>
                <Badge variant="outline" className="bg-green-50 dark:bg-green-900/20">W</Badge>
              </div>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Losses</p>
              <div className="flex items-center gap-2">
                <p className="text-2xl font-semibold text-red-600">{totalLosses}</p>
                <Badge variant="outline" className="bg-red-50 dark:bg-red-900/20">L</Badge>
              </div>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Profit/Loss</p>
              <p className={`text-2xl font-semibold ${currentProfitLoss >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                ${currentProfitLoss.toLocaleString()}
              </p>
            </div>
            {predictionLogic === 'extend-reset' && (
              <div>
                <p className="text-sm text-muted-foreground">Profit Since Reset</p>
                <div className="flex items-center gap-2">
                  <p className={`text-2xl font-semibold ${profitSinceLastReset >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    ${profitSinceLastReset.toLocaleString()}
                  </p>
                  {profitSinceLastReset >= 0 && (
                    <Badge variant="outline" className="bg-blue-50 dark:bg-blue-900/20">
                      {(lastResetEntry ? entries[0].cumulativeProfitLoss : 0) + profitSinceLastReset >= 5000 ? '5X Met' : `${((profitSinceLastReset / 5000) * 100).toFixed(0)}%`}
                    </Badge>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default Statistics;
