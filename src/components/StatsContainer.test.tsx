
import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import StatsContainer from './StatsContainer';
import { GameEntry } from '@/types/game';

describe('StatsContainer', () => {
  const mockProps = {
    baseBet: 1000,
    raiseBet: 1000,
    rowCount: 4,
    betStrategy: 'loss' as const,
    onBaseBetChange: vi.fn(),
    onRaiseBetChange: vi.fn(),
    onRowCountChange: vi.fn(),
    onBetStrategyChange: vi.fn(),
    entries: [] as GameEntry[],
    chartData: [] as { column: number; profitLoss: number }[],
    predictionLogic: 'same' as const,
    onPredictionLogicChange: vi.fn(),
    resetBettingHands: 3,
    onResetBettingHandsChange: vi.fn()
  };

  it('renders settings mode correctly', () => {
    render(<StatsContainer {...mockProps} displayMode="settings" />);
    
    expect(screen.getByText('Bet Settings')).toBeInTheDocument();
    expect(screen.queryByText('Game Statistics')).not.toBeInTheDocument();
    expect(screen.queryByText('Continuous Losses Analysis')).not.toBeInTheDocument();
  });

  it('renders statistics mode correctly', () => {
    render(<StatsContainer {...mockProps} displayMode="statistics" />);
    
    expect(screen.getByText('Game Statistics')).toBeInTheDocument();
    expect(screen.getByText('Total Hands')).toBeInTheDocument();
    expect(screen.getByText('Win Rate')).toBeInTheDocument();
    expect(screen.queryByText('Bet Settings')).not.toBeInTheDocument();
  });

  it('renders loss analysis mode correctly', () => {
    render(<StatsContainer {...mockProps} displayMode="lossAnalysis" />);
    
    expect(screen.getByText('Continuous Losses Analysis')).toBeInTheDocument();
    expect(screen.getByText('No continuous losses recorded yet')).toBeInTheDocument();
  });

  it('calculates statistics correctly with entries', () => {
    const entries: GameEntry[] = [
      { id: 1, columnNumber: 1, rowNumber: 1, handValue: 'P', result: 'N', betAmount: 0, cumulativeProfitLoss: 0 },
      { id: 2, columnNumber: 1, rowNumber: 2, handValue: 'P', result: 'W', betAmount: 1000, cumulativeProfitLoss: 1000 },
      { id: 3, columnNumber: 1, rowNumber: 3, handValue: 'B', result: 'L', betAmount: 1000, cumulativeProfitLoss: 0 },
      { id: 4, columnNumber: 1, rowNumber: 4, handValue: 'P', result: 'W', betAmount: 1000, cumulativeProfitLoss: 1000 }
    ];
    
    render(<StatsContainer {...mockProps} entries={entries} displayMode="statistics" />);
    
    expect(screen.getByText('4')).toBeInTheDocument(); // Total hands
    expect(screen.getByText('3')).toBeInTheDocument(); // Total bets
    expect(screen.getByText('2')).toBeInTheDocument(); // Wins
    expect(screen.getByText('1')).toBeInTheDocument(); // Losses
    expect(screen.getByText('66.7%')).toBeInTheDocument(); // Win rate
    expect(screen.getByText('$1,000')).toBeInTheDocument(); // Profit/Loss
  });

  it('displays loss streaks correctly', () => {
    const entries: GameEntry[] = [
      { id: 1, columnNumber: 1, rowNumber: 1, handValue: 'P', result: 'N', betAmount: 0, cumulativeProfitLoss: 0 },
      { id: 2, columnNumber: 1, rowNumber: 2, handValue: 'P', result: 'L', betAmount: 1000, cumulativeProfitLoss: -1000 },
      { id: 3, columnNumber: 1, rowNumber: 3, handValue: 'B', result: 'L', betAmount: 1000, cumulativeProfitLoss: -2000 },
      { id: 4, columnNumber: 1, rowNumber: 4, handValue: 'P', result: 'W', betAmount: 1000, cumulativeProfitLoss: -1000 },
      { id: 5, columnNumber: 2, rowNumber: 1, handValue: 'B', result: 'N', betAmount: 0, cumulativeProfitLoss: -1000 },
      { id: 6, columnNumber: 2, rowNumber: 2, handValue: 'P', result: 'L', betAmount: 1000, cumulativeProfitLoss: -2000 },
      { id: 7, columnNumber: 2, rowNumber: 3, handValue: 'B', result: 'L', betAmount: 1000, cumulativeProfitLoss: -3000 }
    ];
    
    render(<StatsContainer {...mockProps} entries={entries} displayMode="lossAnalysis" />);
    
    expect(screen.getByText('2 losses in a row')).toBeInTheDocument();
    expect(screen.getAllByText('2')[0]).toBeInTheDocument(); // Occurrences
  });

  it('renders full mode with all sections', () => {
    render(<StatsContainer {...mockProps} />);
    
    expect(screen.getByText('Bet Settings')).toBeInTheDocument();
    expect(screen.getByText('Game Statistics')).toBeInTheDocument();
    expect(screen.getByText('Continuous Losses Analysis')).toBeInTheDocument();
  });

  it('shows recovery mode badge when in recovery mode', () => {
    const entries: GameEntry[] = [
      { id: 1, columnNumber: 1, rowNumber: 1, handValue: 'P', result: 'N', betAmount: 0, cumulativeProfitLoss: 0 },
      { id: 2, columnNumber: 1, rowNumber: 2, handValue: 'P', result: 'L', betAmount: 1000, cumulativeProfitLoss: -1000, recoveryMode: 'start' }
    ];
    
    render(<StatsContainer {...mockProps} entries={entries} displayMode="statistics" />);
    
    expect(screen.getByText('Recovery Mode')).toBeInTheDocument();
    expect(screen.getByText('Active')).toBeInTheDocument();
  });
});
