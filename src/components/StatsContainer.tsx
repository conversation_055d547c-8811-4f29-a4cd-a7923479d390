import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { GameEntry, PredictionLogicType, PredictionLogicOption } from '@/types/game';
import BetSettings from './BetSettings';
import Statistics from './Statistics';
import LossAnalysis from './LossAnalysis';

interface StatsContainerProps {
  baseBet: number;
  raiseBet: number;
  rowCount: number;
  betStrategy: 'loss' | 'win';
  onBaseBetChange: (value: number) => void;
  onRaiseBetChange: (value: number) => void;
  onRowCountChange: (value: number) => void;
  onBetStrategyChange: (value: 'loss' | 'win') => void;
  entries: GameEntry[];
  chartData: { column: number; profitLoss: number; }[];
  predictionLogic: PredictionLogicType;
  onPredictionLogicChange: (value: PredictionLogicType) => void;
  resetBettingHands: number;
  onResetBettingHandsChange: (value: number) => void;
  extensionFactor?: 2 | 3;
  onExtensionFactorChange?: (value: 2 | 3) => void;
  displayMode?: 'full' | 'settings' | 'statistics' | 'lossAnalysis';
  willResetOnWin?: boolean;
}

const StatsContainer = ({ 
  baseBet,
  raiseBet,
  rowCount,
  betStrategy,
  onBaseBetChange,
  onRaiseBetChange,
  onRowCountChange,
  onBetStrategyChange,
  entries,
  chartData,
  predictionLogic,
  onPredictionLogicChange,
  resetBettingHands,
  onResetBettingHandsChange,
  extensionFactor = 2,
  onExtensionFactorChange = () => {},
  displayMode = 'full',
  willResetOnWin = false
}: StatsContainerProps) => {
  
  // Create a predictionLogicOptions array without Mirror and Anti-Mirror
  const predictionLogicOptions: PredictionLogicOption[] = [
    { value: 'same-no-recovery', label: 'Same (No Recovery)' },
    { value: 'same-recovery', label: 'Same - Recovery' },
    { value: 'extend', label: 'Extend' },
    { value: 'extend-reset', label: 'Extend Reset' },
    { value: 'extend-pattern-change', label: 'Extend - Pattern Change' },
    { value: 'extend-2-1-1', label: 'Extend - 2-1-1' },
    { value: 'extend-2-1-1-recovery', label: 'Extend - 2-1-1 - Recovery' },
    { value: 'win-loss', label: 'Win Loss' },
    { value: 'win-loss-prev-rows', label: 'Win Loss - Previous Rows' },
  ];
  
  // Determine if bet strategy and reset betting hands should be hidden based on prediction logic
  const isWinLossLogic = predictionLogic === 'win-loss' || predictionLogic === 'win-loss-prev-rows';
  const isExtendLogic = predictionLogic === 'extend' || predictionLogic === 'extend-reset' || predictionLogic === 'extend-pattern-change' || predictionLogic === 'extend-2-1-1' || predictionLogic === 'extend-2-1-1-recovery';
  
  // Check if row count should be hidden (all extend type logics should hide this)
  const hideRowCount = isExtendLogic;
  
  const renderContent = () => {
    switch (displayMode) {
      case 'settings':
        return (
          <Card className="border-0 shadow-none">
            <CardHeader className="px-0 pt-0">
              <CardTitle className="text-base font-semibold text-primary">Bet Settings</CardTitle>
            </CardHeader>
            <CardContent className="px-0 pb-0">
              <BetSettings
                baseBet={baseBet}
                raiseBet={raiseBet}
                rowCount={rowCount}
                betStrategy={betStrategy}
                onBaseBetChange={onBaseBetChange}
                onRaiseBetChange={onRaiseBetChange}
                onRowCountChange={onRowCountChange}
                onBetStrategyChange={onBetStrategyChange}
                entries={entries}
                predictionLogic={predictionLogic}
                onPredictionLogicChange={onPredictionLogicChange}
                resetBettingHands={resetBettingHands}
                onResetBettingHandsChange={onResetBettingHandsChange}
                layout="horizontal"
                predictionLogicOptions={predictionLogicOptions}
                hideBetStrategy={isWinLossLogic || isExtendLogic}
                hideResetBettingHands={isWinLossLogic || isExtendLogic}
                hideRowCount={hideRowCount}
              />
            </CardContent>
          </Card>
        );

      case 'statistics':
        return <Statistics entries={entries} predictionLogic={predictionLogic} willResetOnWin={willResetOnWin} />;

      case 'lossAnalysis':
        return <LossAnalysis entries={entries} />;

      case 'full':
      default:
        return (
          <div className="grid gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-base font-semibold">Bet Settings</CardTitle>
              </CardHeader>
              <CardContent>
                <BetSettings
                  baseBet={baseBet}
                  raiseBet={raiseBet}
                  rowCount={rowCount}
                  betStrategy={betStrategy}
                  onBaseBetChange={onBaseBetChange}
                  onRaiseBetChange={onRaiseBetChange}
                  onRowCountChange={onRowCountChange}
                  onBetStrategyChange={onBetStrategyChange}
                  entries={entries}
                  predictionLogic={predictionLogic}
                  onPredictionLogicChange={onPredictionLogicChange}
                  resetBettingHands={resetBettingHands}
                  onResetBettingHandsChange={onResetBettingHandsChange}
                  layout="horizontal"
                  predictionLogicOptions={predictionLogicOptions}
                  hideBetStrategy={isWinLossLogic || isExtendLogic}
                  hideResetBettingHands={isWinLossLogic || isExtendLogic}
                  hideRowCount={hideRowCount}
                />
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Statistics entries={entries} predictionLogic={predictionLogic} willResetOnWin={willResetOnWin} />
              <LossAnalysis entries={entries} />
            </div>
          </div>
        );
    }
  };

  return renderContent();
};

export default StatsContainer;
