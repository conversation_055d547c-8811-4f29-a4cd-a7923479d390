import * as React from "react"

const M<PERSON><PERSON>LE_BREAKPOINT = 768

export function useIsMobile() {
  const [isMobile, setIsMobile] = React.useState<boolean>(
    typeof window !== 'undefined' ? window.innerWidth < MOBILE_BREAKPOINT : false
  )

  React.useEffect(() => {
    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)
    const onChange = () => {
      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)
    }
    
    // Set initial value
    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)
    
    // Add event listener
    mql.addEventListener("change", onChange)
    
    // Also listen for resize events for more immediate response
    window.addEventListener("resize", onChange)
    
    // Cleanup
    return () => {
      mql.removeEventListener("change", onChange)
      window.removeEventListener("resize", onChange)
    }
  }, [])

  return isMobile
}

export function useTouchDevice() {
  const [isTouch, setIsTouch] = React.useState<boolean>(
    typeof window !== 'undefined' ? ('ontouchstart' in window) || (navigator.maxTouchPoints > 0) : false
  )

  React.useEffect(() => {
    // This will not change during runtime but we keep the pattern consistent
    setIsTouch(('ontouchstart' in window) || (navigator.maxTouchPoints > 0))
  }, [])

  return isTouch
}
