import { useState, useEffect } from 'react';
import { GameEntry, PredictionLogicType, ExtensionFactor } from '@/types/game';
import { 
  calculateNextPosition, 
  getPatternSourceRow,
  isPatternCell
} from '@/utils/nextBetCalculation';
import { getLossThreshold } from '@/utils/nextBetCalculation';
import { calculateNextPositionForWinLossPrevRows } from '@/utils/winLossPrevRowsCalculation';
import { calculateNextPositionForExtendReset, getExtendResetNextBetDetails, hasProfitResetOccurred } from '@/utils/extendResetPredictionCalculation';
import { getExtend211NextBetDetails, calculateNextPositionForExtend211 } from '@/utils/extend211PredictionCalculation';
import { getSameRecoveryNextBetDetails, isInRecoveryMode } from '@/utils/sameRecoveryCalculation';
import { 
  getExtend211RecoveryNextBetDetails, 
  calculateNextPositionForExtend211Recovery, 
  checkIfInRecoveryMode,
  isPatternCell as isExtend211RecoveryPatternCell
} from '@/utils/extend211RecoveryPredictionCalculation';

export interface CalculationDetails {
  baseBet: number;
  sameRowPreviousLoss: number;
  previousColumnLastLoss: number;
  previousLossInColumn: number;
  previousColumnRow1Loss: number;
  previousColumnRow2Loss: number;
  raiseBet: number;
  lossCount: number;
}

export const useBetCalculationDetails = (
  entries: GameEntry[],
  baseBet: number,
  raiseBet: number,
  rowCount: number,
  betStrategy: 'loss' | 'win',
  recoveryModeActive: boolean,
  recoveryModeBetAmount: number,
  predictionLogic: PredictionLogicType,
  extensionFactor: ExtensionFactor
) => {
  const [calculationDetails, setCalculationDetails] = useState<CalculationDetails>({
    baseBet,
    sameRowPreviousLoss: 0,
    previousColumnLastLoss: 0,
    previousLossInColumn: 0,
    previousColumnRow1Loss: 0,
    previousColumnRow2Loss: 0,
    raiseBet,
    lossCount: 0
  });

  useEffect(() => {
    if (entries.length === 0) {
      setCalculationDetails({
        baseBet,
        sameRowPreviousLoss: 0,
        previousColumnLastLoss: 0,
        previousLossInColumn: 0,
        previousColumnRow1Loss: 0,
        previousColumnRow2Loss: 0,
        raiseBet,
        lossCount: 0
      });
      return;
    }

    let nextPosition = calculateNextPosition(entries, rowCount, predictionLogic, extensionFactor);

    if (predictionLogic === 'win-loss') {
      const lossCount = entries.filter(entry => entry.result === 'L').length;
      setCalculationDetails({
        baseBet,
        sameRowPreviousLoss: 0,
        previousColumnLastLoss: 0,
        previousLossInColumn: 0,
        previousColumnRow1Loss: 0,
        previousColumnRow2Loss: 0,
        raiseBet,
        lossCount
      });
      return;
    }

    if (predictionLogic === 'win-loss-prev-rows') {
      nextPosition = calculateNextPositionForWinLossPrevRows(entries, rowCount);
      const lossCount = entries.filter(entry => entry.result === 'L').length;
      setCalculationDetails({
        baseBet,
        sameRowPreviousLoss: 0,
        previousColumnLastLoss: 0,
        previousLossInColumn: 0,
        previousColumnRow1Loss: 0,
        previousColumnRow2Loss: 0,
        raiseBet,
        lossCount
      });
      return;
    }

    if (predictionLogic === 'same-no-recovery') {
      const previousEntry = entries.length > 0 ? entries[entries.length - 1] : null;
      // Ensure sameRowPreviousLoss is a number, not a Promise
      const sameRowPreviousLoss = previousEntry?.result === 'L' ? 
        (typeof previousEntry.betAmount === 'number' ? previousEntry.betAmount : 0) : 0;
      
      setCalculationDetails({
        baseBet,
        sameRowPreviousLoss,
        previousColumnLastLoss: 0,
        previousLossInColumn: 0,
        previousColumnRow1Loss: 0,
        previousColumnRow2Loss: 0,
        raiseBet,
        lossCount: 0
      });
      return;
    }
    
    if (predictionLogic === 'same-recovery') {
      // Get detailed calculation info for Same Recovery
      const betDetails = getSameRecoveryNextBetDetails(
        entries,
        false, // isGameReset
        baseBet,
        raiseBet,
        rowCount
      );
      
      setCalculationDetails({
        baseBet,
        sameRowPreviousLoss: betDetails.srpcValue,
        previousColumnLastLoss: betDetails.pclrlValue,
        previousLossInColumn: betDetails.plscValue,
        previousColumnRow1Loss: betDetails.row1Value,
        previousColumnRow2Loss: 0,
        raiseBet,
        lossCount: betDetails.lossCount
      });
      return;
    }

    if (predictionLogic === 'extend') {
      const previousEntry = entries.length > 0 ? entries[entries.length - 1] : null;
      const sameRowPreviousLoss = previousEntry?.result === 'L' ? 
        (typeof previousEntry.betAmount === 'number' ? previousEntry.betAmount : 0) : 0;
      
      setCalculationDetails({
        baseBet,
        sameRowPreviousLoss,
        previousColumnLastLoss: 0,
        previousLossInColumn: 0,
        previousColumnRow1Loss: 0,
        previousColumnRow2Loss: 0,
        raiseBet,
        lossCount: 0
      });
      return;
    }
    
    if (predictionLogic === 'extend-reset') {
      // Special handling for extend-reset, get detailed info
      const betDetails = getExtendResetNextBetDetails(
        entries, 
        false, // isGameReset 
        baseBet, 
        raiseBet, 
        rowCount, 
        extensionFactor
      );
      
      const profitResetOccurred = hasProfitResetOccurred(entries);
      
      const details = {
        baseBet,
        sameRowPreviousLoss: profitResetOccurred ? 0 : betDetails.srpcValue,
        previousColumnLastLoss: betDetails.pclrlValue,
        previousLossInColumn: betDetails.plscValue,
        previousColumnRow1Loss: 0,
        previousColumnRow2Loss: 0,
        raiseBet,
        lossCount: betDetails.resetOccurred ? 1 : 0
      };
      
      console.log('Extend Reset calculation details:', details);
      setCalculationDetails(details);
      return;
    }

    if (predictionLogic === 'extend-2-1-1') {
      // Special handling for extend-2-1-1, get detailed info
      const { columnNumber, rowNumber } = calculateNextPositionForExtend211(entries, rowCount, extensionFactor);
      console.log(`useBetCalculationDetails for extend-2-1-1: Next position is Column ${columnNumber}, Row ${rowNumber}`);
      
      // If it's a pattern cell (Row 1 or Row 4), no bet details needed
      if (isPatternCell(rowNumber, predictionLogic, extensionFactor, rowCount)) {
        console.log(`useBetCalculationDetails: Row ${rowNumber} is a pattern cell (No Bet)`);
        setCalculationDetails({
          baseBet,
          sameRowPreviousLoss: 0,
          previousColumnLastLoss: 0,
          previousLossInColumn: 0,
          previousColumnRow1Loss: 0,
          previousColumnRow2Loss: 0,
          raiseBet,
          lossCount: 0
        });
        return;
      }
      
      const betDetails = getExtend211NextBetDetails(
        entries, 
        false, // isGameReset 
        baseBet, 
        raiseBet, 
        rowCount, 
        extensionFactor
      );
      
      const details = {
        baseBet,
        sameRowPreviousLoss: betDetails.srpcValue,
        previousColumnLastLoss: betDetails.pclrlValue,
        previousLossInColumn: betDetails.plscValue,
        previousColumnRow1Loss: 0,
        previousColumnRow2Loss: 0,
        raiseBet,
        lossCount: 0
      };
      
      console.log('Extend-2-1-1 calculation details:', details);
      setCalculationDetails(details);
      return;
    }

    if (predictionLogic === 'extend-2-1-1-recovery') {
      // Special handling for extend-2-1-1-recovery, get detailed info
      const { columnNumber, rowNumber } = calculateNextPositionForExtend211Recovery(entries, rowCount, extensionFactor);
      console.log(`useBetCalculationDetails for extend-2-1-1-recovery: Next position is Column ${columnNumber}, Row ${rowNumber}`);
      
      // If it's a pattern cell (Row 1 or Row 4), no bet details needed
      if (isExtend211RecoveryPatternCell(rowNumber)) {
        console.log(`useBetCalculationDetails: Row ${rowNumber} is a pattern cell (No Bet)`);
        setCalculationDetails({
          baseBet,
          sameRowPreviousLoss: 0,
          previousColumnLastLoss: 0,
          previousLossInColumn: 0,
          previousColumnRow1Loss: 0,
          previousColumnRow2Loss: 0,
          raiseBet,
          lossCount: 0
        });
        return;
      }
      
      // Check if we're in recovery mode
      const isInRecoveryMode = checkIfInRecoveryMode(entries);
      if (isInRecoveryMode) {
        // In recovery mode, we don't show the standard calculation details
        setCalculationDetails({
          baseBet,
          sameRowPreviousLoss: 0,
          previousColumnLastLoss: 0,
          previousLossInColumn: 0,
          previousColumnRow1Loss: 0,
          previousColumnRow2Loss: 0,
          raiseBet,
          lossCount: 0
        });
        return;
      }
      
      const betDetails = getExtend211RecoveryNextBetDetails(
        entries,
        baseBet, 
        raiseBet, 
        rowCount, 
        extensionFactor
      );
      
      const details = {
        baseBet,
        sameRowPreviousLoss: betDetails.srpcValue,
        previousColumnLastLoss: betDetails.pclrlValue,
        previousLossInColumn: betDetails.plscValue,
        previousColumnRow1Loss: betDetails.row1Value,
        previousColumnRow2Loss: 0,
        raiseBet,
        lossCount: betDetails.lossCount
      };
      
      console.log('Extend-2-1-1-Recovery calculation details:', details);
      setCalculationDetails(details);
      return;
    }

    const previousEntry = entries.length > 0 ? entries[entries.length - 1] : null;
    const sameRowPreviousLoss = previousEntry?.result === 'L' ? previousEntry.betAmount : 0;
    const previousColumnLastLoss = 0;
    const previousLossInColumn = 0;
    const previousColumnRow1Loss = 0;
    const previousColumnRow2Loss = 0;
    const lossCount = 0;

    setCalculationDetails({
      baseBet,
      sameRowPreviousLoss,
      previousColumnLastLoss,
      previousLossInColumn,
      previousColumnRow1Loss,
      previousColumnRow2Loss,
      raiseBet,
      lossCount
    });
  }, [entries, baseBet, raiseBet, rowCount, betStrategy, recoveryModeActive, recoveryModeBetAmount, predictionLogic, extensionFactor]);

  return { calculationDetails };
};
