import { useMemo } from 'react';
import { GameEntry, PredictionLogicType, ExtensionFactor } from '@/types/game';

export const useGameGrid = (
  entries: GameEntry[], 
  rowCount: number, 
  predictionLogic: PredictionLogicType = 'same',
  extensionFactor: ExtensionFactor = 2,
  baseRowCount: number = 3
) => {
  const isWinLossMode = predictionLogic === 'win-loss' || predictionLogic === 'win-loss-prev-rows';
  const isSameNoRecoveryMode = predictionLogic === 'same-no-recovery';
  const isExtendMode = predictionLogic === 'extend' || predictionLogic === 'extend-reset' || predictionLogic === 'extend-pattern-change' || predictionLogic === 'extend-2-1-1' || predictionLogic === 'extend-2-1-1-recovery';
  
  // Find columns with active recovery mode
  const recoveryColumns = useMemo(() => {
    const columns = new Map<number, {start: number, end: number | null}>();
    
    entries.forEach(entry => {
      if (entry.recoveryMode === 'start') {
        columns.set(entry.columnNumber, {start: entry.id, end: null});
      } else if (entry.recoveryMode === 'end') {
        if (columns.has(entry.columnNumber)) {
          const record = columns.get(entry.columnNumber);
          if (record) {
            record.end = entry.id;
            columns.set(entry.columnNumber, record);
          }
        }
      }
    });
    
    return columns;
  }, [entries]);

  const getEntryForPosition = (columnNumber: number, rowNumber: number) => {
    return entries.find(
      entry => entry.columnNumber === columnNumber && entry.rowNumber === rowNumber
    );
  };

  const isPatternCell = (rowNumber: number) => {
    if (!isExtendMode) return false;
    
    // For extend logic, Row 1, Row 4, and Row 6 are pattern cells (No Bet cells)
    if (predictionLogic === 'extend-2-1-1' || predictionLogic === 'extend-2-1-1-recovery') {
      return rowNumber === 1 || rowNumber === 4 || rowNumber === 6;
    }
    
    // For other extend logics, only Row 1 and Row 4 are pattern cells
    return rowNumber === 1 || rowNumber === 4;
  };

  const getPatternSection = (rowNumber: number) => {
    if (!isExtendMode) return 0;
    
    // For extend-2-1-1 with rows 1, 4, and 6 as pattern cells:
    // Rows 1-3 are first section, rows 4-5 are second section, rows 6-7 are third section
    if (predictionLogic === 'extend-2-1-1' || predictionLogic === 'extend-2-1-1-recovery') {
      if (rowNumber <= 3) {
        return 0; // First section (rows 1-3)
      } else if (rowNumber <= 5) {
        return 1; // Second section (rows 4-5)
      } else {
        return 2; // Third section (rows 6-7)
      }
    }
    
    // For other extend logics with rows 1 and 4 as pattern cells:
    // Rows 1-3 are first section, rows 4-6 are second section
    return rowNumber <= 3 ? 0 : 1;
  };

  const getPatternSourceRow = (rowNumber: number) => {
    // For pattern cells in extend mode, use the last row of the previous section
    if (!isExtendMode) return rowNumber;
    
    if (rowNumber === 4) {
      return 3; // Pattern cell Row 4 uses Row 3 as source
    } else if (rowNumber === 6) {
      return 5; // Pattern cell Row 6 uses Row 5 as source
    }
    
    return rowNumber;
  };

  const cellMatchesPattern = (entry: GameEntry, patternCellValue: string | undefined): boolean => {
    if (!entry?.handValue || !patternCellValue) return false;
    return entry.handValue === patternCellValue;
  };

  const getCellClassName = (entry?: GameEntry, rowNumber?: number) => {
    // Win-Loss specific coloring
    if (isWinLossMode && entry?.handValue) {
      if (entry.handValue === 'P') return 'bg-green-100'; // Win
      if (entry.handValue === 'B') return 'bg-red-100';  // Loss
    }
    
    const currentRow = rowNumber || 1;
    const currentColumn = entry?.columnNumber || 1;
    
    // Extend mode specific coloring
    if (isExtendMode) {
      // Pattern cells get the blue/red coloring based on value
      if (isPatternCell(currentRow) && entry?.handValue) {
        // No change to pattern cell coloring - they remain blue for P and red for B
        return '';  // This will be handled in the component with inline styling
      }
      
      // Get the section this row belongs to
      const section = getPatternSection(currentRow);
      
      // Find the pattern cell for this section
      const patternCellRow = section === 0 ? 1 : section === 1 ? 4 : 6;
      
      const patternCell = entries.find(
        e => e.columnNumber === currentColumn && 
             e.rowNumber === patternCellRow
      );
      
      // If we have a pattern cell value and a current cell value, color based on match
      if (patternCell?.handValue && entry?.handValue) {
        const isMatch = patternCell.handValue === entry.handValue;
        return isMatch ? 'bg-green-100' : 'bg-red-100';
      }
    } 
    // Same-No-Recovery and Same mode coloring
    else if (!isWinLossMode) {
      // First row always gets blue/red coloring in these modes
      if (currentRow === 1 && entry?.handValue) {
        return ''; // This will be handled in the component with inline styling
      }
      
      // For regular rows, color based on matching with first row
      if (currentRow > 1 && entry?.handValue) {
        const firstRowEntry = entries.find(
          e => e.columnNumber === currentColumn && e.rowNumber === 1
        );
        
        if (firstRowEntry?.handValue) {
          const isMatch = firstRowEntry.handValue === entry.handValue;
          return isMatch ? 'bg-green-100' : 'bg-red-100';
        }
      }
    }
    
    // Default coloring based on result
    if (!entry?.result) return 'bg-white';
    if (entry.result === 'N') return 'bg-gray-100';
    return entry.result === 'W' ? 'bg-green-100' : 'bg-red-100';
  };

  // Updated isResetPoint to ONLY check the entry's resetPoint property
  // This ensures only the trigger cell shows the reset point
  const isResetPoint = (entry?: GameEntry) => {
    if (!entry) return false;
    if (isPatternCell(entry.rowNumber)) return false; // Never show reset dots on pattern cells
    return entry.resetPoint === true;
  };

  const getRecoveryStatus = (entry?: GameEntry) => {
    if (!entry) return null;
    
    // Check explicit recovery mode status first
    if (entry.recoveryMode) return entry.recoveryMode;
    
    // Check if entry is in an active recovery column
    if (entry?.id && recoveryColumns.has(entry.columnNumber)) {
      const recovery = recoveryColumns.get(entry.columnNumber);
      if (recovery) {
        // If entry is after start point and before end point (or no end point exists yet)
        if (entry.id >= recovery.start && (!recovery.end || entry.id <= recovery.end)) {
          return entry.id === recovery.start ? 'start' : 
                 recovery.end && entry.id === recovery.end ? 'end' : 'active';
        }
      }
    }
    
    return null;
  };

  // Function to get the bet amount for display - ensure it's always a number, not a Promise
  const getBetAmount = (entry?: GameEntry) => {
    if (!entry) return 0;
    
    // Pattern cells always have betAmount = 0
    if (isExtendMode && isPatternCell(entry.rowNumber)) {
      return 0;
    }
    
    // If betAmount is already a number, return it
    if (typeof entry.betAmount === 'number') {
      return entry.betAmount;
    }
    
    // If betAmount is a Promise or object, try to handle it
    if (entry.betAmount && typeof entry.betAmount === 'object') {
      // Check if it's a Promise by checking the constructor name
      const isPromise = entry.betAmount.constructor && 
                       entry.betAmount.constructor.name === 'Promise';
      
      if (isPromise) {
        // Since we can't directly resolve the Promise here (this is a synchronous function),
        // we need to use a different approach
        
        // Look for the same entry in the entries array that might have a resolved betAmount
        const resolvedEntry = entries.find(e => 
          e.id === entry.id && 
          typeof e.betAmount === 'number'
        );
        
        if (resolvedEntry) {
          return resolvedEntry.betAmount;
        }
        
        // If we can't find a resolved entry, use the position to estimate the bet amount
        // This is a fallback based on the typical bet progression
        if (entry.columnNumber === 1) {
          if (entry.rowNumber === 1) return 0; // No bet for row 1
          if (entry.rowNumber === 2) return 1000; // Base bet for row 2
          if (entry.rowNumber === 3) return 2000; // Double bet for row 3
          return 1000 * entry.rowNumber; // Rough estimate for other rows
        } else {
          // For columns > 1, use a base estimate
          return 1000 * entry.columnNumber;
        }
      }
    }
    
    // If we can't determine the bet amount, return 0 as a fallback
    return 0;
  };

  return {
    getEntryForPosition,
    isPatternCell,
    getPatternSection,
    getPatternSourceRow,
    getCellClassName,
    cellMatchesPattern,
    isResetPoint,
    getRecoveryStatus,
    getBetAmount,
    isWinLossMode,
    isWinLossPrevRowsLogic: () => predictionLogic === 'win-loss-prev-rows',
    isSameNoRecoveryLogic: () => isSameNoRecoveryMode,
    isExtendLogic: () => isExtendMode,
  };
};
