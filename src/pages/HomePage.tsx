import React, { useEffect, useState } from 'react';
import GameControls from '../components/GameControls';
import StatsContainer from '../components/StatsContainer';
import GameContainer from '../components/GameContainer';
import GameLayout from '../components/GameLayout';
import { useGameState } from '@/hooks/useGameState';
import { toast } from "sonner";
import { useBetCalculationDetails } from '@/hooks/useBetCalculationDetails';
import { createPredictionStrategy } from '@/utils/predictionStrategies';
import { 
  calculateNextBet, 
  shouldResetDueToProfitThreshold,
  shouldResetDueToResetPoint 
} from '@/utils/betCalculationService';
import { calculateNextPosition } from '@/utils/nextBetCalculation';
import { calculateNextPositionForExtendReset, getExtendResetNextBetDetails, hasProfitResetOccurred } from '@/utils/extendResetPredictionCalculation';
import { calculateWinLossNextBet } from '@/utils/winLossCalculation';
import { calculateWinLossPrevRowsNextBet } from '@/utils/winLossPrevRowsCalculation';
import { calculateSameNoRecoveryNextBet } from '@/utils/sameNoRecoveryCalculation';
import { calculateExtendNextBet } from '@/utils/extendPredictionCalculation';
import { calculateExtendResetNextBet } from '@/utils/extendResetPredictionCalculation';
import { calculateExtendPatternChangeNextBet, calculateNextPositionForExtendPatternChange } from '@/utils/extendPatternChangePredictionCalculation';
import { calculateExtend211NextBet, calculateNextPositionForExtend211 } from '@/utils/extend211PredictionCalculation';
import { calculateExtend211RecoveryNextBet, getExtend211RecoveryNextBetDetails } from '@/utils/extend211RecoveryPredictionCalculation';

const HomePage = () => {
  const {
    baseBet,
    setBaseBet,
    raiseBet,
    setRaiseBet,
    rowCount,
    betStrategy,
    setBetStrategy,
    entries,
    setEntries,
    predictionLogic,
    setPredictionLogic,
    resetBettingHands,
    setResetBettingHands,
    resetIndices,
    lastResetIndex,
    isGameReset,
    setIsGameReset,
    extensionFactor,
    setExtensionFactor,
    handleRowCountChange,
    handleExtensionFactorChange,
    handleHandSelect,
    handleUndo,
    handleReset,
    calculateChartData
  } = useGameState();

  const [nextBetAmount, setNextBetAmount] = useState(baseBet);
  const [forceResetBet, setForceResetBet] = useState(false);
  const [isCalculatingBet, setIsCalculatingBet] = useState(false);
  const [profitResetOccurred, setProfitResetOccurred] = useState(false);
  const [willResetOnWin, setWillResetOnWin] = useState(false);

  useEffect(() => {
    if (isGameReset) {
      setIsGameReset(false);
      setProfitResetOccurred(false);
    }
  }, [isGameReset, setIsGameReset]);

  const { calculationDetails } = useBetCalculationDetails(
    entries,
    baseBet,
    raiseBet,
    rowCount,
    betStrategy,
    false,
    0,
    predictionLogic,
    extensionFactor
  );

  useEffect(() => {
    if (isCalculatingBet) return;
    
    const getNextBetAmount = async () => {
      setIsCalculatingBet(true);
      
      try {
        if (entries.length === 0) {
          setNextBetAmount(baseBet);
          setIsCalculatingBet(false);
          return;
        }
        
        let shouldReset = false;
        let resetReason: 'profit' | 'reset-point' | null = null;
        
        let lastResetColumn = null;
        for (let i = entries.length - 1; i >= 0; i--) {
          if (entries[i].resetPoint && entries[i].resetType === 'profit') {
            lastResetColumn = entries[i].columnNumber;
            break;
          }
        }
        
        console.log(`Last profit reset column: ${lastResetColumn || 'none'}`);
        
        const lastEntry = entries[entries.length - 1];
        const isLastEntryReset = lastEntry.resetPoint === true;
        
        if (predictionLogic === 'extend-reset' && !isLastEntryReset) {
          const isProfitReset = shouldResetDueToProfitThreshold(entries, baseBet);
          const isResetPoint = shouldResetDueToResetPoint(entries);
          
          console.log(`Checking reset conditions - Profit: ${isProfitReset}, Reset point: ${isResetPoint}`);
          shouldReset = forceResetBet || isResetPoint || isProfitReset;
          
          if (isProfitReset) {
            resetReason = 'profit';
            console.log(`Reset condition detected for extend-reset: Profit threshold`);
          } else if (isResetPoint) {
            resetReason = 'reset-point';
            console.log(`Reset condition detected for extend-reset: Reset point`);
          }
          
          if (isProfitReset && !isLastEntryReset) {
            const updatedEntries = [...entries];
            updatedEntries[updatedEntries.length - 1] = {
              ...updatedEntries[updatedEntries.length - 1],
              resetPoint: true,
              resetType: resetReason
            };
            setEntries(updatedEntries);
            
            if (isProfitReset) {
              let lastResetIndex = -1;
              for (let i = updatedEntries.length - 2; i >= 0; i--) {
                if (updatedEntries[i].resetPoint === true) {
                  lastResetIndex = i;
                  break;
                }
              }
              
              const startProfit = lastResetIndex >= 0 ? updatedEntries[lastResetIndex].cumulativeProfitLoss : 0;
              const currentProfit = updatedEntries[updatedEntries.length - 1].cumulativeProfitLoss - startProfit;
              
              console.log(`Profit threshold reached (${currentProfit} >= ${baseBet * 5}): Resetting to base bet ${baseBet}`);
              toast.info(`Profit threshold reached (${currentProfit} >= ${baseBet * 5}): Bet reset to ${baseBet}`);
              setProfitResetOccurred(true);
            }
          }
        }
        
        let nextBet;
        let extend211BetDetails;
        let extend211RecoveryBetDetails;
        let patternChangeBetDetails;
        let willResetIfWin;
        
        const nextPosition = predictionLogic === 'extend-reset' 
          ? calculateNextPositionForExtendReset(entries, rowCount, extensionFactor)
          : calculateNextPosition(entries, rowCount, predictionLogic, extensionFactor);
          
        console.log(`\n=== NEXT BET CALCULATION DEBUG ===`);
        console.log(`Calculating next bet for: ${predictionLogic}`);
        console.log(`Next position: Column ${nextPosition.columnNumber}, Row ${nextPosition.rowNumber}`);
        console.log(`Reset occurred: ${shouldReset}, Reason: ${resetReason || 'none'}`);
        console.log(`Last profit reset column: ${lastResetColumn}`);
        
        const profitResetOccurred = hasProfitResetOccurred(entries);
        if (profitResetOccurred) {
          console.log(`Profit reset has occurred in game history - SRPC will be zeroed`);
          setProfitResetOccurred(true);
        }
        
        if (predictionLogic === 'extend-reset' && nextPosition.columnNumber === 1) {
          console.log(`\n=== COLUMN 1 SPECIAL DEBUG ===`);
          console.log(`Checking previous results in this column:`);
          
          entries.filter(entry => entry.columnNumber === nextPosition.columnNumber)
            .sort((a, b) => a.rowNumber - b.rowNumber)
            .forEach(entry => {
              console.log(`Row ${entry.rowNumber}: Result ${entry.result}, Bet ${entry.betAmount}`);
            });
        }
        
        if (predictionLogic === 'extend-reset' && nextPosition.rowNumber === 5) {
          console.log(`\n=== COLUMN ${nextPosition.columnNumber} ROW 5 SPECIAL DEBUG ===`);
          console.log(`Checking previous results in this column:`);
          
          entries.filter(entry => entry.columnNumber === nextPosition.columnNumber)
            .forEach(entry => {
              console.log(`Row ${entry.rowNumber}: Result ${entry.result}, Bet ${entry.betAmount}`);
            });
            
          const row3Entry = entries.find(
            entry => entry.columnNumber === nextPosition.columnNumber && entry.rowNumber === 3
          );
          
          if (row3Entry) {
            console.log(`Row 3 (source for Row 5 calculation) Result: ${row3Entry.result}, Bet: ${row3Entry.betAmount}`);
          } else {
            console.log(`No Row 3 entry found in Column ${nextPosition.columnNumber}`);
          }
        }
        
        switch (predictionLogic) {
          case 'win-loss':
            nextBet = calculateWinLossNextBet(entries, isGameReset, baseBet, raiseBet, rowCount);
            break;
            
          case 'win-loss-prev-rows':
            nextBet = calculateWinLossPrevRowsNextBet(entries, isGameReset, baseBet, raiseBet, rowCount);
            break;
            
          case 'same-no-recovery':
            console.log('Calculating Same-No-Recovery next bet from HomePage');
            nextBet = calculateSameNoRecoveryNextBet(entries, isGameReset, baseBet, raiseBet, rowCount);
            console.log(`HomePage: Same-No-Recovery next bet calculated: ${nextBet}`);
            break;
            
          case 'extend':
            console.log('Calculating Extend next bet from HomePage');
            nextBet = calculateExtendNextBet(entries, isGameReset, baseBet, raiseBet, rowCount, extensionFactor);
            console.log(`HomePage: Extend next bet calculated: ${nextBet}`);
            break;
            
          case 'extend-reset':
            nextBet = calculateExtendResetNextBet(
              entries, 
              isGameReset, 
              baseBet, 
              raiseBet, 
              rowCount, 
              extensionFactor, 
              shouldReset, 
              resetReason,
              lastResetColumn
            );
            console.log(`HomePage: Extend Reset next bet calculated: ${nextBet}`);
            
            patternChangeBetDetails = getExtendResetNextBetDetails(
              entries, 
              isGameReset, 
              baseBet, 
              raiseBet, 
              rowCount, 
              extensionFactor,
              lastResetColumn
            );
            console.log(`ExtendReset Bet Calculation Details:`, patternChangeBetDetails);
            
            if (predictionLogic === 'extend-reset' && !isLastEntryReset) {
              willResetIfWin = shouldResetDueToProfitThreshold(entries, baseBet);
              setWillResetOnWin(willResetIfWin);
              
              if (willResetIfWin) {
                console.log(`A win with bet amount ${nextBet} would trigger a profit reset`);
              }
            }
            break;
            
          case 'extend-pattern-change':
            nextBet = calculateExtendPatternChangeNextBet(
              entries, 
              isGameReset, 
              baseBet, 
              raiseBet, 
              rowCount, 
              extensionFactor
            );
            console.log(`HomePage: Extend Pattern Change next bet calculated: ${nextBet}`);
            
            patternChangeBetDetails = getExtendResetNextBetDetails(
              entries, 
              isGameReset, 
              baseBet, 
              raiseBet, 
              rowCount, 
              extensionFactor,
              lastResetColumn
            );
            console.log(`ExtendPatternChange Bet Calculation Details:`, patternChangeBetDetails);
            
            if (predictionLogic === 'extend-pattern-change' && !isLastEntryReset) {
              willResetIfWin = shouldResetDueToProfitThreshold(entries, baseBet);
              setWillResetOnWin(willResetIfWin);
              
              if (willResetIfWin) {
                console.log(`A win with bet amount ${nextBet} would trigger a profit reset`);
              }
            }
            break;
            
          case 'extend-2-1-1':
            nextBet = calculateExtend211NextBet(
              entries, 
              isGameReset, 
              baseBet, 
              raiseBet, 
              rowCount, 
              extensionFactor
            );
            console.log(`HomePage: Extend 2-1-1 next bet calculated: ${nextBet}`);
            
            extend211BetDetails = getExtendResetNextBetDetails(
              entries, 
              isGameReset, 
              baseBet, 
              raiseBet, 
              rowCount, 
              extensionFactor,
              lastResetColumn
            );
            console.log(`Extend211 Bet Calculation Details:`, extend211BetDetails);
            
            if (predictionLogic === 'extend-2-1-1' && !isLastEntryReset) {
              willResetIfWin = shouldResetDueToProfitThreshold(entries, baseBet);
              setWillResetOnWin(willResetIfWin);
              
              if (willResetIfWin) {
                console.log(`A win with bet amount ${nextBet} would trigger a profit reset`);
              }
            }
            break;
            
          case 'extend-2-1-1-recovery':
            nextBet = calculateExtend211RecoveryNextBet(
              entries, 
              isGameReset, 
              baseBet, 
              raiseBet, 
              rowCount, 
              extensionFactor
            );
            console.log(`HomePage: Extend 2-1-1 Recovery next bet calculated: ${nextBet}`);
            
            extend211RecoveryBetDetails = getExtend211RecoveryNextBetDetails(
              entries, 
              baseBet, 
              raiseBet, 
              rowCount, 
              extensionFactor
            );
            console.log(`Extend211Recovery Bet Calculation Details:`, extend211RecoveryBetDetails);
            
            if (predictionLogic === 'extend-2-1-1-recovery' && !isLastEntryReset) {
              willResetIfWin = shouldResetDueToProfitThreshold(entries, baseBet);
              setWillResetOnWin(willResetIfWin);
              
              if (willResetIfWin) {
                console.log(`A win with bet amount ${nextBet} would trigger a profit reset`);
              }
            }
            break;
            
          default:
            nextBet = await calculateNextBet({
              entries,
              isGameReset,
              baseBet,
              raiseBet,
              rowCount,
              lastResetIndex,
              resetIndices,
              recoveryModeActive: false,
              recoveryModeBetAmount: 0,
              currentResetColumn: null,
              mirrorPrediction: null,
              betStrategy,
              resetBettingHands,
              predictionLogic,
              extensionFactor
            });
        }
        
        console.log(`Final next bet amount: ${nextBet}`);
        console.log(`=== END NEXT BET CALCULATION ===\n`);
        
        setNextBetAmount(nextBet);
      } catch (error) {
        console.error('Error calculating next bet:', error);
        const strategy = createPredictionStrategy(predictionLogic);
        const localNextBet = strategy.calculateBet({
          entries,
          isGameReset,
          baseBet,
          raiseBet,
          rowCount,
          lastResetIndex,
          resetIndices,
          recoveryModeActive: false,
          recoveryModeBetAmount: 0,
          currentResetColumn: null,
          mirrorPrediction: null,
          betStrategy,
          resetBettingHands,
          extensionFactor
        });
        setNextBetAmount(localNextBet);
      } finally {
        setIsCalculatingBet(false);
      }
    };

    getNextBetAmount();
  }, [
    entries, 
    isGameReset, 
    baseBet, 
    raiseBet, 
    rowCount, 
    lastResetIndex, 
    resetIndices, 
    betStrategy, 
    resetBettingHands, 
    predictionLogic, 
    extensionFactor,
    forceResetBet,
    isCalculatingBet,
    setEntries,
    profitResetOccurred
  ]);
  
  const chartData = calculateChartData();

  const isWinLossMode = predictionLogic === 'win-loss' || predictionLogic === 'win-loss-prev-rows';

  const handlePlayerSelect = () => {
    if (isWinLossMode) {
      console.log(`${predictionLogic} Mode: Adding Win result (P)`);
    }
    handleHandSelect(null, null, 'P');
    toast.success(isWinLossMode ? "Win result added" : "Player hand added");
  };
  
  const handleBankerSelect = () => {
    if (isWinLossMode) {
      console.log(`${predictionLogic} Mode: Adding Loss result (B)`);
    }
    handleHandSelect(null, null, 'B');
    toast.success(isWinLossMode ? "Loss result added" : "Banker hand added");
  };

  return (
    <GameLayout>
      <div className="order-1 sm:order-4 bg-white dark:bg-gray-800 rounded-lg shadow-md p-3 sm:p-4 animate-fadeIn">
        <GameContainer 
          entries={entries} 
          rowCount={rowCount} 
          onHandSelect={handleHandSelect} 
          predictionLogic={predictionLogic}
          resetIndices={resetIndices} 
          extensionFactor={extensionFactor}
          onUndo={handleUndo}
        />
      </div>

      <div className="order-5 sm:order-5 sticky bottom-4 z-10 mt-4">
        <GameControls 
          onPlayerSelect={handlePlayerSelect} 
          onBankerSelect={handleBankerSelect} 
          onUndo={handleUndo} 
          onReset={handleReset} 
          nextBetAmount={nextBetAmount} 
          betStrategy={betStrategy} 
          predictionLogic={predictionLogic}
          mirrorPrediction={null} 
          calculationDetails={calculationDetails} 
          recoveryModeActive={false}
          recoveryModeDetails={null}
          entries={entries}
          rowCount={rowCount}
          willResetOnWin={willResetOnWin}
        />
      </div>

      <div className="order-3 sm:order-1 bg-white dark:bg-gray-800 rounded-lg shadow-md p-3 sm:p-4 animate-fadeIn">
        <StatsContainer 
          baseBet={baseBet} 
          raiseBet={raiseBet} 
          rowCount={rowCount} 
          betStrategy={betStrategy} 
          onBaseBetChange={setBaseBet} 
          onRaiseBetChange={setRaiseBet} 
          onRowCountChange={handleRowCountChange} 
          onBetStrategyChange={setBetStrategy} 
          entries={entries} 
          chartData={chartData} 
          predictionLogic={predictionLogic} 
          onPredictionLogicChange={setPredictionLogic} 
          resetBettingHands={resetBettingHands} 
          onResetBettingHandsChange={setResetBettingHands} 
          extensionFactor={extensionFactor}
          onExtensionFactorChange={handleExtensionFactorChange}
          displayMode="settings" 
          willResetOnWin={willResetOnWin}
        />
      </div>

      <div className="order-2 sm:order-2 grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-3 sm:p-4 animate-fadeIn">
          <StatsContainer 
            baseBet={baseBet} 
            raiseBet={raiseBet} 
            rowCount={rowCount} 
            betStrategy={betStrategy} 
            onBaseBetChange={setBaseBet} 
            onRaiseBetChange={setRaiseBet} 
            onRowCountChange={handleRowCountChange} 
            onBetStrategyChange={setBetStrategy} 
            entries={entries} 
            chartData={chartData} 
            predictionLogic={predictionLogic} 
            onPredictionLogicChange={setPredictionLogic} 
            resetBettingHands={resetBettingHands} 
            onResetBettingHandsChange={setResetBettingHands} 
            extensionFactor={extensionFactor}
            onExtensionFactorChange={handleExtensionFactorChange}
            displayMode="statistics" 
            willResetOnWin={willResetOnWin}
          />
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-3 sm:p-4 animate-fadeIn">
          <StatsContainer 
            baseBet={baseBet} 
            raiseBet={raiseBet} 
            rowCount={rowCount} 
            betStrategy={betStrategy} 
            onBaseBetChange={setBaseBet} 
            onRaiseBetChange={setRaiseBet} 
            onRowCountChange={handleRowCountChange} 
            onBetStrategyChange={setBetStrategy} 
            entries={entries} 
            chartData={chartData} 
            predictionLogic={predictionLogic} 
            onPredictionLogicChange={setPredictionLogic} 
            resetBettingHands={resetBettingHands} 
            onResetBettingHandsChange={setResetBettingHands} 
            extensionFactor={extensionFactor}
            onExtensionFactorChange={handleExtensionFactorChange}
            displayMode="lossAnalysis" 
            willResetOnWin={willResetOnWin}
          />
        </div>
      </div>
    </GameLayout>
  );
};

export default HomePage;
