
#!/usr/bin/env node

/**
 * Helper script to run Vitest tests
 * 
 * This script provides clear instructions on how to run tests
 * since there's no npm test script in package.json
 */

console.log("\n🧪 WhatNext Test Runner 🧪\n");
console.log("To run the tests, please use one of the following commands:\n");
console.log("  npx vitest run     # Run tests once");
console.log("  npx vitest         # Run tests in watch mode\n");
console.log("Make sure you have all dependencies installed.\n");

// Check if being executed directly and attempt to run tests
if (require.main === module) {
  try {
    console.log("Attempting to run tests automatically...");
    require('vitest/run').default();
  } catch (error) {
    console.error("Could not automatically run tests. Please use the commands above.");
    console.error("Error:", error.message);
    
    // Provide more specific error handling
    if (error.message.includes('Cannot find package')) {
      console.error("\nIt looks like you're missing a dependency. Try running:");
      console.error("  npm install --save-dev @vitejs/plugin-react-swc vitest jsdom @testing-library/jest-dom");
    }
    
    process.exit(1);
  }
}

module.exports = {
  // Export any test utilities if needed in the future
};
