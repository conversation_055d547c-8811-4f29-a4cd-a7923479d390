
import { ReactNode } from 'react';

export type PredictionLogicType =
  | 'same'
  | 'win-loss'
  | 'win-loss-prev-rows'
  | 'same-no-recovery'
  | 'mirror'
  | 'anti-mirror'
  | 'extend'
  | 'extend-reset'
  | 'extend-pattern-change';

export type ExtensionFactor = 2 | 3;

export interface GameEntry {
  id: number;
  handValue?: 'P' | 'B';
  columnNumber: number;
  rowNumber: number;
  betAmount: number;
  result: 'W' | 'L' | 'N';
  cumulativeProfitLoss: number;
  recoveryMode?: 'start' | 'active' | 'end' | null;
  resetPoint?: boolean;
  resetType?: 'profit' | 'reset-point' | null;
  isPatternCell?: boolean;
}

export interface GameSettings {
  baseBet: number;
  raiseBet: number;
  rowCount: number;
  betStrategy: 'loss' | 'win';
  predictionLogic: PredictionLogicType;
  resetBettingHands: number;
  extensionFactor: ExtensionFactor;
}

export interface SettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
  gameSettings: GameSettings;
  onSaveSettings: (settings: GameSettings) => void;
}

export interface LayoutSettingsProps {
  rowCount: number;
  onRowCountChange: (newRowCount: number) => void;
  extensionFactor: ExtensionFactor;
  onExtensionFactorChange: (newExtensionFactor: ExtensionFactor) => void;
}

export interface PredictionSettingsProps {
  predictionLogic: PredictionLogicType;
  onPredictionLogicChange: (newPredictionLogic: PredictionLogicType) => void;
  resetBettingHands: number;
  onResetBettingHandsChange: (newResetBettingHands: number) => void;
}

export interface BetSettingsProps {
  baseBet: number;
  onBaseBetChange: (newBaseBet: number) => void;
  raiseBet: number;
  onRaiseBetChange: (newRaiseBet: number) => void;
  betStrategy: 'loss' | 'win';
  onBetStrategyChange: (newBetStrategy: 'loss' | 'win') => void;
}

export interface SettingsSectionProps {
  title: string;
  children: ReactNode;
}
