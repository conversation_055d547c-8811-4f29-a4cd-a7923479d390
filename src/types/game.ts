export type HandValue = 'P' | 'B';
export type BetStrategy = 'loss' | 'win';
export type PredictionLogicType = 'same' | 'win-loss' | 'win-loss-prev-rows' | 'same-no-recovery' | 'same-recovery' | 'extend' | 'extend-reset' | 'mirror' | 'anti-mirror' | 'extend-pattern-change' | 'extend-2-1-1' | 'extend-2-1-1-recovery';
export type ExtensionFactor = 2 | 3;

export interface GameEntry {
  id: number;
  handValue: HandValue;
  columnNumber: number;
  rowNumber: number;
  betAmount: number;
  result: 'W' | 'L' | 'N';
  cumulativeProfitLoss: number;
  isPatternCell?: boolean;
  recoveryMode?: 'start' | 'end' | 'active' | null;
  recoveryModeStart?: boolean;
  recoveryModeEnd?: boolean;
  resetPoint?: boolean;
  resetType?: 'profit' | 'reset-point' | null;
  originalRowNumber?: number; // Keep this property to avoid build errors in pdf/index.ts
}

export interface ChartData {
  column: number;
  profitLoss: number;
}

// This interface is needed by BetSettings.tsx and StatsContainer.tsx
export interface PredictionLogicOption {
  value: PredictionLogicType;
  label: string;
}
