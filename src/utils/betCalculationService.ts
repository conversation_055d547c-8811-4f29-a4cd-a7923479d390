import { GameEntry, PredictionLogicType, ExtensionFactor } from '@/types/game';

export interface CalculateNextBetParams {
  entries: GameEntry[];
  isGameReset: boolean;
  baseBet: number;
  raiseBet: number;
  rowCount: number;
  lastResetIndex: number;
  resetIndices: number[];
  recoveryModeActive: boolean;
  recoveryModeBetAmount: number;
  currentResetColumn: number | null;
  mirrorPrediction: 'P' | 'B' | null;
  betStrategy: 'loss' | 'win';
  resetBettingHands: number;
  predictionLogic: PredictionLogicType;
  extensionFactor: ExtensionFactor;
}

export interface CalculationResult {
  nextBet: number;
  result?: 'W' | 'L' | 'N';
}

import { calculateNextBet as localCalculateNextBet } from './nextBetCalculation';
import { calculateWinLossPrevRowsNextBet, determineWinLossPrevRowsResult } from './winLossPrevRowsCalculation';
import { calculateWinLossNextBet, determineWinLossResult } from './winLossCalculation';
import { calculateSameNoRecoveryNextBet, determineSameNoRecoveryResult } from './sameNoRecoveryCalculation';
import { calculateSameRecoveryNextBet, determineSameRecoveryResult, shouldEndRecoveryMode } from './sameRecoveryCalculation';
import { calculateExtendNextBet, determineExtendResult } from './extendPredictionCalculation';
import { calculateExtendResetNextBet, determineExtendResetResult, shouldResetDueToProfitThreshold as extendResetCheckProfitThreshold, hasProfitResetOccurred } from './extendResetPredictionCalculation';
import { calculateExtendPatternChangeNextBet, determineExtendPatternChangeResult } from './extendPatternChangePredictionCalculation';

export const shouldResetDueToProfitThreshold = (
  entries: GameEntry[],
  baseBet: number
): boolean => {
  if (entries.length === 0) return false;
  
  let lastResetIndex = -1;
  for (let i = entries.length - 1; i >= 0; i--) {
    if (entries[i].resetPoint === true) {
      lastResetIndex = i;
      break;
    }
  }
  
  if (lastResetIndex === -1 || lastResetIndex === entries.length - 1) {
    if (lastResetIndex === -1) {
      const currentProfit = entries[entries.length - 1].cumulativeProfitLoss;
      const profitThreshold = baseBet * 5;
      
      console.log(`First profit check: Current profit ${currentProfit}, threshold ${profitThreshold}`);
      
      return currentProfit >= profitThreshold;
    }
    
    return false;
  }
  
  const startProfit = lastResetIndex >= 0 ? entries[lastResetIndex].cumulativeProfitLoss : 0;
  const currentProfit = entries[entries.length - 1].cumulativeProfitLoss;
  const profitSinceReset = currentProfit - startProfit;
  
  const profitThreshold = baseBet * 5;
  
  console.log(`Checking profit threshold: Profit since reset ${profitSinceReset}, threshold ${profitThreshold}`);
  console.log(`Last reset index: ${lastResetIndex}, Start profit: ${startProfit}, Current profit: ${currentProfit}`);
  
  return profitSinceReset >= profitThreshold;
};

export const shouldResetDueToConsecutiveLosses = (
  entries: GameEntry[]
): boolean => {
  return false;
};

export const shouldResetDueToResetPoint = (
  entries: GameEntry[]
): boolean => {
  if (entries.length === 0) return false;
  
  const lastEntry = entries[entries.length - 1];
  const shouldReset = lastEntry.resetPoint === true;
  
  if (shouldReset) {
    console.log('Last entry is marked as a reset point - should reset to base bet');
  }
  
  return shouldReset;
};

export const getLastResetColumn = (entries: GameEntry[]): number | null => {
  const resetEntry = [...entries].reverse().find(entry => entry.resetPoint === true);
  return resetEntry ? resetEntry.columnNumber : null;
}

export const getLastResetType = (entries: GameEntry[]): 'profit' | 'reset-point' | null => {
  const resetEntry = [...entries].reverse().find(entry => entry.resetPoint === true);
  return resetEntry ? resetEntry.resetType || null : null;
}

export async function calculateNextBet(params: {
  entries: GameEntry[];
  isGameReset: boolean;
  baseBet: number;
  raiseBet: number;
  rowCount: number;
  lastResetIndex: number;
  resetIndices: number[];
  recoveryModeActive: boolean;
  recoveryModeBetAmount: number;
  currentResetColumn: number | null;
  mirrorPrediction: 'P' | 'B' | null;
  betStrategy: 'loss' | 'win';
  resetBettingHands: number;
  predictionLogic: PredictionLogicType;
  extensionFactor: ExtensionFactor;
}): Promise<number> {
  const { 
    entries, 
    isGameReset, 
    baseBet, 
    raiseBet, 
    rowCount, 
    predictionLogic,
    extensionFactor,
    lastResetIndex,
    resetIndices,
    recoveryModeActive,
    recoveryModeBetAmount,
    currentResetColumn,
    mirrorPrediction,
    betStrategy,
    resetBettingHands
  } = params;
  
  console.log(`\n=== NEXT BET CALCULATION DEBUG ===`);
  console.log(`Calculating next bet for: ${predictionLogic}`);
  
  // Get the next position for the current logic
  let nextPosition;
  
  if (predictionLogic === 'win-loss-prev-rows') {
    nextPosition = calculateNextPositionForWinLossPrevRows(entries, rowCount);
  } else if (predictionLogic === 'extend-reset') {
    nextPosition = calculateNextPositionForExtendReset(entries, rowCount, extensionFactor);
  } else if (predictionLogic === 'extend-pattern-change') {
    nextPosition = calculateNextPositionForExtendPatternChange(entries, rowCount, extensionFactor);
    console.log(`Next position: Column ${nextPosition.columnNumber}, Row ${nextPosition.rowNumber}`);
  } else if (predictionLogic === 'extend') {
    nextPosition = calculateNextPosition(entries, rowCount, predictionLogic, extensionFactor);
    console.log(`Next position: Column ${nextPosition.columnNumber}, Row ${nextPosition.rowNumber}`);
  } else {
    nextPosition = calculateNextPosition(entries, rowCount, predictionLogic, extensionFactor);
  }
  
  if (isGameReset) {
    console.log('Game was reset: Using base bet');
    return baseBet;
  }
  
  let lastResetColumn = null;
  let lastResetType = null;
  
  if (predictionLogic === 'extend-reset') {
    lastResetColumn = getLastResetColumn(entries);
    lastResetType = getLastResetType(entries);
    
    console.log(`Last reset column: ${lastResetColumn}, reset type: ${lastResetType}`);
    
    if (shouldResetDueToResetPoint(entries)) {
      const lastEntry = entries[entries.length - 1];
      const resetType = lastEntry.resetType || 'profit';
      console.log(`Last entry was marked as a reset point (${resetType}): Resetting to base bet ${baseBet}`);
      return baseBet;
    }
    
    if (extendResetCheckProfitThreshold(entries, baseBet)) {
      console.log(`Profit threshold reached: Resetting to base bet ${baseBet}`);
      return baseBet;
    }
  }

  if (predictionLogic === 'win-loss-prev-rows') {
    console.log('Using isolated win-loss-prev-rows calculation');
    return calculateWinLossPrevRowsNextBet(
      entries, 
      isGameReset, 
      baseBet, 
      raiseBet, 
      rowCount
    );
  }
  
  if (predictionLogic === 'win-loss') {
    console.log('Using isolated win-loss calculation');
    return calculateWinLossNextBet(
      entries,
      isGameReset,
      baseBet,
      raiseBet,
      rowCount
    );
  }
  
  if (predictionLogic === 'same-no-recovery') {
    console.log('Using isolated same-no-recovery calculation');
    return calculateSameNoRecoveryNextBet(
      entries,
      isGameReset,
      baseBet,
      raiseBet,
      rowCount
    );
  }
  
  if (predictionLogic === 'same-recovery') {
    console.log('Using isolated same-recovery calculation');
    return calculateSameRecoveryNextBet(
      entries,
      isGameReset,
      baseBet,
      raiseBet,
      rowCount
    );
  }
  
  if (predictionLogic === 'extend') {
    console.log('Using isolated extend calculation');
    return calculateExtendNextBet(
      entries,
      isGameReset,
      baseBet,
      raiseBet,
      rowCount,
      extensionFactor
    );
  }
  
  if (predictionLogic === 'extend-reset') {
    console.log('Using isolated extend-reset calculation');
    return calculateExtendResetNextBet(
      entries,
      isGameReset,
      baseBet,
      raiseBet,
      rowCount,
      extensionFactor,
      shouldResetDueToResetPoint(entries),
      entries.length > 0 && entries[entries.length - 1].resetPoint ? entries[entries.length - 1].resetType : null,
      lastResetColumn
    );
  }

  if (predictionLogic === 'extend-pattern-change') {
    console.log('Using isolated extend-pattern-change calculation');
    return calculateExtendPatternChangeNextBet(
      entries,
      isGameReset,
      baseBet,
      raiseBet,
      rowCount,
      extensionFactor
    );
  }

  // If no matching logic, default to baseBet
  return baseBet;
};

export const determineResult = async (
  handValue: 'P' | 'B',
  columnNumber: number,
  rowNumber: number,
  entries: GameEntry[],
  predictionLogic: PredictionLogicType,
  mirrorPrediction: 'P' | 'B' | null
): Promise<'W' | 'L' | 'N'> => {
  if (predictionLogic === 'win-loss') {
    return determineWinLossResult(handValue);
  }
  
  if (predictionLogic === 'win-loss-prev-rows') {
    return determineWinLossPrevRowsResult(handValue);
  }
  
  if (predictionLogic === 'same-no-recovery') {
    return determineSameNoRecoveryResult(
      handValue,
      columnNumber,
      rowNumber,
      entries
    );
  }
  
  if (predictionLogic === 'same-recovery') {
    return determineSameRecoveryResult(
      handValue,
      columnNumber,
      rowNumber,
      entries
    );
  }
  
  if (predictionLogic === 'extend') {
    return determineExtendResult(
      handValue,
      columnNumber,
      rowNumber,
      entries
    );
  }
  
  if (predictionLogic === 'extend-reset') {
    return determineExtendResetResult(
      handValue,
      columnNumber,
      rowNumber,
      entries
    );
  }

  if (predictionLogic === 'extend-pattern-change') {
    return determineExtendPatternChangeResult(
      handValue,
      columnNumber,
      rowNumber,
      entries
    );
  }

  // If no matching logic, default to 'N'
  return 'N';
};

function calculateNextPositionForWinLossPrevRows(entries: GameEntry[], rowCount: number): { columnNumber: number, rowNumber: number } {
  // Implement logic to calculate next position for win-loss-prev-rows
  return { columnNumber: 0, rowNumber: 0 };
}

function calculateNextPositionForExtendReset(entries: GameEntry[], rowCount: number, extensionFactor: ExtensionFactor): { columnNumber: number, rowNumber: number } {
  // Implement logic to calculate next position for extend-reset
  return { columnNumber: 0, rowNumber: 0 };
}

function calculateNextPositionForExtendPatternChange(entries: GameEntry[], rowCount: number, extensionFactor: ExtensionFactor): { columnNumber: number, rowNumber: number } {
  // Implement logic to calculate next position for extend-pattern-change
  return { columnNumber: 0, rowNumber: 0 };
}

function calculateNextPosition(entries: GameEntry[], rowCount: number, predictionLogic: PredictionLogicType, extensionFactor: ExtensionFactor): { columnNumber: number, rowNumber: number } {
  // Implement logic to calculate next position for other prediction logic types
  return { columnNumber: 0, rowNumber: 0 };
}

function createPredictionStrategy(predictionLogic: PredictionLogicType): {
  calculateBet: (params: {
    entries: GameEntry[];
    isGameReset: boolean;
    baseBet: number;
    raiseBet: number;
    rowCount: number;
    lastResetIndex: number;
    resetIndices: number[];
    recoveryModeActive: boolean;
    recoveryModeBetAmount: number;
    currentResetColumn: number | null;
    mirrorPrediction: 'P' | 'B' | null;
    betStrategy: 'loss' | 'win';
    resetBettingHands: number;
    extensionFactor: ExtensionFactor;
  }) => number;
} {
  // Implement logic to create prediction strategy based on prediction logic
  return {
    calculateBet: (params: {
      entries: GameEntry[];
      isGameReset: boolean;
      baseBet: number;
      raiseBet: number;
      rowCount: number;
      lastResetIndex: number;
      resetIndices: number[];
      recoveryModeActive: boolean;
      recoveryModeBetAmount: number;
      currentResetColumn: number | null;
      mirrorPrediction: 'P' | 'B' | null;
      betStrategy: 'loss' | 'win';
      resetBettingHands: number;
      extensionFactor: ExtensionFactor;
    }) => {
      // Implement logic to calculate bet based on strategy
      return 0;
    }
  };
}
