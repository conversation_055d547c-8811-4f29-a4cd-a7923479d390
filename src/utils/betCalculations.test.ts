
import { describe, it, expect } from 'vitest';
import { 
  getLossThreshold, 
  getWinThreshold, 
  calculateNextPosition,
  isFirstHandInColumn,
  getColumnLossCount,
  getColumnWinCount,
  hasAllLossesInColumn,
  hasAllWinsInColumn,
  calculateInitialRecoveryModeBet,
  calculateContinuingRecoveryModeBet,
  getColumnLossAmount,
  getColumnWinAmount,
  getSumOfBetsInColumn
} from './betCalculations';
import { GameEntry } from '@/types/game';

describe('getLossThreshold', () => {
  it('returns correct threshold based on row count', () => {
    expect(getLossThreshold(3)).toBe(2);
    expect(getLossThreshold(4)).toBe(2);
    expect(getLossThreshold(6)).toBe(3);
    expect(getLossThreshold(8)).toBe(4);
  });
});

describe('getWinThreshold', () => {
  it('returns correct threshold based on row count', () => {
    expect(getWinThreshold(3)).toBe(2);
    expect(getWinThreshold(4)).toBe(2);
    expect(getWinThreshold(6)).toBe(3);
    expect(getWinThreshold(8)).toBe(4);
  });
});

describe('calculateNextPosition', () => {
  it('returns first position for empty entries', () => {
    const result = calculateNextPosition([], 4);
    expect(result).toEqual({ columnNumber: 1, rowNumber: 1 });
  });

  it('returns next row in same column when not at last row', () => {
    const entries: GameEntry[] = [
      { id: 1, columnNumber: 1, rowNumber: 2, handValue: 'P', result: 'W', betAmount: 1000, cumulativeProfitLoss: 1000 }
    ];
    const result = calculateNextPosition(entries, 4);
    expect(result).toEqual({ columnNumber: 1, rowNumber: 3 });
  });

  it('returns first row of next column when at last row', () => {
    const entries: GameEntry[] = [
      { id: 1, columnNumber: 1, rowNumber: 4, handValue: 'P', result: 'W', betAmount: 1000, cumulativeProfitLoss: 1000 }
    ];
    const result = calculateNextPosition(entries, 4);
    expect(result).toEqual({ columnNumber: 2, rowNumber: 1 });
  });
});

describe('isFirstHandInColumn', () => {
  it('returns true for first row in any column', () => {
    expect(isFirstHandInColumn(1, 1)).toBe(true);
    expect(isFirstHandInColumn(2, 1)).toBe(true);
    expect(isFirstHandInColumn(10, 1)).toBe(true);
  });

  it('returns false for any other row', () => {
    expect(isFirstHandInColumn(1, 2)).toBe(false);
    expect(isFirstHandInColumn(2, 3)).toBe(false);
    expect(isFirstHandInColumn(10, 4)).toBe(false);
  });
});

describe('getColumnLossCount', () => {
  it('returns correct count of losses in a column', () => {
    const entries: GameEntry[] = [
      { id: 1, columnNumber: 1, rowNumber: 1, handValue: 'P', result: 'N', betAmount: 0, cumulativeProfitLoss: 0 },
      { id: 2, columnNumber: 1, rowNumber: 2, handValue: 'P', result: 'W', betAmount: 1000, cumulativeProfitLoss: 1000 },
      { id: 3, columnNumber: 1, rowNumber: 3, handValue: 'B', result: 'L', betAmount: 1000, cumulativeProfitLoss: 0 },
      { id: 4, columnNumber: 1, rowNumber: 4, handValue: 'P', result: 'L', betAmount: 1000, cumulativeProfitLoss: -1000 },
      { id: 5, columnNumber: 2, rowNumber: 1, handValue: 'B', result: 'N', betAmount: 0, cumulativeProfitLoss: -1000 },
      { id: 6, columnNumber: 2, rowNumber: 2, handValue: 'P', result: 'L', betAmount: 1000, cumulativeProfitLoss: -2000 }
    ];
    
    expect(getColumnLossCount(entries, 1)).toBe(2);
    expect(getColumnLossCount(entries, 2)).toBe(1);
    expect(getColumnLossCount(entries, 3)).toBe(0);
  });
});

describe('getColumnWinCount', () => {
  it('returns correct count of wins in a column', () => {
    const entries: GameEntry[] = [
      { id: 1, columnNumber: 1, rowNumber: 1, handValue: 'P', result: 'N', betAmount: 0, cumulativeProfitLoss: 0 },
      { id: 2, columnNumber: 1, rowNumber: 2, handValue: 'P', result: 'W', betAmount: 1000, cumulativeProfitLoss: 1000 },
      { id: 3, columnNumber: 1, rowNumber: 3, handValue: 'B', result: 'W', betAmount: 1000, cumulativeProfitLoss: 2000 },
      { id: 4, columnNumber: 1, rowNumber: 4, handValue: 'P', result: 'L', betAmount: 1000, cumulativeProfitLoss: 1000 },
      { id: 5, columnNumber: 2, rowNumber: 1, handValue: 'B', result: 'N', betAmount: 0, cumulativeProfitLoss: 1000 },
      { id: 6, columnNumber: 2, rowNumber: 2, handValue: 'P', result: 'W', betAmount: 1000, cumulativeProfitLoss: 2000 }
    ];
    
    expect(getColumnWinCount(entries, 1)).toBe(2);
    expect(getColumnWinCount(entries, 2)).toBe(1);
    expect(getColumnWinCount(entries, 3)).toBe(0);
  });
});

describe('hasAllLossesInColumn', () => {
  it('returns true when all betting entries in column have losses', () => {
    const entries: GameEntry[] = [
      { id: 1, columnNumber: 1, rowNumber: 1, handValue: 'P', result: 'N', betAmount: 0, cumulativeProfitLoss: 0 },
      { id: 2, columnNumber: 1, rowNumber: 2, handValue: 'P', result: 'L', betAmount: 1000, cumulativeProfitLoss: -1000 },
      { id: 3, columnNumber: 1, rowNumber: 3, handValue: 'B', result: 'L', betAmount: 1000, cumulativeProfitLoss: -2000 },
      { id: 4, columnNumber: 1, rowNumber: 4, handValue: 'P', result: 'L', betAmount: 1000, cumulativeProfitLoss: -3000 }
    ];
    
    expect(hasAllLossesInColumn(entries, 1, 4)).toBe(true);
  });

  it('returns false when not all betting entries in column have losses', () => {
    const entries: GameEntry[] = [
      { id: 1, columnNumber: 1, rowNumber: 1, handValue: 'P', result: 'N', betAmount: 0, cumulativeProfitLoss: 0 },
      { id: 2, columnNumber: 1, rowNumber: 2, handValue: 'P', result: 'W', betAmount: 1000, cumulativeProfitLoss: 1000 },
      { id: 3, columnNumber: 1, rowNumber: 3, handValue: 'B', result: 'L', betAmount: 1000, cumulativeProfitLoss: 0 },
      { id: 4, columnNumber: 1, rowNumber: 4, handValue: 'P', result: 'L', betAmount: 1000, cumulativeProfitLoss: -1000 }
    ];
    
    expect(hasAllLossesInColumn(entries, 1, 4)).toBe(false);
  });
});

describe('hasAllWinsInColumn', () => {
  it('returns true when all betting entries in column have wins', () => {
    const entries: GameEntry[] = [
      { id: 1, columnNumber: 1, rowNumber: 1, handValue: 'P', result: 'N', betAmount: 0, cumulativeProfitLoss: 0 },
      { id: 2, columnNumber: 1, rowNumber: 2, handValue: 'P', result: 'W', betAmount: 1000, cumulativeProfitLoss: 1000 },
      { id: 3, columnNumber: 1, rowNumber: 3, handValue: 'B', result: 'W', betAmount: 1000, cumulativeProfitLoss: 2000 },
      { id: 4, columnNumber: 1, rowNumber: 4, handValue: 'P', result: 'W', betAmount: 1000, cumulativeProfitLoss: 3000 }
    ];
    
    expect(hasAllWinsInColumn(entries, 1, 4)).toBe(true);
  });

  it('returns false when not all betting entries in column have wins', () => {
    const entries: GameEntry[] = [
      { id: 1, columnNumber: 1, rowNumber: 1, handValue: 'P', result: 'N', betAmount: 0, cumulativeProfitLoss: 0 },
      { id: 2, columnNumber: 1, rowNumber: 2, handValue: 'P', result: 'W', betAmount: 1000, cumulativeProfitLoss: 1000 },
      { id: 3, columnNumber: 1, rowNumber: 3, handValue: 'B', result: 'L', betAmount: 1000, cumulativeProfitLoss: 0 },
      { id: 4, columnNumber: 1, rowNumber: 4, handValue: 'P', result: 'W', betAmount: 1000, cumulativeProfitLoss: 1000 }
    ];
    
    expect(hasAllWinsInColumn(entries, 1, 4)).toBe(false);
  });
});

describe('calculateInitialRecoveryModeBet', () => {
  it('calculates correct recovery bet amount based on loss sum', () => {
    const result = calculateInitialRecoveryModeBet(3000, 1000, 4);
    // Total: 3000 (loss) + 3000 (3 × baseBet) = 6000
    // Per row: 6000 ÷ 3 (betting rows) = 2000
    // Rounded to nearest baseBet = 2000
    expect(result).toBe(2000);
  });
});

describe('calculateContinuingRecoveryModeBet', () => {
  it('calculates correct continuing recovery bet with doubled loss', () => {
    const result = calculateContinuingRecoveryModeBet(3000, 1000, 4);
    // Doubled loss: 6000 + 3000 (3 × baseBet) = 9000
    // Per row: 9000 ÷ 3 (betting rows) = 3000
    // Rounded to nearest baseBet = 3000
    expect(result).toBe(3000);
  });
});

describe('getColumnLossAmount', () => {
  it('returns sum of bet amounts for losing entries in a column', () => {
    const entries: GameEntry[] = [
      { id: 1, columnNumber: 1, rowNumber: 1, handValue: 'P', result: 'N', betAmount: 0, cumulativeProfitLoss: 0 },
      { id: 2, columnNumber: 1, rowNumber: 2, handValue: 'P', result: 'L', betAmount: 1000, cumulativeProfitLoss: -1000 },
      { id: 3, columnNumber: 1, rowNumber: 3, handValue: 'B', result: 'W', betAmount: 2000, cumulativeProfitLoss: 1000 },
      { id: 4, columnNumber: 1, rowNumber: 4, handValue: 'P', result: 'L', betAmount: 3000, cumulativeProfitLoss: -2000 }
    ];
    
    expect(getColumnLossAmount(entries, 1)).toBe(4000); // 1000 + 3000
  });
});

describe('getColumnWinAmount', () => {
  it('returns sum of bet amounts for winning entries in a column', () => {
    const entries: GameEntry[] = [
      { id: 1, columnNumber: 1, rowNumber: 1, handValue: 'P', result: 'N', betAmount: 0, cumulativeProfitLoss: 0 },
      { id: 2, columnNumber: 1, rowNumber: 2, handValue: 'P', result: 'W', betAmount: 1000, cumulativeProfitLoss: 1000 },
      { id: 3, columnNumber: 1, rowNumber: 3, handValue: 'B', result: 'L', betAmount: 2000, cumulativeProfitLoss: -1000 },
      { id: 4, columnNumber: 1, rowNumber: 4, handValue: 'P', result: 'W', betAmount: 3000, cumulativeProfitLoss: 2000 }
    ];
    
    expect(getColumnWinAmount(entries, 1)).toBe(4000); // 1000 + 3000
  });
});

describe('getSumOfBetsInColumn', () => {
  it('returns sum of all bet amounts in a column (excluding first row)', () => {
    const entries: GameEntry[] = [
      { id: 1, columnNumber: 1, rowNumber: 1, handValue: 'P', result: 'N', betAmount: 0, cumulativeProfitLoss: 0 },
      { id: 2, columnNumber: 1, rowNumber: 2, handValue: 'P', result: 'W', betAmount: 1000, cumulativeProfitLoss: 1000 },
      { id: 3, columnNumber: 1, rowNumber: 3, handValue: 'B', result: 'L', betAmount: 2000, cumulativeProfitLoss: -1000 },
      { id: 4, columnNumber: 1, rowNumber: 4, handValue: 'P', result: 'W', betAmount: 3000, cumulativeProfitLoss: 2000 }
    ];
    
    expect(getSumOfBetsInColumn(entries, 1)).toBe(6000); // 1000 + 2000 + 3000
  });
});
