
import { GameEntry } from '@/types/game';
import { toast } from "@/hooks/use-toast";

// Function to calculate the threshold for losses based on row count
export const getLossThreshold = (rows: number): number => {
  if (rows === 3) return 2;
  if (rows === 4) return 2;
  if (rows === 6) return 3;
  return 4; // For 8 rows
};

// Function to calculate the threshold for wins based on row count
export const getWinThreshold = (rows: number): number => {
  if (rows === 3) return 2;
  if (rows === 4) return 2;
  if (rows === 6) return 3;
  return 4; // For 8 rows
};

// Function to calculate the next position in the grid
export const calculateNextPosition = (
  currentEntries: GameEntry[], 
  rowCount: number
): { columnNumber: number; rowNumber: number } => {
  if (currentEntries.length === 0) {
    return { columnNumber: 1, rowNumber: 1 };
  }

  const lastEntry = currentEntries[currentEntries.length - 1];
  let nextColumn = lastEntry.columnNumber;
  let nextRow = lastEntry.rowNumber + 1;

  if (nextRow > rowCount) {
    nextColumn++;
    nextRow = 1;
  }

  return { columnNumber: nextColumn, rowNumber: nextRow };
};

// Function to check if a position is the first hand in a column
export const isFirstHandInColumn = (columnNumber: number, rowNumber: number): boolean => {
  return rowNumber === 1;
};

// Function to get the count of losses in a column
export const getColumnLossCount = (entries: GameEntry[], columnNumber: number): number => {
  return entries.filter(
    entry => entry.columnNumber === columnNumber && entry.result === 'L'
  ).length;
};

// Function to get the count of wins in a column
export const getColumnWinCount = (entries: GameEntry[], columnNumber: number): number => {
  return entries.filter(
    entry => entry.columnNumber === columnNumber && entry.result === 'W'
  ).length;
};

// Function to check if a column has all losses
export const hasAllLossesInColumn = (entries: GameEntry[], columnNumber: number, rowCount: number): boolean => {
  // Filter entries for this column, excluding the first row (which is 'N')
  const columnEntries = entries.filter(
    entry => entry.columnNumber === columnNumber && entry.rowNumber > 1
  );
  
  // If there are no betting entries in this column yet, return false
  if (columnEntries.length === 0) return false;
  
  // If we don't have enough entries to fill the column (excluding first row), return false
  if (columnEntries.length < rowCount - 1) return false;
  
  // Check if all entries in the column have 'L' result
  return columnEntries.every(entry => entry.result === 'L');
};

// Function to check if a column has all wins
export const hasAllWinsInColumn = (entries: GameEntry[], columnNumber: number, rowCount: number): boolean => {
  // Filter entries for this column, excluding the first row (which is 'N')
  const columnEntries = entries.filter(
    entry => entry.columnNumber === columnNumber && entry.rowNumber > 1
  );
  
  // If there are no betting entries in this column yet, return false
  if (columnEntries.length === 0) return false;
  
  // If we don't have enough entries to fill the column (excluding first row), return false
  if (columnEntries.length < rowCount - 1) return false;
  
  // Check if all entries in the column have 'W' result
  return columnEntries.every(entry => entry.result === 'W');
};

// Function to calculate initial recovery mode bet amount (for the first occurrence)
export const calculateInitialRecoveryModeBet = (lossSum: number, baseBet: number, rowCount: number): number => {
  // For initial recovery mode, we just sum the losses (no doubling)
  const totalLossAmount = lossSum;
  
  // Add baseBet for each betting row (excluding first row) instead of hardcoded 1000
  const additionalAmount = baseBet * (rowCount - 1);
  
  // Total amount to recover (losses plus additional amount)
  const totalAmount = totalLossAmount + additionalAmount;
  
  // Divide by number of rows (excluding first row)
  const perRowAmount = Math.round(totalAmount / (rowCount - 1));
  
  // Round to nearest baseBet (instead of nearest 1000)
  const roundedAmount = Math.ceil(perRowAmount / baseBet) * baseBet;
  
  return roundedAmount;
};

// Function to calculate continuing recovery mode bet amount
export const calculateContinuingRecoveryModeBet = (lossSum: number, baseBet: number, rowCount: number): number => {
  // For continuing recovery mode, we double the loss amount from the previous column
  const doubledLossAmount = lossSum * 2;
  
  // Add baseBet for each betting row (excluding first row) instead of hardcoded 1000
  const additionalAmount = baseBet * (rowCount - 1);
  
  // Total amount to recover (doubled losses plus additional amount)
  const totalAmount = doubledLossAmount + additionalAmount;
  
  // Divide by number of rows (excluding first row)
  const perRowAmount = Math.round(totalAmount / (rowCount - 1));
  
  // Round to nearest baseBet (instead of nearest 1000)
  const roundedAmount = Math.ceil(perRowAmount / baseBet) * baseBet;
  
  return roundedAmount;
};

// Function to get total loss amount in a column
export const getColumnLossAmount = (entries: GameEntry[], columnNumber: number): number => {
  return entries
    .filter(entry => entry.columnNumber === columnNumber && entry.rowNumber > 1 && entry.result === 'L')
    .reduce((sum, entry) => sum + entry.betAmount, 0);
};

// Function to get total win amount in a column
export const getColumnWinAmount = (entries: GameEntry[], columnNumber: number): number => {
  return entries
    .filter(entry => entry.columnNumber === columnNumber && entry.rowNumber > 1 && entry.result === 'W')
    .reduce((sum, entry) => sum + entry.betAmount, 0);
};

// Function to calculate the sum of bet amounts in a column
export const getSumOfBetsInColumn = (entries: GameEntry[], columnNumber: number): number => {
  return entries
    .filter(entry => entry.columnNumber === columnNumber && entry.rowNumber > 1)
    .reduce((sum, entry) => sum + entry.betAmount, 0);
};
