import { <PERSON><PERSON><PERSON><PERSON>, ExtensionFactor } from '@/types/game';

/**
 * Calculate the next bet amount specifically for Extend-2-1-1 logic.
 * This function is completely isolated from other prediction logics.
 * This is an exact clone of the Extend logic.
 */
export function calculateExtend211NextBet(
  entries: GameEntry[],
  isGameReset: boolean,
  baseBet: number,
  raiseBet: number,
  rowCount: number,
  extensionFactor: ExtensionFactor
): number {
  // For empty game or reset game, return base bet
  if (isGameReset || entries.length === 0) {
    // Do not check for profit threshold or consecutive losses in Extend-2-1-1 mode
    return baseBet;
  }

  // Calculate the next position
  const { columnNumber, rowNumber } = calculateNextPositionForExtend211(entries, rowCount, extensionFactor);
  console.log(`Calculating next bet for Extend-2-1-1 logic`);
  console.log(`Next position: Column ${columnNumber}, Row ${rowNumber}`);

  // Check if it's a pattern cell (Row 1 or Row 4)
  if (isPatternCell(rowNumber)) {
    console.log(`Row ${rowNumber} is a pattern cell (No Bet)`);
    return 0;
  }

  // Handle Row 2 in any column according to the formula: (SRPC + RBA) + (PCLRL + RBA) + RBA
  if (rowNumber === 2) {
    // Start with RBA as base
    let result = raiseBet;
    console.log(`Row 2 calculation - Starting with RBA: ${result}`);

    if (columnNumber > 1) {
      // For Row 2 in Column 2+, apply the standardized formula: (SRPC + RBA) + (PCLRL + RBA) + RBA
      console.log(`Row 2 in Column ${columnNumber} - applying standardized formula: (SRPC + RBA) + (PCLRL + RBA) + RBA`);
      
      // SRPC (Same Row Previous Column) component
      // Find the Row 2 entry in previous column
      const sameRowPrevCol = entries.find(
        entry => entry.columnNumber === columnNumber - 1 && 
                entry.rowNumber === 2 && 
                !entry.isPatternCell
      );
      
      // Check if SRPC is a loss or win
      if (sameRowPrevCol) {
        if (sameRowPrevCol.result === 'L') {
          const srpcValue = sameRowPrevCol.betAmount + raiseBet;
          result += srpcValue;
          console.log(`SRPC (Row 2, Col ${columnNumber-1}) was a LOSS: Adding (${sameRowPrevCol.betAmount} + ${raiseBet}) = ${srpcValue}`);
        } else if (sameRowPrevCol.result === 'W') {
          console.log(`SRPC (Row 2, Col ${columnNumber-1}) was a WIN: Adding 0`);
        } else {
          console.log(`SRPC (Row 2, Col ${columnNumber-1}) was neither win nor loss (${sameRowPrevCol.result}): Adding 0`);
        }
      } else {
        console.log(`No SRPC (Row 2 entry in Column ${columnNumber-1}) found: Adding 0`);
      }
      
      // PCLRL (Previous Column's Last Row) component
      // Find the last row in previous column
      const lastRowInPrevCol = getLastRowNumberInColumn(entries, columnNumber - 1);
      console.log(`Last row in previous column (Column ${columnNumber-1}) is Row ${lastRowInPrevCol}`);
      
      // Find the entry for the last row in previous column
      const prevColLastRow = entries.find(
        entry => entry.columnNumber === columnNumber - 1 && 
                entry.rowNumber === lastRowInPrevCol && 
                !entry.isPatternCell
      );
      
      // Check if PCLRL is a loss or win
      if (prevColLastRow) {
        if (prevColLastRow.result === 'L') {
          const pclrlValue = prevColLastRow.betAmount + raiseBet;
          result += pclrlValue;
          console.log(`PCLRL (Row ${lastRowInPrevCol}, Col ${columnNumber-1}) was a LOSS: Adding (${prevColLastRow.betAmount} + ${raiseBet}) = ${pclrlValue}`);
        } else if (prevColLastRow.result === 'W') {
          console.log(`PCLRL (Row ${lastRowInPrevCol}, Col ${columnNumber-1}) was a WIN: Adding 0`);
        } else {
          console.log(`PCLRL (Row ${lastRowInPrevCol}, Col ${columnNumber-1}) was neither win nor loss (${prevColLastRow.result}): Adding 0`);
        }
      } else {
        console.log(`No PCLRL (Row ${lastRowInPrevCol} entry in Column ${columnNumber-1}) found: Adding 0`);
      }
    }
    
    console.log(`Final Row 2 bet calculation: ${result}`);
    return result;
  }

  // Row 3 calculation - Implementing the specified formula: (SRPC + RBA) + (PLSC + RBA) + RBA
  if (rowNumber === 3) {
    console.log(`Column ${columnNumber}, Row 3: Starting with RBA: ${raiseBet}`);
    
    // Start with RBA as base
    let result = raiseBet;
    
    // Check if the same row in previous column was a loss
    if (columnNumber > 1) {
      const sameRowPrevCol = entries.find(
        entry => entry.columnNumber === columnNumber - 1 && 
                entry.rowNumber === 3 && 
                !entry.isPatternCell
      );
      
      if (sameRowPrevCol) {
        if (sameRowPrevCol.result === 'L') {
          const srpcValue = sameRowPrevCol.betAmount + raiseBet;
          result += srpcValue;
          console.log(`SRPC (Row 3, Col ${columnNumber-1}) was a LOSS: Adding (${sameRowPrevCol.betAmount} + ${raiseBet}) = ${srpcValue}`);
        } else if (sameRowPrevCol.result === 'W') {
          console.log(`SRPC (Row 3, Col ${columnNumber-1}) was a WIN: Not adding SRPC component`);
        } else {
          console.log(`SRPC (Row 3, Col ${columnNumber-1}) was neither win nor loss (${sameRowPrevCol.result}): Not adding SRPC component`);
        }
      } else {
        console.log(`No SRPC (Row 3 entry in Column ${columnNumber-1}) found: Not adding SRPC component`);
      }
    }
    
    // Check for previous loss in same column (Row 2)
    const prevRowSameCol = entries.find(
      entry => entry.columnNumber === columnNumber && 
              entry.rowNumber === 2 && 
              !entry.isPatternCell
    );
    
    if (prevRowSameCol) {
      if (prevRowSameCol.result === 'L') {
        const plscValue = prevRowSameCol.betAmount + raiseBet;
        result += plscValue;
        console.log(`PLSC (Row 2, Col ${columnNumber}) was a LOSS: Adding (${prevRowSameCol.betAmount} + ${raiseBet}) = ${plscValue}`);
      } else if (prevRowSameCol.result === 'W') {
        console.log(`PLSC (Row 2, Col ${columnNumber}) was a WIN: Not adding PLSC component`);
      } else {
        console.log(`PLSC (Row 2, Col ${columnNumber}) was neither win nor loss (${prevRowSameCol.result}): Not adding PLSC component`);
      }
    } else {
      console.log(`No PLSC (Row 2 entry in Column ${columnNumber}) found: Not adding PLSC component`);
    }
    
    console.log(`Final Row 3 bet calculation: ${result}`);
    return result;
  }

  // Row 5 calculation - Corrected formula: (Row 3 Loss in Current Column + RBA) + (SRPC + RBA) + RBA
  if (rowNumber === 5) {
    console.log(`Row 5 calculation - Corrected formula: (Row 3 Loss + RBA) + (SRPC + RBA) + RBA`);
    
    // Start with RBA as base
    let result = raiseBet;
    console.log(`Starting with RBA: ${result}`);
    
    // First component: (Row 3 Loss in Current Column + RBA)
    // Specifically look for Row 3 loss in current column
    const row3Entry = entries.find(
      entry => entry.columnNumber === columnNumber && 
              entry.rowNumber === 3 && 
              entry.result === 'L'
    );
    
    if (row3Entry) {
      const row3Loss = row3Entry.betAmount;
      const row3Component = row3Loss + raiseBet;
      result += row3Component;
      console.log(`Found Row 3 loss in current column: ${row3Loss}`);
      console.log(`Adding (Row 3 Loss + RBA): ${row3Component} (${row3Loss} + ${raiseBet})`);
    } else {
      console.log(`No Row 3 loss found in current column, not adding this component`);
    }
    
    // Second component: (SRPC + RBA)
    // Check for Same Row Previous Column loss (Row 5 in previous column)
    if (columnNumber > 1) {
      const sameRowPrevCol = entries.find(
        entry => entry.columnNumber === columnNumber - 1 && 
                entry.rowNumber === 5 && 
                !entry.isPatternCell &&
                entry.result === 'L'
      );
      
      if (sameRowPrevCol) {
        const srpcLoss = sameRowPrevCol.betAmount;
        const srpcComponent = srpcLoss + raiseBet;
        result += srpcComponent;
        console.log(`Found same row previous column loss at Col ${columnNumber-1}, Row 5: ${srpcLoss}`);
        console.log(`Adding (SRPC + RBA): ${srpcComponent} (${srpcLoss} + ${raiseBet})`);
      } else {
        console.log(`No loss found for Row 5 in previous column, not adding SRPC component`);
      }
    }
    
    console.log(`Final Row 5 bet calculation: ${result}`);
    return result;
  }

  // Row 7 calculation - Corrected formula: (Row 5 Loss in Current Column + RBA) + (SRPC + RBA) + RBA
  if (rowNumber === 7) {
    console.log(`Row 7 calculation - Formula: (Row 5 Loss + RBA) + (SRPC + RBA) + RBA`);
    
    // Start with RBA as base
    let result = raiseBet;
    console.log(`Starting with RBA: ${result}`);
    
    // First component: (Row 5 Loss in Current Column + RBA)
    // Specifically look for Row 5 loss in current column
    const row5Entry = entries.find(
      entry => entry.columnNumber === columnNumber && 
              entry.rowNumber === 5 && 
              entry.result === 'L'
    );
    
    if (row5Entry) {
      const row5Loss = row5Entry.betAmount;
      const row5Component = row5Loss + raiseBet;
      result += row5Component;
      console.log(`Found Row 5 loss in current column: ${row5Loss}`);
      console.log(`Adding (Row 5 Loss + RBA): ${row5Component} (${row5Loss} + ${raiseBet})`);
    } else {
      console.log(`No Row 5 loss found in current column, not adding this component`);
    }
    
    // Second component: (SRPC + RBA)
    // Check for Same Row Previous Column loss (Row 7 in previous column)
    if (columnNumber > 1) {
      const sameRowPrevCol = entries.find(
        entry => entry.columnNumber === columnNumber - 1 && 
                entry.rowNumber === 7 && 
                !entry.isPatternCell &&
                entry.result === 'L'
      );
      
      if (sameRowPrevCol) {
        const srpcLoss = sameRowPrevCol.betAmount;
        const srpcComponent = srpcLoss + raiseBet;
        result += srpcComponent;
        console.log(`Found same row previous column loss at Col ${columnNumber-1}, Row 7: ${srpcLoss}`);
        console.log(`Adding (SRPC + RBA): ${srpcComponent} (${srpcLoss} + ${raiseBet})`);
      } else {
        console.log(`No loss found for Row 7 in previous column, not adding SRPC component`);
      }
    }
    
    console.log(`Final Row 7 bet calculation: ${result}`);
    return result;
  }

  // Default case: return base bet
  console.log(`Default case - Using base bet: ${baseBet}`);
  return baseBet;
}

/**
 * Determine the result of a hand specifically for Extend-2-1-1 logic.
 * This function implements the win/loss identification rules as specified:
 * - Row 1, Row 4, and Row 6 are pattern cells (No Bet)
 * - Row 2 & Row 3 follow pattern from Row 1 (Win if same, Loss if different)
 * - Row 5 follows pattern from Row 4 (Win if same, Loss if different)
 * - Row 7 follows pattern from Row 6 (Win if same, Loss if different)
 */
export function determineExtend211Result(
  handValue: 'P' | 'B',
  columnNumber: number,
  rowNumber: number,
  entries: GameEntry[]
): 'W' | 'L' | 'N' {
  // Pattern cells (Row 1, Row 4, and Row 6) are always 'N' (No Bet)
  if (isPatternCell(rowNumber)) {
    console.log(`determineExtend211Result: Row ${rowNumber} is a pattern cell (No Bet)`);
    return 'N';
  }
  
  // For non-pattern cells, we need to check the pattern cell value
  let patternRow: number;
  
  if (rowNumber === 2 || rowNumber === 3) {
    // Rows 2 and 3 follow Row 1's pattern
    patternRow = 1;
  } else if (rowNumber === 5) {
    // Row 5 follows Row 4's pattern
    patternRow = 4;
  } else if (rowNumber === 7) {
    // Row 7 follows Row 6's pattern
    patternRow = 6;
  } else {
    console.log(`determineExtend211Result: No rule defined for Row ${rowNumber} - Result is N`);
    return 'N';
  }
  
  // Find the pattern cell in the current column
  const patternCell = entries.find(
    entry => entry.columnNumber === columnNumber && 
            entry.rowNumber === patternRow
  );
  
  // If pattern cell doesn't exist, we can't determine the result
  if (!patternCell) {
    console.log(`determineExtend211Result: No pattern cell found for Row ${patternRow}, Column ${columnNumber} - Result is N`);
    return 'N';
  }
  
  // Compare hand value with pattern cell value
  // Win if they match, Loss if they don't
  const result = handValue === patternCell.handValue ? 'W' : 'L';
  console.log(`determineExtend211Result: For Row ${rowNumber}, comparing with Row ${patternRow} pattern (${patternCell.handValue}), hand value ${handValue} - Result is ${result}`);
  return result;
}

/**
 * Get detailed information about the next bet calculation for Extend-2-1-1 logic.
 * This is used by useBetCalculationDetails to display bet evidence.
 */
export function getExtend211NextBetDetails(
  entries: GameEntry[],
  isGameReset: boolean,
  baseBet: number,
  raiseBet: number,
  rowCount: number,
  extensionFactor: ExtensionFactor,
  lastResetColumn: number | null = null
) {
  if (isGameReset || entries.length === 0) {
    return {
      baseBet,
      raiseBet,
      srpcValue: 0,
      pclrlValue: 0,
      plscValue: 0,
      resetOccurred: false
    };
  }

  const { columnNumber, rowNumber } = calculateNextPositionForExtend211(entries, rowCount, extensionFactor);
  console.log(`getExtend211NextBetDetails: Next position is Column ${columnNumber}, Row ${rowNumber}`);
  
  // Check if it's a pattern cell (Row 1 or Row 4)
  if (isPatternCell(rowNumber)) {
    console.log(`getExtend211NextBetDetails: Row ${rowNumber} is a pattern cell (No Bet)`);
    return {
      baseBet,
      raiseBet,
      srpcValue: 0,
      pclrlValue: 0,
      plscValue: 0,
      resetOccurred: false
    };
  }

  // Initialize values
  let srpcValue = 0;
  let pclrlValue = 0;
  let plscValue = 0;

  // Row 2 calculation
  if (rowNumber === 2) {
    if (columnNumber > 1) {
      // SRPC (Same Row Previous Column) component
      const sameRowPrevCol = entries.find(
        entry => entry.columnNumber === columnNumber - 1 && 
                entry.rowNumber === 2 && 
                !entry.isPatternCell &&
                entry.result === 'L'
      );
      
      if (sameRowPrevCol) {
        srpcValue = sameRowPrevCol.betAmount;
        console.log(`getExtend211NextBetDetails: SRPC for Row 2 = ${srpcValue}`);
      }
      
      // PCLRL (Previous Column's Last Row) component
      const lastRowInPrevCol = getLastRowNumberInColumn(entries, columnNumber - 1);
      const prevColLastRow = entries.find(
        entry => entry.columnNumber === columnNumber - 1 && 
                entry.rowNumber === lastRowInPrevCol && 
                !entry.isPatternCell &&
                entry.result === 'L'
      );
      
      if (prevColLastRow) {
        pclrlValue = prevColLastRow.betAmount;
        console.log(`getExtend211NextBetDetails: PCLRL for Row 2 = ${pclrlValue}`);
      }
    }
  }
  
  // Row 3 calculation
  else if (rowNumber === 3) {
    if (columnNumber > 1) {
      // SRPC (Same Row Previous Column) component
      const sameRowPrevCol = entries.find(
        entry => entry.columnNumber === columnNumber - 1 && 
                entry.rowNumber === 3 && 
                !entry.isPatternCell &&
                entry.result === 'L'
      );
      
      if (sameRowPrevCol) {
        srpcValue = sameRowPrevCol.betAmount;
        console.log(`getExtend211NextBetDetails: SRPC for Row 3 = ${srpcValue}`);
      }
    }
    
    // PLSC (Previous Loss in Same Column) component - specifically Row 2 loss
    const row2LossInCurrentCol = entries.find(
      entry => entry.columnNumber === columnNumber && 
              entry.rowNumber === 2 && 
              !entry.isPatternCell &&
              entry.result === 'L'
    );
    
    if (row2LossInCurrentCol) {
      plscValue = row2LossInCurrentCol.betAmount;
      console.log(`getExtend211NextBetDetails: PLSC (Row 2 loss) for Row 3 = ${plscValue}`);
    }
  }
  
  // Row 5 calculation
  else if (rowNumber === 5) {
    if (columnNumber > 1) {
      // SRPC (Same Row Previous Column) component
      const sameRowPrevCol = entries.find(
        entry => entry.columnNumber === columnNumber - 1 && 
                entry.rowNumber === 5 && 
                !entry.isPatternCell &&
                entry.result === 'L'
      );
      
      if (sameRowPrevCol) {
        srpcValue = sameRowPrevCol.betAmount;
        console.log(`getExtend211NextBetDetails: SRPC for Row 5 = ${srpcValue}`);
      }
    }
    
    // PLSC (Previous Loss in Same Column) component - specifically Row 3 loss
    const row3LossInCurrentCol = entries.find(
      entry => entry.columnNumber === columnNumber && 
              entry.rowNumber === 3 && 
              !entry.isPatternCell &&
              entry.result === 'L'
    );
    
    if (row3LossInCurrentCol) {
      plscValue = row3LossInCurrentCol.betAmount;
      console.log(`getExtend211NextBetDetails: PLSC (Row 3 loss) for Row 5 = ${plscValue}`);
    }
  }
  
  // Row 7 calculation
  else if (rowNumber === 7) {
    if (columnNumber > 1) {
      // SRPC (Same Row Previous Column) component
      const sameRowPrevCol = entries.find(
        entry => entry.columnNumber === columnNumber - 1 && 
                entry.rowNumber === 7 && 
                !entry.isPatternCell &&
                entry.result === 'L'
      );
      
      if (sameRowPrevCol) {
        srpcValue = sameRowPrevCol.betAmount;
        console.log(`getExtend211NextBetDetails: SRPC for Row 7 = ${srpcValue}`);
      }
    }
    
    // PLSC (Previous Loss in Same Column) component - specifically Row 5 loss
    const row5LossInCurrentCol = entries.find(
      entry => entry.columnNumber === columnNumber && 
              entry.rowNumber === 5 && 
              !entry.isPatternCell &&
              entry.result === 'L'
    );
    
    if (row5LossInCurrentCol) {
      plscValue = row5LossInCurrentCol.betAmount;
      console.log(`getExtend211NextBetDetails: PLSC (Row 5 loss) for Row 7 = ${plscValue}`);
    }
  }

  console.log(`getExtend211NextBetDetails: Final values - SRPC: ${srpcValue}, PCLRL: ${pclrlValue}, PLSC: ${plscValue}`);
  
  return {
    baseBet,
    raiseBet,
    srpcValue,
    pclrlValue,
    plscValue,
    resetOccurred: false
  };
}

// Helper Functions

/**
 * Get the last row number in a specific column
 */
export function getLastRowNumberInColumn(
  entries: GameEntry[],
  columnNumber: number
): number {
  // Filter entries for the specified column
  const columnEntries = entries.filter(entry => entry.columnNumber === columnNumber);
  
  // If no entries found, return 0
  if (columnEntries.length === 0) {
    return 0;
  }
  
  // Find the maximum row number
  return Math.max(...columnEntries.map(entry => entry.rowNumber));
}

/**
 * Check if a position has a previous loss in the column
 */
export function hasPreviousLossInColumn(
  entries: GameEntry[],
  columnNumber: number,
  rowNumber: number
): boolean {
  // Check if there's any loss in the same column with a lower row number
  return entries.some(
    entry => entry.columnNumber === columnNumber && 
            entry.rowNumber < rowNumber && 
            !entry.isPatternCell &&
            entry.result === 'L'
  );
}

/**
 * Get the previous loss in the column (any row before the current one)
 */
export function getPreviousLossInColumn(
  entries: GameEntry[],
  columnNumber: number,
  rowNumber: number
): number {
  // Find all losses in the same column with a lower row number
  const previousLosses = entries.filter(
    entry => entry.columnNumber === columnNumber && 
            entry.rowNumber < rowNumber && 
            !entry.isPatternCell &&
            entry.result === 'L'
  );
  
  // If no previous losses, return 0
  if (previousLosses.length === 0) {
    return 0;
  }
  
  // Find the most recent loss (highest row number)
  const mostRecentLoss = previousLosses.reduce(
    (prev, current) => (prev.rowNumber > current.rowNumber) ? prev : current
  );
  
  return mostRecentLoss.betAmount;
}

/**
 * Get the previous loss in the column but specifically for a given row
 */
export function getPreviousRowLossInColumn(
  entries: GameEntry[],
  columnNumber: number,
  rowNumber: number,
  specificRow: number
): number {
  // Find the loss for the specific row in the current column
  const specificRowLoss = entries.find(
    entry => entry.columnNumber === columnNumber && 
            entry.rowNumber === specificRow && 
            !entry.isPatternCell &&
            entry.result === 'L'
  );
  
  // If no specific row loss found, return 0
  if (!specificRowLoss) {
    return 0;
  }
  
  return specificRowLoss.betAmount;
}

/**
 * Get a loss from Row 3 in the current column
 */
export function getRow3LossInCurrentColumn(
  entries: GameEntry[],
  columnNumber: number
): number {
  // Find the Row 3 entry for the current column
  const row3Entry = entries.find(
    entry => entry.columnNumber === columnNumber && 
            entry.rowNumber === 3 && 
            entry.result === 'L'
  );
  
  // If it exists and is a loss, return its bet amount
  if (row3Entry) {
    return row3Entry.betAmount;
  }
  
  return 0;
}

/**
 * Get a loss from Row 2 in the previous column
 */
export function getPreviousColumnRow2Loss(
  entries: GameEntry[],
  columnNumber: number
): number {
  if (columnNumber <= 1) return 0;
  
  // Find the Row 2 entry for the previous column
  const row2Entry = entries.find(
    entry => entry.columnNumber === columnNumber - 1 && 
            entry.rowNumber === 2 && 
            !entry.isPatternCell &&
            entry.result === 'L'
  );
  
  // If it exists and is a loss, return its bet amount
  if (row2Entry) {
    return row2Entry.betAmount;
  }
  
  return 0;
}

/**
 * Get a loss from Row 3 in the previous column
 */
export function getPreviousColumnRow3Loss(
  entries: GameEntry[],
  columnNumber: number
): number {
  if (columnNumber <= 1) return 0;
  
  // Find the Row 3 entry for the previous column
  const row3Entry = entries.find(
    entry => entry.columnNumber === columnNumber - 1 && 
            entry.rowNumber === 3 && 
            !entry.isPatternCell &&
            entry.result === 'L'
  );
  
  // If it exists and is a loss, return its bet amount
  if (row3Entry) {
    return row3Entry.betAmount;
  }
  
  return 0;
}

/**
 * Get a loss from Row 5 in the previous column
 */
export function getPreviousColumnRow5Loss(
  entries: GameEntry[],
  columnNumber: number
): number {
  if (columnNumber <= 1) return 0;
  
  // Find the Row 5 entry for the previous column
  const row5Entry = entries.find(
    entry => entry.columnNumber === columnNumber - 1 && 
            entry.rowNumber === 5 && 
            !entry.isPatternCell &&
            entry.result === 'L'
  );
  
  // If it exists and is a loss, return its bet amount
  if (row5Entry) {
    return row5Entry.betAmount;
  }
  
  return 0;
}

/**
 * Get a loss from Row 5 in the current column
 */
export function getRow5LossInCurrentColumn(
  entries: GameEntry[],
  columnNumber: number
): number {
  // Find the Row 5 entry for the current column
  const row5Entry = entries.find(
    entry => entry.columnNumber === columnNumber && 
            entry.rowNumber === 5 && 
            entry.result === 'L'
  );
  
  // If it exists and is a loss, return its bet amount
  if (row5Entry) {
    return row5Entry.betAmount;
  }
  
  return 0;
}

/**
 * Get the same row's previous column loss
 */
export function getSameRowPreviousColumnLoss(
  entries: GameEntry[],
  columnNumber: number,
  rowNumber: number
): number {
  if (columnNumber <= 1) return 0;
  
  const sameRowPrevCol = entries.find(
    entry => entry.columnNumber === columnNumber - 1 && 
            entry.rowNumber === rowNumber && 
            !entry.isPatternCell &&
            entry.result === 'L'
  );
  
  return sameRowPrevCol ? sameRowPrevCol.betAmount : 0;
}

/**
 * Check if the same row in the previous column was a win
 */
export function wasSameRowPreviousColumnWin(
  entries: GameEntry[],
  columnNumber: number,
  rowNumber: number
): boolean {
  if (columnNumber <= 1) return false;
  
  const entry = entries.find(
    e => e.columnNumber === columnNumber - 1 && e.rowNumber === rowNumber && !e.isPatternCell
  );
  
  return entry?.result === 'W';
}

/**
 * Check if a position was a win
 */
export function wasPositionWin(
  entries: GameEntry[],
  columnNumber: number,
  rowNumber: number
): boolean {
  const entry = entries.find(
    e => e.columnNumber === columnNumber && e.rowNumber === rowNumber && !e.isPatternCell
  );
  
  return entry?.result === 'W';
}

/**
 * Check if a row is a pattern cell
 */
export function isPatternCell(rowNumber: number): boolean {
  return rowNumber === 1 || rowNumber === 4 || rowNumber === 6;
}

/**
 * Get the pattern section for a row
 * Returns 1 for rows 1-3, 2 for rows 4-5, 3 for rows 6-7
 */
export function getPatternSection(rowNumber: number): number {
  if (rowNumber <= 3) {
    return 1; // First section (rows 1-3)
  } else if (rowNumber <= 5) {
    return 2; // Second section (rows 4-5)
  } else {
    return 3; // Third section (rows 6-7)
  }
}

/**
 * Calculate the next position for Extend-2-1-1 logic
 */
export function calculateNextPositionForExtend211(
  entries: GameEntry[],
  rowCount: number,
  extensionFactor: ExtensionFactor
): { columnNumber: number; rowNumber: number } {
  if (entries.length === 0) {
    return { columnNumber: 1, rowNumber: 1 };
  }

  const lastEntry = entries[entries.length - 1];
  let nextColumnNumber = lastEntry.columnNumber;
  let nextRowNumber = lastEntry.rowNumber + 1;

  // For Extend-2-1-1, we always use 7 rows regardless of the rowCount and extensionFactor
  const effectiveRowCount = 7;
  
  // Check if we need to move to the next column
  if (nextRowNumber > effectiveRowCount) {
    nextColumnNumber++;
    nextRowNumber = 1;
  }

  // Skip pattern cells for auto-population
  if (isPatternCell(nextRowNumber)) {
    // If we're on Row 6, we need to ensure we don't skip to the next column yet
    if (nextRowNumber === 6) {
      console.log(`calculateNextPositionForExtend211: Auto-populating pattern cell at Row ${nextRowNumber}`);
      // We'll auto-populate this pattern cell, so we don't need to skip it
    }
  }

  return { columnNumber: nextColumnNumber, rowNumber: nextRowNumber };
}
