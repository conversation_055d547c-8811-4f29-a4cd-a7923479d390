import { <PERSON><PERSON><PERSON><PERSON>, ExtensionFactor } from '@/types/game';

/**
 * Calculate the next bet amount specifically for Extend-2-1-1-Recovery logic.
 * This function is completely isolated from other prediction logics.
 * This is an exact clone of the Extend-2-1-1 logic with recovery mode added.
 */
export function calculateExtend211RecoveryNextBet(
  entries: GameEntry[],
  isGameReset: boolean,
  baseBet: number,
  raiseBet: number,
  rowCount: number,
  extensionFactor: ExtensionFactor
): number {
  // If the game was reset, start with the base bet
  if (isGameReset) {
    return baseBet;
  }
  
  // Calculate the next position
  const { columnNumber, rowNumber } = calculateNextPositionForExtend211Recovery(entries, rowCount, extensionFactor);
  console.log(`Calculating next bet for Extend-2-1-1-Recovery logic`);
  console.log(`Next position: Column ${columnNumber}, Row ${rowNumber}`);

  // Check if it's a pattern cell (Row 1 or Row 4 or Row 6)
  if (isPatternCell(rowNumber)) {
    console.log(`Row ${rowNumber} is a pattern cell (No Bet)`);
    return 0;
  }
  
  // Check if we're in recovery mode
  const inRecoveryMode = checkIfInRecoveryMode(entries);
  console.log(`In recovery mode: ${inRecoveryMode}`);
  
  // If in recovery mode, calculate the recovery bet
  if (inRecoveryMode) {
    // Check if we should exit recovery mode (2 consecutive wins)
    const consecutiveWins = getConsecutiveWins(entries);
    console.log(`Consecutive wins: ${consecutiveWins}`);
    
    if (consecutiveWins >= 2) {
      console.log(`Exiting recovery mode after ${consecutiveWins} consecutive wins`);
      
      // Mark the last entry as the end of recovery mode
      entries[entries.length - 1].recoveryModeEnd = true;
      
      // Return to base bet
      return baseBet;
    }
    
    // Get the recovery mode start index
    const recoveryStartIndex = getRecoveryModeStartIndex(entries);
    
    // Get all entries after recovery mode started
    const entriesAfterRecovery = entries.slice(recoveryStartIndex);
    
    // Get all losses that occurred after recovery mode started (excluding pattern cells)
    const lossesAfterRecovery = entriesAfterRecovery.filter(entry => 
      entry.result === 'L' && !entry.isPatternCell
    );
    
    console.log(`Recovery mode started at index: ${recoveryStartIndex}`);
    console.log(`Entries after recovery: ${entriesAfterRecovery.length}`);
    console.log(`Losses after recovery: ${lossesAfterRecovery.length}`);
    
    // Count the number of non-pattern cell bets placed after recovery mode started
    const nonPatternBetsAfterRecovery = entriesAfterRecovery.filter(entry => 
      !entry.isPatternCell && entry.betAmount > 0
    ).length;
    
    console.log(`Non-pattern bets after recovery: ${nonPatternBetsAfterRecovery}`);
    
    // Track which "set" of two bets we're on
    const recoveryBetSet = Math.floor(nonPatternBetsAfterRecovery / 2);
    console.log(`Current recovery bet set: ${recoveryBetSet}`);
    
    // Get all non-pattern entries with bets after recovery mode started
    const nonPatternEntriesWithBets = entriesAfterRecovery.filter(entry => 
      !entry.isPatternCell && entry.betAmount > 0
    );
    
    // Organize entries by sets (each set contains 2 bets)
    const betSets = [];
    for (let i = 0; i < nonPatternEntriesWithBets.length; i += 2) {
      const set = nonPatternEntriesWithBets.slice(i, i + 2);
      betSets.push(set);
    }
    
    console.log(`Total bet sets: ${betSets.length}`);
    
    let recoveryBetAmount = 0;
    
    // Case 1: Initial Recovery Mode Calculation (first set)
    if (recoveryBetSet === 0) {
      // Formula: RecoveryBet = ((sum of 3 previous losses) + (RBA*2 for each loss)) ÷ 2
      const lastThreeLosses = getLastNLosses(entries, 3);
      const sumOfLosses = lastThreeLosses.reduce((sum, entry) => sum + entry.betAmount, 0);
      const rbaAddition = raiseBet * 2 * lastThreeLosses.length;
      const totalAmount = sumOfLosses + rbaAddition;
      recoveryBetAmount = Math.floor(totalAmount / 2 / 100) * 100;
      
      console.log(`Initial recovery calculation for 3 consecutive losses:`);
      console.log(`- Last 3 losses: ${lastThreeLosses.map(e => e.betAmount).join(', ')}`);
      console.log(`- Sum of losses: ${sumOfLosses}`);
      console.log(`- RBA*2 for each loss: ${rbaAddition}`);
      console.log(`- Total before division: ${totalAmount}`);
      console.log(`- Final recovery bet (÷2): ${recoveryBetAmount}`);
    }
    // Case 2 or Case 3: Calculate based on losses in the current set
    else if (recoveryBetSet > 0 && betSets.length > 0) {
      // Get the current set (the set we're calculating for)
      const currentSet = betSets[recoveryBetSet - 1];
      
      // Count losses in the current set
      const lossesInCurrentSet = currentSet.filter(entry => entry.result === 'L');
      
      console.log(`Current set entries: ${currentSet.length}`);
      console.log(`Losses in current set: ${lossesInCurrentSet.length}`);
      
      // Case 2: One Loss During Recovery Mode in the current set
      if (lossesInCurrentSet.length === 1) {
        // Formula: RecoveryBet = ((sum of that 1 loss)*2 + (RBA*2 for the loss)) ÷ 2
        const sumOfLosses = lossesInCurrentSet[0].betAmount;
        const doubledSum = sumOfLosses * 2;
        const rbaAddition = raiseBet * 2; // RBA*2 for the loss
        const totalAmount = doubledSum + rbaAddition;
        recoveryBetAmount = Math.floor(totalAmount / 2 / 100) * 100;
        
        console.log(`Recovery calculation for 1 loss during recovery mode:`);
        console.log(`- Loss in current set: ${lossesInCurrentSet[0].betAmount}`);
        console.log(`- Sum of loss * 2: ${doubledSum}`);
        console.log(`- RBA*2 for the loss: ${rbaAddition}`);
        console.log(`- Total before division: ${totalAmount}`);
        console.log(`- Final recovery bet (÷2): ${recoveryBetAmount}`);
      }
      // Case 3: Two Losses During Recovery Mode in the current set
      else if (lossesInCurrentSet.length === 2) {
        // Formula: RecoveryBet = ((sum of those 2 losses)*2 + (RBA*2 for each loss)) ÷ 2
        const sumOfLosses = lossesInCurrentSet.reduce((sum, entry) => sum + entry.betAmount, 0);
        const doubledSum = sumOfLosses * 2;
        const rbaAddition = raiseBet * 2 * lossesInCurrentSet.length; // RBA*2 for each loss
        const totalAmount = doubledSum + rbaAddition;
        recoveryBetAmount = Math.floor(totalAmount / 2 / 100) * 100;
        
        console.log(`Recovery calculation for 2 losses during recovery mode:`);
        console.log(`- Losses in current set: ${lossesInCurrentSet.map(e => e.betAmount).join(', ')}`);
        console.log(`- Sum of losses: ${sumOfLosses}`);
        console.log(`- Sum * 2: ${doubledSum}`);
        console.log(`- RBA*2 for each loss: ${rbaAddition}`);
        console.log(`- Total before division: ${totalAmount}`);
        console.log(`- Final recovery bet (÷2): ${recoveryBetAmount}`);
      }
      // No losses in the current set - use the initial recovery calculation
      else {
        // Formula: RecoveryBet = ((sum of 3 previous losses) + (RBA*2 for each loss)) ÷ 2
        const lastThreeLosses = getLastNLosses(entries, 3);
        const sumOfLosses = lastThreeLosses.reduce((sum, entry) => sum + entry.betAmount, 0);
        const rbaAddition = raiseBet * 2 * lastThreeLosses.length;
        const totalAmount = sumOfLosses + rbaAddition;
        recoveryBetAmount = Math.floor(totalAmount / 2 / 100) * 100;
        
        console.log(`Fallback to initial recovery calculation:`);
        console.log(`- Last 3 losses: ${lastThreeLosses.map(e => e.betAmount).join(', ')}`);
        console.log(`- Sum of losses: ${sumOfLosses}`);
        console.log(`- RBA*2 for each loss: ${rbaAddition}`);
        console.log(`- Total before division: ${totalAmount}`);
        console.log(`- Final recovery bet (÷2): ${recoveryBetAmount}`);
      }
    }
    
    // Special case for Column 2 Row 3 when there was a loss at Column 2 Row 2
    if (columnNumber === 2 && rowNumber === 3) {
      // Find the loss in Column 2 Row 2
      const col2Row2Loss = entries.find(entry => 
        entry.columnNumber === 2 && entry.rowNumber === 2 && entry.result === 'L'
      );
      
      if (col2Row2Loss) {
        // Formula: RecoveryBet = ((sum of that 1 loss)*2 + (RBA*2 for the loss)) ÷ 2
        const sumOfLosses = col2Row2Loss.betAmount;
        const doubledSum = sumOfLosses * 2;
        const rbaAddition = raiseBet * 2; // RBA*2 for the loss
        const totalAmount = doubledSum + rbaAddition;
        recoveryBetAmount = Math.floor(totalAmount / 2 / 100) * 100;
        
        console.log(`Special case for Column 2 Row 3:`);
        console.log(`- Column 2 Row 2 loss: ${sumOfLosses}`);
        console.log(`- Sum of loss * 2: ${doubledSum}`);
        console.log(`- RBA*2 for the loss: ${rbaAddition}`);
        console.log(`- Total before division: ${totalAmount}`);
        console.log(`- Final recovery bet (÷2): ${recoveryBetAmount}`);
      }
    }
    
    // If we haven't calculated a recovery bet amount yet, use the initial formula
    if (recoveryBetAmount === 0) {
      // Formula: RecoveryBet = ((sum of 3 previous losses) + (RBA*2 for each loss)) ÷ 2
      const lastThreeLosses = getLastNLosses(entries, 3);
      const sumOfLosses = lastThreeLosses.reduce((sum, entry) => sum + entry.betAmount, 0);
      const rbaAddition = raiseBet * 2 * lastThreeLosses.length;
      const totalAmount = sumOfLosses + rbaAddition;
      recoveryBetAmount = Math.floor(totalAmount / 2 / 100) * 100;
      
      console.log(`Fallback to initial recovery calculation:`);
      console.log(`- Last 3 losses: ${lastThreeLosses.map(e => e.betAmount).join(', ')}`);
      console.log(`- Sum of losses: ${sumOfLosses}`);
      console.log(`- RBA*2 for each loss: ${rbaAddition}`);
      console.log(`- Total before division: ${totalAmount}`);
      console.log(`- Final recovery bet (÷2): ${recoveryBetAmount}`);
    }
    
    return recoveryBetAmount;
  }
  
  // If not in recovery mode, check if we have 3 consecutive losses to activate it
  const consecutiveLosses = getConsecutiveLosses(entries);
  console.log(`Consecutive losses: ${consecutiveLosses}`);
  
  if (consecutiveLosses >= 3) {
    console.log(`Activating recovery mode after ${consecutiveLosses} consecutive losses`);
    
    // Calculate the recovery bet amount using the formula:
    // ((sum of 3 previous losses) + (RBA*2 for each loss)) ÷ 2
    const lastThreeLosses = getLastNLosses(entries, 3);
    const sumOfLosses = lastThreeLosses.reduce((sum, entry) => sum + entry.betAmount, 0);
    const rbaAddition = raiseBet * 2 * lastThreeLosses.length;
    const recoveryBetAmount = Math.floor((sumOfLosses + rbaAddition) / 2 / 100) * 100;
    
    console.log(`Initial recovery calculation for 3 consecutive losses:`);
    console.log(`- Last 3 losses: ${lastThreeLosses.map(e => e.betAmount).join(', ')}`);
    console.log(`- Sum of losses: ${sumOfLosses}`);
    console.log(`- RBA*2 for each loss: ${rbaAddition}`);
    console.log(`- Total before division: ${sumOfLosses + rbaAddition}`);
    console.log(`- Final recovery bet (÷2): ${recoveryBetAmount}`);
    
    // Mark the current entry as the start of recovery mode
    entries[entries.length - 1].recoveryModeStart = true;
    
    return recoveryBetAmount;
  }

  // If not in recovery mode, continue with normal Extend-2-1-1 logic
  return calculateNormalExtend211Bet(entries, baseBet, raiseBet, rowCount, extensionFactor);
}

/**
 * Calculate the normal Extend-2-1-1 bet (when not in recovery mode)
 */
function calculateNormalExtend211Bet(
  entries: GameEntry[],
  baseBet: number,
  raiseBet: number,
  rowCount: number,
  extensionFactor: ExtensionFactor
): number {
  if (entries.length === 0) {
    return baseBet;
  }

  // Check if we just exited recovery mode
  const lastEntry = entries[entries.length - 1];
  const justExitedRecovery = lastEntry.recoveryModeEnd === true;
  
  // If we just exited recovery mode, start fresh with base bet
  if (justExitedRecovery) {
    console.log(`Just exited recovery mode, starting fresh with base bet: ${baseBet}`);
    return baseBet;
  }
  
  const nextPosition = calculateNextPositionForExtend211Recovery(entries, rowCount, extensionFactor);
  const { columnNumber, rowNumber } = nextPosition;
  
  console.log(`Calculating next bet for Extend-2-1-1-Recovery logic`);
  console.log(`Next position: Column ${columnNumber}, Row ${rowNumber}`);
  
  // If this is a pattern cell, return 0 (No Bet)
  if (isPatternCell(rowNumber)) {
    console.log(`Row ${rowNumber} is a pattern cell (No Bet)`);
    return 0;
  }
  
  // Get the recovery mode end index (if any)
  const recoveryEndIndex = getRecoveryModeEndIndex(entries);
  const hasExitedRecovery = recoveryEndIndex !== -1;
  
  // Start with the raise bet amount
  let result = raiseBet;
  
  // Row 2 calculation
  if (rowNumber === 2) {
    console.log(`Row 2 calculation - Starting with RBA: ${raiseBet}`);
    
    // SRPC (Same Row Previous Column) component
    if (columnNumber > 1) {
      const srpcValue = getSameRowPreviousColumnLoss(entries, columnNumber, rowNumber);
      
      // If we've exited recovery mode, don't consider previous column losses
      if (hasExitedRecovery && recoveryEndIndex >= entries.findIndex(e => e.columnNumber === columnNumber - 1)) {
        console.log(`Skipping SRPC component because we've exited recovery mode and are in a new column`);
      } else if (srpcValue > 0) {
        // Check if there was a win between this loss and now
        const wasWin = wasSameRowPreviousColumnWin(entries, columnNumber, rowNumber);
        
        if (wasWin) {
          console.log(`SRPC (Row ${rowNumber}, Col ${columnNumber-1}) had a win after the loss - not considering this loss`);
        } else {
          result += (srpcValue + raiseBet);
          console.log(`SRPC (Row ${rowNumber}, Col ${columnNumber-1}) was a LOSS: Adding (${srpcValue} + ${raiseBet}) = ${srpcValue + raiseBet}`);
        }
      } else {
        console.log(`No SRPC (Row ${rowNumber}, Col ${columnNumber-1}) loss found`);
      }
    }
    
    // PCLRL (Previous Column Last Row Loss) component
    if (columnNumber > 1) {
      const lastRowInPrevCol = getLastRowNumberInColumn(entries, columnNumber - 1);
      const prevColLastRow = entries.find(
        entry => entry.columnNumber === columnNumber - 1 && 
                entry.rowNumber === lastRowInPrevCol && 
                !entry.isPatternCell
      );
      
      // If we've exited recovery mode, don't consider previous column losses
      if (hasExitedRecovery && recoveryEndIndex >= entries.findIndex(e => e.columnNumber === columnNumber - 1)) {
        console.log(`Skipping PCLRL component because we've exited recovery mode and are in a new column`);
      } else if (prevColLastRow && prevColLastRow.result === 'L') {
        const pclrlValue = prevColLastRow.betAmount + raiseBet;
        result += pclrlValue;
        console.log(`PCLRL (Row ${lastRowInPrevCol}, Col ${columnNumber-1}) was a LOSS: Adding (${prevColLastRow.betAmount} + ${raiseBet}) = ${pclrlValue}`);
      } else if (prevColLastRow && prevColLastRow.result === 'W') {
        console.log(`PCLRL (Row ${lastRowInPrevCol}, Col ${columnNumber-1}) was a WIN: Not adding PCLRL component`);
      } else {
        console.log(`PCLRL (Row ${lastRowInPrevCol}, Col ${columnNumber-1}) was neither win nor loss (${prevColLastRow?.result || 'not found'})`);
      }
    }
    
    console.log(`Final Row 2 bet calculation: ${result}`);
    return result;
  }
  
  // Row 3 calculation
  else if (rowNumber === 3) {
    console.log(`Column ${columnNumber}, Row 3: Starting with RBA: ${raiseBet}`);
    
    // PLSC (Previous Loss in Same Column) component - specifically Row 2 loss
    const row2LossInCurrentCol = entries.find(
      entry => entry.columnNumber === columnNumber && 
              entry.rowNumber === 2 && 
              !entry.isPatternCell
    );
    
    // If we've exited recovery mode, only consider immediate consecutive losses
    if (hasExitedRecovery) {
      // Check if there was a win between the Row 2 loss and now
      const wasWinAfterRow2 = entries.some(
        entry => entry.columnNumber === columnNumber && 
                entry.rowNumber > 2 && 
                entry.rowNumber < 3 && 
                entry.result === 'W' && 
                !entry.isPatternCell
      );
      
      if (wasWinAfterRow2) {
        console.log(`There was a win after Row 2 loss - not considering this loss after recovery mode exit`);
      } else if (row2LossInCurrentCol && row2LossInCurrentCol.result === 'L') {
        result += (row2LossInCurrentCol.betAmount + raiseBet);
        console.log(`PLSC (Row 2, Col ${columnNumber}) was a LOSS: Adding (${row2LossInCurrentCol.betAmount} + ${raiseBet}) = ${row2LossInCurrentCol.betAmount + raiseBet}`);
      }
    } else if (row2LossInCurrentCol && row2LossInCurrentCol.result === 'L') {
      result += (row2LossInCurrentCol.betAmount + raiseBet);
      console.log(`PLSC (Row 2, Col ${columnNumber}) was a LOSS: Adding (${row2LossInCurrentCol.betAmount} + ${raiseBet}) = ${row2LossInCurrentCol.betAmount + raiseBet}`);
    }
    
    console.log(`Final Row 3 bet calculation: ${result}`);
    return result;
  }
  
  // Row 5 calculation
  else if (rowNumber === 5) {
    console.log(`Row 5 calculation - Corrected formula: (Row 3 Loss + RBA) + (SRPC + RBA) + RBA`);
    console.log(`Starting with RBA: ${raiseBet}`);
    
    // Row 3 Loss component
    const row3LossInCurrentCol = entries.find(
      entry => entry.columnNumber === columnNumber && 
              entry.rowNumber === 3 && 
              !entry.isPatternCell
    );
    
    // If we've exited recovery mode, only consider immediate consecutive losses
    if (hasExitedRecovery) {
      // Check if there was a win between the Row 3 loss and now
      const wasWinAfterRow3 = entries.some(
        entry => entry.columnNumber === columnNumber && 
                entry.rowNumber > 3 && 
                entry.rowNumber < 5 && 
                entry.result === 'W' && 
                !entry.isPatternCell
      );
      
      if (wasWinAfterRow3) {
        console.log(`There was a win after Row 3 loss - not considering this loss after recovery mode exit`);
      } else if (row3LossInCurrentCol && row3LossInCurrentCol.result === 'L') {
        result += (row3LossInCurrentCol.betAmount + raiseBet);
        console.log(`Found Row 3 loss in current column: ${row3LossInCurrentCol.betAmount}`);
        console.log(`Adding (Row 3 Loss + RBA): ${row3LossInCurrentCol.betAmount + raiseBet} (${row3LossInCurrentCol.betAmount} + ${raiseBet})`);
      }
    } else if (row3LossInCurrentCol && row3LossInCurrentCol.result === 'L') {
      result += (row3LossInCurrentCol.betAmount + raiseBet);
      console.log(`Found Row 3 loss in current column: ${row3LossInCurrentCol.betAmount}`);
      console.log(`Adding (Row 3 Loss + RBA): ${row3LossInCurrentCol.betAmount + raiseBet} (${row3LossInCurrentCol.betAmount} + ${raiseBet})`);
    }
    
    // SRPC (Same Row Previous Column) component
    if (columnNumber > 1) {
      const srpcValue = getSameRowPreviousColumnLoss(entries, columnNumber, rowNumber);
      
      // If we've exited recovery mode, don't consider previous column losses
      if (hasExitedRecovery && recoveryEndIndex >= entries.findIndex(e => e.columnNumber === columnNumber - 1)) {
        console.log(`Skipping SRPC component because we've exited recovery mode and are in a new column`);
      } else if (srpcValue > 0) {
        // Check if there was a win between this loss and now
        const wasWin = wasSameRowPreviousColumnWin(entries, columnNumber, rowNumber);
        
        if (wasWin) {
          console.log(`SRPC (Row ${rowNumber}, Col ${columnNumber-1}) had a win after the loss - not considering this loss`);
        } else {
          result += (srpcValue + raiseBet);
          console.log(`SRPC (Row ${rowNumber}, Col ${columnNumber-1}) was a LOSS: Adding (${srpcValue} + ${raiseBet}) = ${srpcValue + raiseBet}`);
        }
      } else {
        console.log(`No SRPC (Row ${rowNumber}, Col ${columnNumber-1}) loss found`);
      }
    }
    
    console.log(`Final Row 5 bet calculation: ${result}`);
    return result;
  }
  
  // Row 7 calculation
  else if (rowNumber === 7) {
    console.log(`Row 7 calculation - Starting with RBA: ${raiseBet}`);
    
    // Row 5 Loss component
    const row5LossInCurrentCol = entries.find(
      entry => entry.columnNumber === columnNumber && 
              entry.rowNumber === 5 && 
              !entry.isPatternCell
    );
    
    // If we've exited recovery mode, only consider immediate consecutive losses
    if (hasExitedRecovery) {
      // Check if there was a win between the Row 5 loss and now
      const wasWinAfterRow5 = entries.some(
        entry => entry.columnNumber === columnNumber && 
                entry.rowNumber > 5 && 
                entry.rowNumber < 7 && 
                entry.result === 'W' && 
                !entry.isPatternCell
      );
      
      if (wasWinAfterRow5) {
        console.log(`There was a win after Row 5 loss - not considering this loss after recovery mode exit`);
      } else if (row5LossInCurrentCol && row5LossInCurrentCol.result === 'L') {
        result += (row5LossInCurrentCol.betAmount + raiseBet);
        console.log(`Found Row 5 loss in current column: ${row5LossInCurrentCol.betAmount}`);
        console.log(`Adding (Row 5 Loss + RBA): ${row5LossInCurrentCol.betAmount + raiseBet} (${row5LossInCurrentCol.betAmount} + ${raiseBet})`);
      }
    } else if (row5LossInCurrentCol && row5LossInCurrentCol.result === 'L') {
      result += (row5LossInCurrentCol.betAmount + raiseBet);
      console.log(`Found Row 5 loss in current column: ${row5LossInCurrentCol.betAmount}`);
      console.log(`Adding (Row 5 Loss + RBA): ${row5LossInCurrentCol.betAmount + raiseBet} (${row5LossInCurrentCol.betAmount} + ${raiseBet})`);
    }
    
    console.log(`Final Row 7 bet calculation: ${result}`);
    return result;
  }
  
  // Default case - just return the raise bet amount
  return raiseBet;
}

/**
 * Determine the result of a hand specifically for Extend-2-1-1-Recovery logic.
 * This function implements the win/loss identification rules as specified:
 * - Row 1, Row 4, and Row 6 are pattern cells (No Bet)
 * - Row 2 & Row 3 follow pattern from Row 1 (Win if same, Loss if different)
 * - Row 5 follows pattern from Row 4 (Win if same, Loss if different)
 * - Row 7 follows pattern from Row 6 (Win if same, Loss if different)
 */
export function determineExtend211RecoveryResult(
  handValue: 'P' | 'B',
  columnNumber: number,
  rowNumber: number,
  entries: GameEntry[],
  extensionFactor?: ExtensionFactor
): 'W' | 'L' | 'N' {
  // Pattern cells (Row 1, Row 4, and Row 6) are always 'N' (No Bet)
  if (isPatternCell(rowNumber)) {
    console.log(`determineExtend211RecoveryResult: Row ${rowNumber} is a pattern cell (No Bet)`);
    return 'N';
  }
  
  // For non-pattern cells, we need to check the pattern cell value
  let patternRow: number;
  
  if (rowNumber === 2 || rowNumber === 3) {
    // Rows 2 and 3 follow Row 1's pattern
    patternRow = 1;
  } else if (rowNumber === 5) {
    // Row 5 follows Row 4's pattern
    patternRow = 4;
  } else if (rowNumber === 7) {
    // Row 7 follows Row 6's pattern
    patternRow = 6;
  } else {
    console.log(`determineExtend211RecoveryResult: No rule defined for Row ${rowNumber} - Result is N`);
    return 'N';
  }
  
  // Find the pattern cell in the current column
  const patternCell = entries.find(
    entry => entry.columnNumber === columnNumber && 
            entry.rowNumber === patternRow
  );
  
  // If pattern cell doesn't exist, we can't determine the result
  if (!patternCell) {
    console.log(`determineExtend211RecoveryResult: No pattern cell found for Row ${patternRow}, Column ${columnNumber} - Result is N`);
    return 'N';
  }
  
  // Compare hand value with pattern cell value
  // Win if they match, Loss if they don't
  const result = handValue === patternCell.handValue ? 'W' : 'L';
  console.log(`determineExtend211RecoveryResult: For Row ${rowNumber}, comparing with Row ${patternRow} pattern (${patternCell.handValue}), hand value ${handValue} - Result is ${result}`);
  return result;
}

/**
 * Get detailed information about the next bet calculation for Extend-2-1-1-Recovery logic.
 * This is used by useBetCalculationDetails to display bet evidence.
 */
export function getExtend211RecoveryNextBetDetails(
  entries: GameEntry[],
  baseBet: number,
  raiseBet: number,
  rowCount: number,
  extensionFactor: ExtensionFactor
): {
  baseBet: number;
  raiseBet: number;
  srpcValue: number;
  pclrlValue: number;
  plscValue: number;
  row1Value: number;
  lossCount: number;
} {
  if (entries.length === 0) {
    return {
      baseBet,
      raiseBet,
      srpcValue: 0,
      pclrlValue: 0,
      plscValue: 0,
      row1Value: 0,
      lossCount: 0
    };
  }

  // Check if we're in recovery mode
  const isInRecoveryMode = checkIfInRecoveryMode(entries);
  if (isInRecoveryMode) {
    // In recovery mode, we don't show the standard calculation details
    return {
      baseBet,
      raiseBet,
      srpcValue: 0,
      pclrlValue: 0,
      plscValue: 0,
      row1Value: 0,
      lossCount: 0
    };
  }
  
  const nextPosition = calculateNextPositionForExtend211Recovery(entries, rowCount, extensionFactor);
  const { columnNumber, rowNumber } = nextPosition;
  
  console.log(`getExtend211RecoveryNextBetDetails: Next position is Column ${columnNumber}, Row ${rowNumber}`);
  
  // Check if it's a pattern cell (Row 1 or Row 4)
  if (isPatternCell(rowNumber)) {
    console.log(`getExtend211RecoveryNextBetDetails: Row ${rowNumber} is a pattern cell (No Bet)`);
    return {
      baseBet,
      raiseBet,
      srpcValue: 0,
      pclrlValue: 0,
      plscValue: 0,
      row1Value: 0,
      lossCount: 0
    };
  }

  // Initialize values
  let srpcValue = 0;
  let pclrlValue = 0;
  let plscValue = 0;

  // Row 2 calculation
  if (rowNumber === 2) {
    if (columnNumber > 1) {
      // SRPC (Same Row Previous Column) component
      const sameRowPrevCol = entries.find(
        entry => entry.columnNumber === columnNumber - 1 && 
                entry.rowNumber === 2 && 
                !entry.isPatternCell
      );
      
      if (sameRowPrevCol && sameRowPrevCol.result === 'L') {
        srpcValue = sameRowPrevCol.betAmount;
        console.log(`getExtend211RecoveryNextBetDetails: SRPC for Row 2 = ${srpcValue}`);
      }
      
      // PCLRL (Previous Column's Last Row) component
      const lastRowInPrevCol = getLastRowNumberInColumn(entries, columnNumber - 1);
      const prevColLastRow = entries.find(
        entry => entry.columnNumber === columnNumber - 1 && 
                entry.rowNumber === lastRowInPrevCol && 
                !entry.isPatternCell
      );
      
      if (prevColLastRow && prevColLastRow.result === 'L') {
        pclrlValue = prevColLastRow.betAmount;
        console.log(`getExtend211RecoveryNextBetDetails: PCLRL for Row 2 = ${pclrlValue}`);
      }
    }
  }
  
  // Row 3 calculation
  else if (rowNumber === 3) {
    if (columnNumber > 1) {
      // SRPC (Same Row Previous Column) component
      const sameRowPrevCol = entries.find(
        entry => entry.columnNumber === columnNumber - 1 && 
                entry.rowNumber === 3 && 
                !entry.isPatternCell
      );
      
      if (sameRowPrevCol && sameRowPrevCol.result === 'L') {
        srpcValue = sameRowPrevCol.betAmount;
        console.log(`getExtend211RecoveryNextBetDetails: SRPC for Row 3 = ${srpcValue}`);
      }
    }
    
    // PLSC (Previous Loss in Same Column) component - specifically Row 2 loss
    const row2LossInCurrentCol = entries.find(
      entry => entry.columnNumber === columnNumber && 
              entry.rowNumber === 2 && 
              !entry.isPatternCell
    );
    
    if (row2LossInCurrentCol && row2LossInCurrentCol.result === 'L') {
      plscValue = row2LossInCurrentCol.betAmount;
      console.log(`getExtend211RecoveryNextBetDetails: PLSC (Row 2 loss) for Row 3 = ${plscValue}`);
    }
  }
  
  // Row 5 calculation
  else if (rowNumber === 5) {
    if (columnNumber > 1) {
      // SRPC (Same Row Previous Column) component
      const sameRowPrevCol = entries.find(
        entry => entry.columnNumber === columnNumber - 1 && 
                entry.rowNumber === 5 && 
                !entry.isPatternCell
      );
      
      if (sameRowPrevCol && sameRowPrevCol.result === 'L') {
        srpcValue = sameRowPrevCol.betAmount;
        console.log(`getExtend211RecoveryNextBetDetails: SRPC for Row 5 = ${srpcValue}`);
      }
    }
    
    // PLSC (Previous Loss in Same Column) component - specifically Row 3 loss
    const row3LossInCurrentCol = entries.find(
      entry => entry.columnNumber === columnNumber && 
              entry.rowNumber === 3 && 
              !entry.isPatternCell
    );
    
    if (row3LossInCurrentCol && row3LossInCurrentCol.result === 'L') {
      plscValue = row3LossInCurrentCol.betAmount;
      console.log(`getExtend211RecoveryNextBetDetails: PLSC (Row 3 loss) for Row 5 = ${plscValue}`);
    }
  }
  
  // Row 7 calculation
  else if (rowNumber === 7) {
    if (columnNumber > 1) {
      // SRPC (Same Row Previous Column) component
      const sameRowPrevCol = entries.find(
        entry => entry.columnNumber === columnNumber - 1 && 
                entry.rowNumber === 7 && 
                !entry.isPatternCell
      );
      
      if (sameRowPrevCol && sameRowPrevCol.result === 'L') {
        srpcValue = sameRowPrevCol.betAmount;
        console.log(`getExtend211RecoveryNextBetDetails: SRPC for Row 7 = ${srpcValue}`);
      }
    }
    
    // PLSC (Previous Loss in Same Column) component - specifically Row 5 loss
    const row5LossInCurrentCol = entries.find(
      entry => entry.columnNumber === columnNumber && 
              entry.rowNumber === 5 && 
              !entry.isPatternCell
    );
    
    if (row5LossInCurrentCol && row5LossInCurrentCol.result === 'L') {
      plscValue = row5LossInCurrentCol.betAmount;
      console.log(`getExtend211RecoveryNextBetDetails: PLSC (Row 5 loss) for Row 7 = ${plscValue}`);
    }
  }

  console.log(`getExtend211RecoveryNextBetDetails: Final values - SRPC: ${srpcValue}, PCLRL: ${pclrlValue}, PLSC: ${plscValue}`);
  
  return {
    baseBet,
    raiseBet,
    srpcValue,
    pclrlValue,
    plscValue,
    row1Value: 0,
    lossCount: 0
  };
}

// Helper Functions

/**
 * Get the last row number in a specific column
 */
export function getLastRowNumberInColumn(
  entries: GameEntry[],
  columnNumber: number
): number {
  // Filter entries for the specified column
  const columnEntries = entries.filter(entry => entry.columnNumber === columnNumber);
  
  // If no entries found, return 0
  if (columnEntries.length === 0) {
    return 0;
  }
  
  // Find the maximum row number
  return Math.max(...columnEntries.map(entry => entry.rowNumber));
}

/**
 * Check if a position has a previous loss in the column
 */
export function hasPreviousLossInColumn(
  entries: GameEntry[],
  columnNumber: number,
  rowNumber: number
): boolean {
  // Check if there's any loss in the same column with a lower row number
  return entries.some(
    entry => entry.columnNumber === columnNumber && 
            entry.rowNumber < rowNumber && 
            !entry.isPatternCell &&
            entry.result === 'L'
  );
}

/**
 * Get the previous loss in the column (any row before the current one)
 */
export function getPreviousLossInColumn(
  entries: GameEntry[],
  columnNumber: number,
  rowNumber: number
): number {
  // Find all losses in the same column with a lower row number
  const previousLosses = entries.filter(
    entry => entry.columnNumber === columnNumber && 
            entry.rowNumber < rowNumber && 
            !entry.isPatternCell &&
            entry.result === 'L'
  );
  
  // If no previous losses, return 0
  if (previousLosses.length === 0) {
    return 0;
  }
  
  // Find the most recent loss (highest row number)
  const mostRecentLoss = previousLosses.reduce(
    (prev, current) => (prev.rowNumber > current.rowNumber) ? prev : current
  );
  
  return mostRecentLoss.betAmount;
}

/**
 * Get the previous loss in the column but specifically for a given row
 */
export function getPreviousRowLossInColumn(
  entries: GameEntry[],
  columnNumber: number,
  rowNumber: number,
  specificRow: number
): number {
  // Find the loss for the specific row in the current column
  const specificRowLoss = entries.find(
    entry => entry.columnNumber === columnNumber && 
            entry.rowNumber === specificRow && 
            !entry.isPatternCell &&
            entry.result === 'L'
  );
  
  // If no specific row loss found, return 0
  if (!specificRowLoss) {
    return 0;
  }
  
  return specificRowLoss.betAmount;
}

/**
 * Get a loss from Row 3 in the current column
 */
export function getRow3LossInCurrentColumn(
  entries: GameEntry[],
  columnNumber: number
): number {
  // Find the Row 3 entry for the current column
  const row3Entry = entries.find(
    entry => entry.columnNumber === columnNumber && 
            entry.rowNumber === 3 && 
            entry.result === 'L'
  );
  
  // If it exists and is a loss, return its bet amount
  if (row3Entry) {
    return row3Entry.betAmount;
  }
  
  return 0;
}

/**
 * Get a loss from Row 2 in the previous column
 */
export function getPreviousColumnRow2Loss(
  entries: GameEntry[],
  columnNumber: number
): number {
  if (columnNumber <= 1) return 0;
  
  // Find the Row 2 entry for the previous column
  const row2Entry = entries.find(
    entry => entry.columnNumber === columnNumber - 1 && 
            entry.rowNumber === 2 && 
            !entry.isPatternCell &&
            entry.result === 'L'
  );
  
  // If it exists and is a loss, return its bet amount
  if (row2Entry) {
    return row2Entry.betAmount;
  }
  
  return 0;
}

/**
 * Get a loss from Row 3 in the previous column
 */
export function getPreviousColumnRow3Loss(
  entries: GameEntry[],
  columnNumber: number
): number {
  if (columnNumber <= 1) return 0;
  
  // Find the Row 3 entry for the previous column
  const row3Entry = entries.find(
    entry => entry.columnNumber === columnNumber - 1 && 
            entry.rowNumber === 3 && 
            !entry.isPatternCell &&
            entry.result === 'L'
  );
  
  // If it exists and is a loss, return its bet amount
  if (row3Entry) {
    return row3Entry.betAmount;
  }
  
  return 0;
}

/**
 * Get a loss from Row 5 in the previous column
 */
export function getPreviousColumnRow5Loss(
  entries: GameEntry[],
  columnNumber: number
): number {
  if (columnNumber <= 1) return 0;
  
  // Find the Row 5 entry for the previous column
  const row5Entry = entries.find(
    entry => entry.columnNumber === columnNumber - 1 && 
            entry.rowNumber === 5 && 
            !entry.isPatternCell &&
            entry.result === 'L'
  );
  
  // If it exists and is a loss, return its bet amount
  if (row5Entry) {
    return row5Entry.betAmount;
  }
  
  return 0;
}

/**
 * Get a loss from Row 5 in the current column
 */
export function getRow5LossInCurrentColumn(
  entries: GameEntry[],
  columnNumber: number
): number {
  // Find the Row 5 entry for the current column
  const row5Entry = entries.find(
    entry => entry.columnNumber === columnNumber && 
            entry.rowNumber === 5 && 
            entry.result === 'L'
  );
  
  // If it exists and is a loss, return its bet amount
  if (row5Entry) {
    return row5Entry.betAmount;
  }
  
  return 0;
}

/**
 * Get the same row's previous column loss
 */
export function getSameRowPreviousColumnLoss(
  entries: GameEntry[],
  columnNumber: number,
  rowNumber: number
): number {
  if (columnNumber <= 1) return 0;
  
  const sameRowPrevCol = entries.find(
    entry => entry.columnNumber === columnNumber - 1 && 
            entry.rowNumber === rowNumber && 
            !entry.isPatternCell &&
            entry.result === 'L'
  );
  
  return sameRowPrevCol ? sameRowPrevCol.betAmount : 0;
}

/**
 * Check if the same row in the previous column was a win
 */
export function wasSameRowPreviousColumnWin(
  entries: GameEntry[],
  columnNumber: number,
  rowNumber: number
): boolean {
  if (columnNumber <= 1) return false;
  
  const entry = entries.find(
    e => e.columnNumber === columnNumber - 1 && e.rowNumber === rowNumber && !e.isPatternCell
  );
  
  return entry?.result === 'W';
}

/**
 * Check if a position was a win
 */
export function wasPositionWin(
  entries: GameEntry[],
  columnNumber: number,
  rowNumber: number
): boolean {
  const entry = entries.find(
    e => e.columnNumber === columnNumber && e.rowNumber === rowNumber && !e.isPatternCell
  );
  
  return entry?.result === 'W';
}

/**
 * Check if a row is a pattern cell
 */
export function isPatternCell(rowNumber: number): boolean {
  return rowNumber === 1 || rowNumber === 4 || rowNumber === 6;
}

/**
 * Get the pattern section for a row
 * Returns 1 for rows 1-3, 2 for rows 4-5, 3 for rows 6-7
 */
export function getPatternSection(rowNumber: number): number {
  if (rowNumber <= 3) {
    return 1; // First section (rows 1-3)
  } else if (rowNumber <= 5) {
    return 2; // Second section (rows 4-5)
  } else {
    return 3; // Third section (rows 6-7)
  }
}

/**
 * Calculate the next position for Extend-2-1-1-Recovery logic
 */
export function calculateNextPositionForExtend211Recovery(
  entries: GameEntry[],
  rowCount: number,
  extensionFactor: ExtensionFactor
): { columnNumber: number; rowNumber: number } {
  if (entries.length === 0) {
    return { columnNumber: 1, rowNumber: 1 };
  }

  const lastEntry = entries[entries.length - 1];
  let nextColumnNumber = lastEntry.columnNumber;
  let nextRowNumber = lastEntry.rowNumber + 1;

  // For Extend-2-1-1-Recovery, we always use 7 rows regardless of the rowCount and extensionFactor
  const effectiveRowCount = 7;
  
  // Check if we need to move to the next column
  if (nextRowNumber > effectiveRowCount) {
    nextColumnNumber++;
    nextRowNumber = 1;
  }

  // Skip pattern cells for auto-population
  if (isPatternCell(nextRowNumber)) {
    // If we're on Row 6, we need to ensure we don't skip to the next column yet
    if (nextRowNumber === 6) {
      console.log(`calculateNextPositionForExtend211Recovery: Auto-populating pattern cell at Row ${nextRowNumber}`);
      // We'll auto-populate this pattern cell, so we don't need to skip it
    }
  }

  return { columnNumber: nextColumnNumber, rowNumber: nextRowNumber };
}

/**
 * Get the number of consecutive losses at the end of the entries array
 * This counts across columns, not just within the same column
 */
export function getConsecutiveLosses(entries: GameEntry[]): number {
  if (entries.length === 0) return 0;
  
  let count = 0;
  for (let i = entries.length - 1; i >= 0; i--) {
    // Only count non-pattern cells with a loss result
    if (entries[i].result === 'L' && !entries[i].isPatternCell) {
      count++;
    } else if (entries[i].result !== 'N') {
      // Stop counting if we encounter a non-loss (except for No Bet)
      break;
    }
    // Skip pattern cells (No Bet) and continue counting
  }
  
  return count;
}

/**
 * Get the number of consecutive wins at the end of the entries array
 * This counts across columns, not just within the same column
 */
export function getConsecutiveWins(entries: GameEntry[]): number {
  if (entries.length === 0) return 0;
  
  let count = 0;
  for (let i = entries.length - 1; i >= 0; i--) {
    // Only count non-pattern cells with a win result
    if (entries[i].result === 'W' && !entries[i].isPatternCell) {
      count++;
    } else if (entries[i].result !== 'N') {
      // Stop counting if we encounter a non-win (except for No Bet)
      break;
    }
    // Skip pattern cells (No Bet) and continue counting
  }
  
  return count;
}

/**
 * Get the last N losses from the entries array
 * This looks across columns, not just within the same column
 */
export function getLastNLosses(entries: GameEntry[], n: number): GameEntry[] {
  if (entries.length === 0) return [];
  
  const losses: GameEntry[] = [];
  for (let i = entries.length - 1; i >= 0; i--) {
    // Only include non-pattern cells with a loss result
    if (entries[i].result === 'L' && !entries[i].isPatternCell) {
      losses.push(entries[i]);
      if (losses.length === n) break;
    }
    // Skip pattern cells and non-losses
  }
  
  return losses.reverse();
}

/**
 * Get the last N losses before a specific index
 */
export function getLastNLossesBeforeIndex(entries: GameEntry[], n: number, beforeIndex: number): GameEntry[] {
  if (entries.length === 0 || beforeIndex < 0) return [];
  
  const losses: GameEntry[] = [];
  for (let i = beforeIndex; i >= 0; i--) {
    // Only include non-pattern cells with a loss result
    if (entries[i].result === 'L' && !entries[i].isPatternCell) {
      losses.push(entries[i]);
      if (losses.length === n) break;
    }
    // Skip pattern cells and non-losses
  }
  
  return losses.reverse();
}

/**
 * Helper function to get the last N consecutive losses in the current column
 */
export function getLastNConsecutiveLossesInCurrentColumn(
  entries: GameEntry[],
  n: number
): GameEntry[] {
  if (entries.length === 0) return [];
  
  // Get the current column
  const currentColumn = entries[entries.length - 1].columnNumber;
  
  // Start from the end and collect consecutive losses
  const consecutiveLosses: GameEntry[] = [];
  for (let i = entries.length - 1; i >= 0; i--) {
    const entry = entries[i];
    
    // Only consider entries in the current column
    if (entry.columnNumber !== currentColumn) {
      break;
    }
    
    // If we find a win, break the consecutive loss streak
    if (entry.result === 'W') {
      break;
    }
    
    // Add losses to our collection
    if (entry.result === 'L') {
      consecutiveLosses.unshift(entry); // Add to the beginning to maintain order
      
      // If we have enough losses, stop
      if (consecutiveLosses.length >= n) {
        break;
      }
    }
  }
  
  // Return the last N consecutive losses (or fewer if not enough)
  return consecutiveLosses.slice(-n);
}

/**
 * Helper function to get losses after a specific index, but only in the current column
 * and only consecutive losses (no wins in between)
 */
export function getLossesAfterIndexInCurrentColumn(
  entries: GameEntry[],
  startIndex: number
): GameEntry[] {
  if (entries.length === 0 || startIndex < 0 || startIndex >= entries.length) return [];
  
  // Get the current column
  const currentColumn = entries[entries.length - 1].columnNumber;
  
  // Start from the end and collect consecutive losses
  const consecutiveLosses: GameEntry[] = [];
  for (let i = entries.length - 1; i > startIndex; i--) {
    const entry = entries[i];
    
    // Only consider entries in the current column
    if (entry.columnNumber !== currentColumn) {
      break;
    }
    
    // If we find a win, break the consecutive loss streak
    if (entry.result === 'W') {
      break;
    }
    
    // Add losses to our collection
    if (entry.result === 'L') {
      consecutiveLosses.unshift(entry); // Add to the beginning to maintain order
    }
  }
  
  return consecutiveLosses;
}

/**
 * Check if we're currently in recovery mode
 */
export function checkIfInRecoveryMode(entries: GameEntry[]): boolean {
  if (entries.length === 0) return false;
  
  // Check if any entry has recoveryModeStart flag
  const recoveryStartIndex = getRecoveryModeStartIndex(entries);
  if (recoveryStartIndex === -1) return false;
  
  // Check if any entry after the recovery start has recoveryModeEnd flag
  const recoveryEndIndex = entries.findIndex((entry, index) => 
    index > recoveryStartIndex && entry.recoveryModeEnd === true
  );
  
  // If we found an end index, we're no longer in recovery mode
  if (recoveryEndIndex !== -1) return false;
  
  return true;
}

/**
 * Get the index of the entry that started the current recovery mode
 */
export function getRecoveryModeStartIndex(entries: GameEntry[]): number {
  if (entries.length === 0) return -1;
  
  // Find the last recovery mode start
  for (let i = entries.length - 1; i >= 0; i--) {
    if (entries[i].recoveryModeStart === true) {
      return i;
    }
    
    // If we find an end before finding a start, we're not in recovery mode
    if (entries[i].recoveryModeEnd === true) {
      return -1;
    }
  }
  
  return -1;
}

/**
 * Get losses that occurred after a specific index in the entries array
 */
export function getLossesAfterIndex(entries: GameEntry[], startIndex: number): GameEntry[] {
  if (entries.length === 0 || startIndex < 0 || startIndex >= entries.length) return [];
  
  return entries.slice(startIndex + 1).filter(entry => 
    entry.result === 'L' && !entry.isPatternCell
  );
}

/**
 * Count how many bets have been made with the same amount
 */
function countBetsWithSameAmount(entries: GameEntry[]): number {
  if (entries.length === 0) return 0;
  
  // Get the last non-zero bet amount
  const lastNonZeroBets = entries.filter(entry => entry.betAmount > 0);
  if (lastNonZeroBets.length === 0) return 0;
  
  const lastBetAmount = lastNonZeroBets[lastNonZeroBets.length - 1].betAmount;
  
  // Count how many consecutive bets at the end have this same amount
  let count = 0;
  for (let i = lastNonZeroBets.length - 1; i >= 0; i--) {
    if (lastNonZeroBets[i].betAmount === lastBetAmount) {
      count++;
    } else {
      break;
    }
  }
  
  return count;
}

/**
 * Get the last N consecutive losses before a specific index
 */
function getLastConsecutiveLosses(
  entries: GameEntry[],
  n: number,
  beforeIndex: number
): GameEntry[] {
  if (entries.length === 0 || beforeIndex < 0) return [];
  
  // Get the current column
  const currentColumn = entries[beforeIndex].columnNumber;
  
  // Start from the specified index and collect consecutive losses
  const consecutiveLosses: GameEntry[] = [];
  for (let i = beforeIndex; i >= 0; i--) {
    const entry = entries[i];
    
    // Only consider entries in the current column
    if (entry.columnNumber !== currentColumn) {
      break;
    }
    
    // If we find a win, break the consecutive loss streak
    if (entry.result === 'W') {
      break;
    }
    
    // Add losses to our collection
    if (entry.result === 'L') {
      consecutiveLosses.unshift(entry); // Add to the beginning to maintain order
      
      // If we have enough losses, stop
      if (consecutiveLosses.length >= n) {
        break;
      }
    }
  }
  
  // Return the last N consecutive losses (or fewer if not enough)
  return consecutiveLosses.slice(-n);
}

/**
 * Calculate the recovery bet amount
 */
function calculateRecoveryBetAmount(entries: GameEntry[], raiseBet: number): number {
  // Get the recovery mode start index
  const recoveryStartIndex = getRecoveryModeStartIndex(entries);
  
  // Get all entries after recovery mode started
  const entriesAfterRecovery = entries.slice(recoveryStartIndex);
  
  // Get all losses after recovery mode started
  const lossesAfterRecovery = entriesAfterRecovery.filter(entry => 
    entry.result === 'L' && !entry.isPatternCell
  );
  
  // If we have no losses after recovery mode started, use the initial recovery calculation
  // This is the formula for the first recovery bet: ((sum of 3 previous losses) + (RBA*2 for each loss)) ÷ 2
  const lastThreeLosses = getLastNLosses(entries, 3);
  const sumOfLosses = lastThreeLosses.reduce((sum, entry) => sum + entry.betAmount, 0);
  const rbaAddition = raiseBet * 2 * lastThreeLosses.length;
  const recoveryBetAmount = Math.floor((sumOfLosses + rbaAddition) / 2 / 100) * 100;
  
  console.log(`Initial recovery calculation for 3 consecutive losses:`);
  console.log(`- Last 3 losses: ${lastThreeLosses.map(e => e.betAmount).join(', ')}`);
  console.log(`- Sum of losses: ${sumOfLosses}`);
  console.log(`- RBA*2 for each loss: ${rbaAddition}`);
  console.log(`- Total before division: ${sumOfLosses + rbaAddition}`);
  console.log(`- Final recovery bet (÷2): ${recoveryBetAmount}`);
  
  return recoveryBetAmount;
}

/**
 * Get the index of the entry that ended the current recovery mode
 */
export function getRecoveryModeEndIndex(entries: GameEntry[]): number {
  if (entries.length === 0) return -1;
  
  // Find the last recovery mode end
  for (let i = entries.length - 1; i >= 0; i--) {
    if (entries[i].recoveryModeEnd === true) {
      return i;
    }
    
    // If we find a start before finding an end, we're in recovery mode
    if (entries[i].recoveryModeStart === true) {
      return -1;
    }
  }
  
  return -1;
}
