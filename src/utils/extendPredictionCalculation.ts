import { GameEntry, ExtensionFactor } from '@/types/game';

/**
 * Calculate the next bet amount specifically for Extend logic.
 * This function is completely isolated from other prediction logics.
 */
export function calculateExtendNextBet(
  entries: GameEntry[],
  isGameReset: boolean,
  baseBet: number,
  raiseBet: number,
  rowCount: number,
  extensionFactor: ExtensionFactor
): number {
  // For empty game or reset game, return base bet
  if (isGameReset || entries.length === 0) {
    // Do not check for profit threshold or consecutive losses in Extend mode
    return baseBet;
  }

  // Calculate the next position
  const { columnNumber, rowNumber } = calculateNextPositionForExtend(entries, rowCount, extensionFactor);
  console.log(`Calculating next bet for Extend logic`);
  console.log(`Next position: Column ${columnNumber}, Row ${rowNumber}`);

  // Check if it's a pattern cell (Row 1 or Row 4)
  if (isPatternCell(rowNumber)) {
    console.log(`Row ${rowNumber} is a pattern cell (No Bet)`);
    return 0;
  }

  // Handle Row 2 in any column according to the formula: (SRPC + RBA) + (PCLRL + RBA) + RBA
  if (rowNumber === 2) {
    // Start with RBA as base
    let result = raiseBet;
    console.log(`Row 2 calculation - Starting with RBA: ${result}`);

    if (columnNumber > 1) {
      // For Row 2 in Column 2+, apply the standardized formula: (SRPC + RBA) + (PCLRL + RBA) + RBA
      console.log(`Row 2 in Column ${columnNumber} - applying standardized formula: (SRPC + RBA) + (PCLRL + RBA) + RBA`);
      
      // SRPC (Same Row Previous Column) component
      // Find the Row 2 entry in previous column
      const sameRowPrevCol = entries.find(
        entry => entry.columnNumber === columnNumber - 1 && 
                entry.rowNumber === 2 && 
                !entry.isPatternCell
      );
      
      // Check if SRPC is a loss or win
      if (sameRowPrevCol) {
        if (sameRowPrevCol.result === 'L') {
          const srpcValue = sameRowPrevCol.betAmount + raiseBet;
          result += srpcValue;
          console.log(`SRPC (Row 2, Col ${columnNumber-1}) was a LOSS: Adding (${sameRowPrevCol.betAmount} + ${raiseBet}) = ${srpcValue}`);
        } else if (sameRowPrevCol.result === 'W') {
          console.log(`SRPC (Row 2, Col ${columnNumber-1}) was a WIN: Adding 0`);
        } else {
          console.log(`SRPC (Row 2, Col ${columnNumber-1}) was neither win nor loss (${sameRowPrevCol.result}): Adding 0`);
        }
      } else {
        console.log(`No SRPC (Row 2 entry in Column ${columnNumber-1}) found: Adding 0`);
      }
      
      // PCLRL (Previous Column's Last Row) component
      // Find the last row in previous column
      const lastRowInPrevCol = getLastRowNumberInColumn(entries, columnNumber - 1);
      console.log(`Last row in previous column (Column ${columnNumber-1}) is Row ${lastRowInPrevCol}`);
      
      // Find the entry for the last row in previous column
      const prevColLastRow = entries.find(
        entry => entry.columnNumber === columnNumber - 1 && 
                entry.rowNumber === lastRowInPrevCol && 
                !entry.isPatternCell
      );
      
      // Check if PCLRL is a loss or win
      if (prevColLastRow) {
        if (prevColLastRow.result === 'L') {
          const pclrlValue = prevColLastRow.betAmount + raiseBet;
          result += pclrlValue;
          console.log(`PCLRL (Row ${lastRowInPrevCol}, Col ${columnNumber-1}) was a LOSS: Adding (${prevColLastRow.betAmount} + ${raiseBet}) = ${pclrlValue}`);
        } else if (prevColLastRow.result === 'W') {
          console.log(`PCLRL (Row ${lastRowInPrevCol}, Col ${columnNumber-1}) was a WIN: Adding 0`);
        } else {
          console.log(`PCLRL (Row ${lastRowInPrevCol}, Col ${columnNumber-1}) was neither win nor loss (${prevColLastRow.result}): Adding 0`);
        }
      } else {
        console.log(`No PCLRL (Row ${lastRowInPrevCol} entry in Column ${columnNumber-1}) found: Adding 0`);
      }
    }
    
    console.log(`Final Row 2 bet calculation: ${result}`);
    return result;
  }

  // Row 3 calculation - Implementing the specified formula: (SRPC + RBA) + (PLSC + RBA) + RBA
  if (rowNumber === 3) {
    console.log(`Column ${columnNumber}, Row 3: Starting with RBA: ${raiseBet}`);
    
    // Start with RBA as base
    let result = raiseBet;
    
    // Check if the same row in previous column was a loss
    if (columnNumber > 1) {
      const sameRowPrevCol = entries.find(
        entry => entry.columnNumber === columnNumber - 1 && 
                entry.rowNumber === 3 && 
                !entry.isPatternCell
      );
      
      if (sameRowPrevCol) {
        if (sameRowPrevCol.result === 'L') {
          const srpcValue = sameRowPrevCol.betAmount + raiseBet;
          result += srpcValue;
          console.log(`SRPC (Row 3, Col ${columnNumber-1}) was a LOSS: Adding (${sameRowPrevCol.betAmount} + ${raiseBet}) = ${srpcValue}`);
        } else if (sameRowPrevCol.result === 'W') {
          console.log(`SRPC (Row 3, Col ${columnNumber-1}) was a WIN: Not adding SRPC component`);
        } else {
          console.log(`SRPC (Row 3, Col ${columnNumber-1}) was neither win nor loss (${sameRowPrevCol.result}): Not adding SRPC component`);
        }
      } else {
        console.log(`No SRPC (Row 3 entry in Column ${columnNumber-1}) found: Not adding SRPC component`);
      }
    }
    
    // Check for previous loss in same column (Row 2)
    const prevRowSameCol = entries.find(
      entry => entry.columnNumber === columnNumber && 
              entry.rowNumber === 2 && 
              !entry.isPatternCell
    );
    
    if (prevRowSameCol) {
      if (prevRowSameCol.result === 'L') {
        const plscValue = prevRowSameCol.betAmount + raiseBet;
        result += plscValue;
        console.log(`PLSC (Row 2, Col ${columnNumber}) was a LOSS: Adding (${prevRowSameCol.betAmount} + ${raiseBet}) = ${plscValue}`);
      } else if (prevRowSameCol.result === 'W') {
        console.log(`PLSC (Row 2, Col ${columnNumber}) was a WIN: Not adding PLSC component`);
      } else {
        console.log(`PLSC (Row 2, Col ${columnNumber}) was neither win nor loss (${prevRowSameCol.result}): Not adding PLSC component`);
      }
    } else {
      console.log(`No PLSC (Row 2 entry in Column ${columnNumber}) found: Not adding PLSC component`);
    }
    
    console.log(`Final Row 3 bet calculation: ${result}`);
    return result;
  }

  // Row 5 calculation - Corrected formula: (Row 3 Loss in Current Column + RBA) + (SRPC + RBA) + RBA
  if (rowNumber === 5) {
    console.log(`Row 5 calculation - Corrected formula: (Row 3 Loss + RBA) + (SRPC + RBA) + RBA`);
    
    // Start with RBA as base
    let result = raiseBet;
    console.log(`Starting with RBA: ${result}`);
    
    // First component: (Row 3 Loss in Current Column + RBA)
    // Specifically look for Row 3 loss in current column
    const row3Entry = entries.find(
      entry => entry.columnNumber === columnNumber && 
              entry.rowNumber === 3 && 
              entry.result === 'L'
    );
    
    if (row3Entry) {
      const row3Loss = row3Entry.betAmount;
      const row3Component = row3Loss + raiseBet;
      result += row3Component;
      console.log(`Found Row 3 loss in current column: ${row3Loss}`);
      console.log(`Adding (Row 3 Loss + RBA): ${row3Component} (${row3Loss} + ${raiseBet})`);
    } else {
      console.log(`No Row 3 loss found in current column, not adding this component`);
    }
    
    // Second component: (SRPC + RBA)
    // Check for Same Row Previous Column loss (Row 5 in previous column)
    if (columnNumber > 1) {
      const sameRowPrevCol = entries.find(
        entry => entry.columnNumber === columnNumber - 1 && 
                entry.rowNumber === 5 && 
                !entry.isPatternCell &&
                entry.result === 'L'
      );
      
      if (sameRowPrevCol) {
        const srpcLoss = sameRowPrevCol.betAmount;
        const srpcComponent = srpcLoss + raiseBet;
        result += srpcComponent;
        console.log(`Found same row previous column loss at Col ${columnNumber-1}, Row 5: ${srpcLoss}`);
        console.log(`Adding (SRPC + RBA): ${srpcComponent} (${srpcLoss} + ${raiseBet})`);
      } else {
        console.log(`No loss found for Row 5 in previous column, not adding SRPC component`);
      }
    }
    
    console.log(`Final Row 5 bet calculation: ${result}`);
    return result;
  }

  if (columnNumber === 1) {
    // Row 2 and Row 3 use base bet for the first bet
    if ((rowNumber === 2 || rowNumber === 3) && !hasPreviousLossInColumn(entries, columnNumber, rowNumber)) {
      console.log(`Column 1, Row ${rowNumber}: No previous losses found, using base bet: ${baseBet}`);
      return baseBet;
    }
    
    // For all rows in Column 1 including Row 2 and 3 when there's a previous loss, use PLSC + RBA
    const previousLoss = getPreviousLossInColumn(entries, columnNumber, rowNumber);
    if (previousLoss > 0) {
      const betAmount = previousLoss + raiseBet;
      console.log(`Column 1, Row ${rowNumber}: Using PLSC + RBA: ${betAmount} (${previousLoss} + ${raiseBet})`);
      return betAmount;
    }
    
    return baseBet;
  }

  // For other rows after Row 3 but not Row 5
  if (rowNumber > 3 && rowNumber !== 5) {
    console.log(`Calculating for Column ${columnNumber}, Row ${rowNumber}, starting with RBA: ${raiseBet}`);
    
    // Check for previous row in same column win
    const prevRowSameColEntry = entries.find(entry => 
      entry.columnNumber === columnNumber && 
      entry.rowNumber === rowNumber - 1 && 
      !entry.isPatternCell &&
      entry.result === 'W'
    );
    
    if (prevRowSameColEntry) {
      console.log(`Previous row in same column was a WIN, checking for PLSC instead of resetting`);
      
      // Check for Previous Loss in Same Column (immediate previous non-pattern row)
      const previousLoss = getPreviousLossInColumn(entries, columnNumber, rowNumber);
      if (previousLoss > 0) {
        const betAmount = previousLoss + raiseBet;
        console.log(`Found PLSC after previous row win: ${betAmount} (${previousLoss} + ${raiseBet})`);
        return betAmount;
      }
      
      console.log(`No PLSC found after previous row win, using base bet: ${baseBet}`);
      return baseBet;
    }
    
    // Check for same row previous column win
    const prevColSameRowEntry = entries.find(entry => 
      entry.columnNumber === columnNumber - 1 && 
      entry.rowNumber === rowNumber && 
      !entry.isPatternCell &&
      entry.result === 'W'
    );
    
    if (prevColSameRowEntry) {
      console.log(`Same row in previous column was a WIN, checking for PLSC instead of resetting`);
      
      // Check for Previous Loss in Same Column (immediate previous non-pattern row)
      const previousLoss = getPreviousLossInColumn(entries, columnNumber, rowNumber);
      if (previousLoss > 0) {
        const betAmount = previousLoss + raiseBet;
        console.log(`Found PLSC after same row previous column win: ${betAmount} (${previousLoss} + ${raiseBet})`);
        return betAmount;
      }
      
      console.log(`No PLSC found after same row previous column win, using base bet: ${baseBet}`);
      return baseBet;
    }
    
    // If no wins, calculate with formula: RBA + (SRPC + RBA) + (PLSC + RBA)
    let result = raiseBet;
    
    // Add Same Row Previous Column loss + RBA
    const sameRowPreviousColumnLoss = getSameRowPreviousColumnLoss(entries, columnNumber, rowNumber);
    const sameRowPrevColWin = wasSameRowPreviousColumnWin(entries, columnNumber, rowNumber);
    
    if (sameRowPreviousColumnLoss > 0) {
      result += (sameRowPreviousColumnLoss + raiseBet);
      console.log(`Adding (SRPC + RBA): ${sameRowPreviousColumnLoss + raiseBet} (${sameRowPreviousColumnLoss} + ${raiseBet})`);
    } else if (sameRowPrevColWin) {
      // If there was a win in same row previous column, SRPC + RBA = NA (don't add)
      console.log(`SRPC was a win, SRPC + RBA = NA (not adding to calculation)`);
    } else {
      console.log(`No SRPC loss or win found, adding 0`);
    }
    
    // Add Previous Loss in Same Column + RBA (immediate previous non-pattern row)
    const previousLoss = getPreviousLossInColumn(entries, columnNumber, rowNumber);
    const wasPrevRowWin = wasPositionWin(entries, columnNumber, rowNumber - 1);
    
    if (previousLoss > 0) {
      result += (previousLoss + raiseBet);
      console.log(`Adding (PLSC + RBA): ${previousLoss + raiseBet} (${previousLoss} + ${raiseBet})`);
    } else if (wasPrevRowWin && !isPatternCell(rowNumber - 1)) {
      // If there was a win in previous row, PLSC + RBA = NA (don't add)
      console.log(`PLSC was a win, PLSC + RBA = NA (not adding to calculation)`);
    } else {
      console.log(`No PLSC loss or win found, adding 0`);
    }
    
    console.log(`Final calculated bet: ${result}`);
    return result;
  }

  return baseBet;
}

/**
 * Determine the result of a hand specifically for Extend logic.
 * This function implements the win/loss identification rules as specified:
 * - Row 1 & Row 4 are pattern cells (No Bet)
 * - Row 2 & Row 3 follow pattern from Row 1 (Win if same, Loss if different)
 * - Row 4 copies value from Row 3 (No Bet)
 * - Row 5 follows pattern from Row 4 (Win if same, Loss if different)
 */
export function determineExtendResult(
  handValue: 'P' | 'B',
  columnNumber: number,
  rowNumber: number,
  entries: GameEntry[]
): 'W' | 'L' | 'N' {
  console.log(`determineExtendResult: Checking result for Col ${columnNumber}, Row ${rowNumber}, Hand ${handValue}`);
  
  // For pattern cells (Row 1 and Row 4), result is always 'N' (no bet)
  if (isPatternCell(rowNumber)) {
    console.log(`determineExtendResult: Row ${rowNumber} is a pattern cell - Result is N`);
    return 'N';
  }

  // For Row 2 and Row 3, compare with pattern from Row 1
  if (rowNumber === 2 || rowNumber === 3) {
    const patternValue = entries.find(
      entry => entry.columnNumber === columnNumber && entry.rowNumber === 1
    )?.handValue;

    if (!patternValue) {
      console.log(`determineExtendResult: No pattern value found in Row 1 - Result is N`);
      return 'N';
    }

    const result = handValue === patternValue ? 'W' : 'L';
    console.log(`determineExtendResult: For Row ${rowNumber}, comparing with Row 1 pattern (${patternValue}), hand value ${handValue} - Result is ${result}`);
    return result;
  }

  // For Row 5, compare with pattern from Row 4
  if (rowNumber === 5) {
    const patternValue = entries.find(
      entry => entry.columnNumber === columnNumber && entry.rowNumber === 4
    )?.handValue;

    if (!patternValue) {
      console.log(`determineExtendResult: No pattern value found in Row 4 - Result is N`);
      return 'N';
    }

    const result = handValue === patternValue ? 'W' : 'L';
    console.log(`determineExtendResult: For Row 5, comparing with Row 4 pattern (${patternValue}), hand value ${handValue} - Result is ${result}`);
    return result;
  }

  console.log(`determineExtendResult: No special rule applied for Row ${rowNumber} - Result is N`);
  return 'N';
}

// Helper Functions

/**
 * Get the last row number in a specific column
 */
export function getLastRowNumberInColumn(
  entries: GameEntry[],
  columnNumber: number
): number {
  const columnEntries = entries.filter(entry => 
    entry.columnNumber === columnNumber
  );
  
  if (columnEntries.length === 0) {
    return 0;
  }
  
  return Math.max(...columnEntries.map(entry => entry.rowNumber));
}

/**
 * Check if a position has a previous loss in the column
 */
export function hasPreviousLossInColumn(
  entries: GameEntry[],
  columnNumber: number,
  rowNumber: number
): boolean {
  if (rowNumber <= 1) return false;
  
  // Find entries in the same column with row numbers less than the current row
  const previousRows = entries.filter(
    entry => entry.columnNumber === columnNumber && 
            entry.rowNumber < rowNumber &&
            !entry.isPatternCell
  );
  
  // Check if any previous row has a loss
  return previousRows.some(entry => entry.result === 'L');
}

/**
 * Get the previous loss in the column (any row before the current one)
 */
export function getPreviousLossInColumn(
  entries: GameEntry[],
  columnNumber: number,
  rowNumber: number
): number {
  if (rowNumber <= 1) return 0;
  
  // Get all entries in the current column before the current row
  const previousEntries = entries.filter(
    entry => entry.columnNumber === columnNumber && 
            entry.rowNumber < rowNumber &&
            !entry.isPatternCell
  );
  
  // Sort by row number descending to get the most recent entries first
  previousEntries.sort((a, b) => b.rowNumber - a.rowNumber);
  
  // Find the first loss
  const previousLoss = previousEntries.find(entry => entry.result === 'L');
  
  if (!previousLoss) {
    console.log(`No previous loss found in column ${columnNumber}`);
    return 0;
  }
  
  return previousLoss.betAmount;
}

/**
 * Get the previous loss in the column but specifically for a given row
 */
export function getPreviousRowLossInColumn(
  entries: GameEntry[],
  columnNumber: number,
  rowNumber: number,
  specificRow: number
): number {
  if (rowNumber <= 1) return 0;
  
  // Find the specific row entry
  const specificRowEntry = entries.find(
    entry => entry.columnNumber === columnNumber && 
            entry.rowNumber === specificRow &&
            !entry.isPatternCell
  );
  
  // If it exists and is a loss, return its bet amount
  if (specificRowEntry && specificRowEntry.result === 'L') {
    return specificRowEntry.betAmount;
  }
  
  return 0;
}

/**
 * Get a loss from Row 3 in the current column
 */
export function getRow3LossInCurrentColumn(
  entries: GameEntry[],
  columnNumber: number
): number {
  // Find the Row 3 entry for the current column
  const row3Entry = entries.find(
    entry => entry.columnNumber === columnNumber && 
            entry.rowNumber === 3 && 
            entry.result === 'L'
  );
  
  // If it exists and is a loss, return its bet amount
  if (row3Entry) {
    return row3Entry.betAmount;
  }
  
  return 0;
}

/**
 * Get a loss from Row 2 in the previous column
 */
export function getPreviousColumnRow2Loss(
  entries: GameEntry[],
  columnNumber: number
): number {
  if (columnNumber <= 1) return 0;
  
  // Find the Row 2 entry for the previous column
  const row2Entry = entries.find(
    entry => entry.columnNumber === columnNumber - 1 && 
            entry.rowNumber === 2 && 
            !entry.isPatternCell &&
            entry.result === 'L'
  );
  
  // If it exists and is a loss, return its bet amount
  if (row2Entry) {
    return row2Entry.betAmount;
  }
  
  return 0;
}

/**
 * Get a loss from Row 3 in the previous column
 */
export function getPreviousColumnRow3Loss(
  entries: GameEntry[],
  columnNumber: number
): number {
  if (columnNumber <= 1) return 0;
  
  // Find the Row 3 entry for the previous column
  const row3Entry = entries.find(
    entry => entry.columnNumber === columnNumber - 1 && 
            entry.rowNumber === 3 && 
            !entry.isPatternCell &&
            entry.result === 'L'
  );
  
  // If it exists and is a loss, return its bet amount
  if (row3Entry) {
    return row3Entry.betAmount;
  }
  
  return 0;
}

/**
 * Get a loss from Row 5 in the previous column
 */
export function getPreviousColumnRow5Loss(
  entries: GameEntry[],
  columnNumber: number
): number {
  if (columnNumber <= 1) return 0;
  
  // Find the Row 5 entry for the previous column
  const row5Entry = entries.find(
    entry => entry.columnNumber === columnNumber - 1 && 
            entry.rowNumber === 5 && 
            !entry.isPatternCell &&
            entry.result === 'L'
  );
  
  // If it exists and is a loss, return its bet amount
  if (row5Entry) {
    return row5Entry.betAmount;
  }
  
  return 0;
}

/**
 * Get the same row's previous column loss
 */
export function getSameRowPreviousColumnLoss(
  entries: GameEntry[],
  columnNumber: number,
  rowNumber: number
): number {
  if (columnNumber <= 1) return 0;
  
  const sameRowPrevCol = entries.find(
    entry => entry.columnNumber === columnNumber - 1 && 
            entry.rowNumber === rowNumber && 
            !entry.isPatternCell &&
            entry.result === 'L'
  );
  
  return sameRowPrevCol ? sameRowPrevCol.betAmount : 0;
}

/**
 * Check if the same row in the previous column was a win
 */
export function wasSameRowPreviousColumnWin(
  entries: GameEntry[],
  columnNumber: number,
  rowNumber: number
): boolean {
  if (columnNumber <= 1) return false;
  
  const entry = entries.find(
    e => e.columnNumber === columnNumber - 1 && e.rowNumber === rowNumber && !e.isPatternCell
  );
  
  return entry?.result === 'W';
}

/**
 * Check if a position was a win
 */
export function wasPositionWin(
  entries: GameEntry[],
  columnNumber: number,
  rowNumber: number
): boolean {
  const entry = entries.find(
    e => e.columnNumber === columnNumber && e.rowNumber === rowNumber && !e.isPatternCell
  );
  
  return entry?.result === 'W';
}

/**
 * Check if a row is a pattern cell
 */
export function isPatternCell(rowNumber: number): boolean {
  return rowNumber === 1 || rowNumber === 4;
}

/**
 * Calculate the next position for Extend logic
 */
export function calculateNextPositionForExtend(
  entries: GameEntry[],
  rowCount: number,
  extensionFactor: ExtensionFactor
): { columnNumber: number; rowNumber: number } {
  if (entries.length === 0) {
    return { columnNumber: 1, rowNumber: 1 };
  }

  const lastEntry = entries[entries.length - 1];
  let nextColumnNumber = lastEntry.columnNumber;
  let nextRowNumber = lastEntry.rowNumber + 1;

  const effectiveRowCount = rowCount * extensionFactor;
  if (nextRowNumber > effectiveRowCount) {
    nextColumnNumber++;
    nextRowNumber = 1;
  }

  return { columnNumber: nextColumnNumber, rowNumber: nextRowNumber };
}
