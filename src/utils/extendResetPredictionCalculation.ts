
import { GameEntry, ExtensionFactor } from '@/types/game';

/**
 * Calculate the next bet amount specifically for Extend-Reset logic.
 * This function is completely isolated from other prediction logics.
 */
export function calculateExtendResetNextBet(
  entries: GameEntry[],
  isGameReset: boolean,
  baseBet: number,
  raiseBet: number,
  rowCount: number,
  extensionFactor: ExtensionFactor,
  shouldReset: boolean = false,
  resetReason: 'profit' | 'reset-point' | null = null,
  lastResetColumn: number | null = null
): number {
  // For empty game or reset game, return base bet
  if (isGameReset || entries.length === 0) {
    return baseBet;
  }

  // Calculate the next position
  const { columnNumber, rowNumber } = calculateNextPositionForExtendReset(entries, rowCount, extensionFactor);
  console.log(`Calculating next bet for Extend-Reset logic`);
  console.log(`Next position: Column ${columnNumber}, Row ${rowNumber}`);
  console.log(`Last reset column: ${lastResetColumn}`);

  // Check if it's a pattern cell (Row 1 or Row 4)
  if (isPatternCell(rowNumber)) {
    console.log(`Row ${rowNumber} is a pattern cell (No Bet)`);
    return 0;
  }

  // IMPORTANT: Find the most recent reset in any column
  let mostRecentResetColumn = null;
  let mostRecentResetEntry = null;
  let resetType = null;
  
  for (let i = entries.length - 1; i >= 0; i--) {
    if (entries[i].resetPoint === true) {
      mostRecentResetColumn = entries[i].columnNumber;
      mostRecentResetEntry = entries[i];
      resetType = entries[i].resetType;
      break;
    }
  }
  
  console.log(`Most recent reset column: ${mostRecentResetColumn || 'none'}, reset type: ${resetType || 'none'}`);
  
  // Check if we're in the same column as the most recent reset
  const isInResetColumn = mostRecentResetColumn !== null && columnNumber === mostRecentResetColumn;
  console.log(`Is in reset column: ${isInResetColumn}`);

  // IMPORTANT CHANGE: After profit reset, only Row 1 through Row 3 use base bet
  // Row 5 should still use formula with Previous Loss in Same Column + RBA
  if (mostRecentResetEntry && resetType === 'profit' && columnNumber === mostRecentResetColumn) {
    // Only rows 1-3 should use base bet after reset
    if (rowNumber <= 3) {
      console.log(`Row ${rowNumber} in reset Column ${columnNumber}: Using base bet after ${resetType} reset: ${baseBet}`);
      return baseBet;
    }
    
    // For Row 5, continue with formula calculation even after reset
    console.log(`Row ${rowNumber} in reset Column ${columnNumber} after ${resetType} reset: Continuing with formula calculation`);
  }

  // Find if there was a reset on Row 5 in any column
  let resetOnRow5 = false;
  let row5ResetColumn = null;
  for (let i = entries.length - 1; i >= 0; i--) {
    if (entries[i].resetPoint === true && entries[i].rowNumber === 5) {
      resetOnRow5 = true;
      row5ResetColumn = entries[i].columnNumber;
      console.log(`Found a reset on Row 5 in Column ${entries[i].columnNumber}`);
      break;
    }
  }

  // UPDATED LOGIC: Only zero SRPC in the reset column itself OR in the immediate next column after a Row 5 reset
  // Previously this was zeroing SRPC for all columns after a Row 5 reset
  const shouldZeroSrpc = isInResetColumn || (resetOnRow5 && columnNumber === row5ResetColumn + 1);
  
  if (shouldZeroSrpc) {
    if (isInResetColumn) {
      console.log(`In reset column ${mostRecentResetColumn} - will zero SRPC component`);
    }
    if (resetOnRow5 && columnNumber === row5ResetColumn + 1) {
      console.log(`In column immediately after Row 5 reset in Column ${row5ResetColumn} - will zero SRPC component`);
    }
  } else {
    console.log(`Not in reset column and not immediately after Row 5 reset - will apply full formula including SRPC component`);
  }

  // Special handling for Column 1
  if (columnNumber === 1) {
    console.log(`Special handling for Column 1, Row ${rowNumber}`);
    
    // Row 2 and Row 3 use base bet for the first bet if no immediate previous loss
    if ((rowNumber === 2 || rowNumber === 3) && !hasImmediatePreviousLossInColumn(entries, columnNumber, rowNumber)) {
      console.log(`Column 1, Row ${rowNumber}: No immediate previous loss found, using base bet: ${baseBet}`);
      return baseBet;
    }
    
    // For all rows in Column 1 when there's an immediate previous loss, use PLSC + RBA
    const previousLoss = getImmediatePreviousLossInColumn(entries, columnNumber, rowNumber);
    if (previousLoss > 0) {
      const betAmount = previousLoss + raiseBet;
      console.log(`Column 1, Row ${rowNumber}: Using PLSC + RBA: ${betAmount} (${previousLoss} + ${raiseBet})`);
      return betAmount;
    }
    
    return baseBet;
  }

  // Handle Row 2 in any column according to the formula
  if (rowNumber === 2) {
    // If this is the first bet after a profit reset, use base bet
    if (isInResetColumn && mostRecentResetEntry && resetType === 'profit') {
      console.log(`Row 2 in reset Column ${columnNumber}: Using base bet after ${resetType} reset: ${baseBet}`);
      return baseBet;
    }
    
    // Start with RBA as base
    let result = raiseBet;
    console.log(`Row 2 calculation - Starting with RBA: ${result}`);

    if (columnNumber > 1) {
      // SRPC (Same Row Previous Column) component
      let srpcComponent = 0;
      
      // UPDATED: Only zero SRPC in the reset column itself OR in the immediate next column after a Row 5 reset
      if (shouldZeroSrpc) {
        if (resetOnRow5 && columnNumber === row5ResetColumn + 1) {
          console.log(`SRPC component will be zeroed due to being in column immediately after Row 5 reset in column ${row5ResetColumn}`);
        } else {
          console.log(`SRPC component will be zeroed due to reset in column ${mostRecentResetColumn}`);
        }
      } else {
        // Find the Row 2 entry in previous column
        const sameRowPrevCol = entries.find(
          entry => entry.columnNumber === columnNumber - 1 && 
                   entry.rowNumber === 2 && 
                   !entry.isPatternCell
        );
        
        // Check if SRPC is a loss or win
        if (sameRowPrevCol) {
          if (sameRowPrevCol.result === 'L') {
            srpcComponent = sameRowPrevCol.betAmount + raiseBet;
            console.log(`SRPC (Row 2, Col ${columnNumber-1}) was a LOSS: Adding (${sameRowPrevCol.betAmount} + ${raiseBet}) = ${srpcComponent}`);
          } else if (sameRowPrevCol.result === 'W') {
            console.log(`SRPC (Row 2, Col ${columnNumber-1}) was a WIN: Adding 0`);
          } else {
            console.log(`SRPC (Row 2, Col ${columnNumber-1}) was neither win nor loss (${sameRowPrevCol.result}): Adding 0`);
          }
        } else {
          console.log(`No SRPC (Row 2 entry in Column ${columnNumber-1}) found: Adding 0`);
        }
      }
      
      // Add SRPC component if not zeroed
      if (!shouldZeroSrpc) {
        result += srpcComponent;
      }
      
      // PCLRL (Previous Column's Last Row) component
      // Always include PCLRL component regardless of whether we're after a reset
      let pclrlComponent = 0;
      
      // Find the last row in previous column
      const lastRowInPrevCol = getLastRowNumberInColumn(entries, columnNumber - 1);
      console.log(`Last row in previous column (Column ${columnNumber-1}) is Row ${lastRowInPrevCol}`);
      
      // Find the entry for the last row in previous column
      const prevColLastRow = entries.find(
        entry => entry.columnNumber === columnNumber - 1 && 
                entry.rowNumber === lastRowInPrevCol && 
                !entry.isPatternCell
      );
      
      // Check if PCLRL is a loss or win
      if (prevColLastRow) {
        if (prevColLastRow.result === 'L') {
          pclrlComponent = prevColLastRow.betAmount + raiseBet;
          console.log(`PCLRL (Row ${lastRowInPrevCol}, Col ${columnNumber-1}) was a LOSS: Adding (${prevColLastRow.betAmount} + ${raiseBet}) = ${pclrlComponent}`);
        } else if (prevColLastRow.result === 'W') {
          console.log(`PCLRL (Row ${lastRowInPrevCol}, Col ${columnNumber-1}) was a WIN: Adding 0`);
        } else {
          console.log(`PCLRL (Row ${lastRowInPrevCol}, Col ${columnNumber-1}) was neither win nor loss (${prevColLastRow.result}): Adding 0`);
        }
      } else {
        console.log(`No PCLRL (Row ${lastRowInPrevCol} entry in Column ${columnNumber-1}) found: Adding 0`);
      }
      
      result += pclrlComponent;
    }
    
    console.log(`Final Row 2 bet calculation: ${result}`);
    return result;
  }

  // Row 3 calculation - Implementing the specified formula: (SRPC + RBA) + (PLSC + RBA) + RBA
  if (rowNumber === 3) {
    // If this is after a profit reset, use base bet
    if (isInResetColumn && mostRecentResetEntry && resetType === 'profit') {
      console.log(`Row 3 in reset Column ${columnNumber}: Using base bet after ${resetType} reset: ${baseBet}`);
      return baseBet;
    }
    
    console.log(`Column ${columnNumber}, Row 3: Starting with RBA: ${raiseBet}`);
    
    // Start with RBA as base
    let result = raiseBet;
    
    // Check if the same row in previous column was a loss
    // UPDATED: Only zero SRPC in the reset column itself OR in the immediate next column after a Row 5 reset
    if (columnNumber > 1) {
      if (shouldZeroSrpc) {
        if (resetOnRow5 && columnNumber === row5ResetColumn + 1) {
          console.log(`SRPC component is zeroed due to being in column immediately after Row 5 reset in column ${row5ResetColumn}`);
        } else {
          console.log(`SRPC component is zeroed due to reset in column ${mostRecentResetColumn}`);
        }
      } else {
        const sameRowPrevCol = entries.find(
          entry => entry.columnNumber === columnNumber - 1 && 
                  entry.rowNumber === 3 && 
                  !entry.isPatternCell
        );
        
        if (sameRowPrevCol) {
          if (sameRowPrevCol.result === 'L') {
            const srpcValue = sameRowPrevCol.betAmount + raiseBet;
            result += srpcValue;
            console.log(`SRPC (Row 3, Col ${columnNumber-1}) was a LOSS: Adding (${sameRowPrevCol.betAmount} + ${raiseBet}) = ${srpcValue}`);
          } else if (sameRowPrevCol.result === 'W') {
            console.log(`SRPC (Row 3, Col ${columnNumber-1}) was a WIN: Not adding SRPC component`);
          } else {
            console.log(`SRPC (Row 3, Col ${columnNumber-1}) was neither win nor loss (${sameRowPrevCol.result}): Not adding SRPC component`);
          }
        } else {
          console.log(`No SRPC (Row 3 entry in Column ${columnNumber-1}) found: Not adding SRPC component`);
        }
      }
    }
    
    // Check for immediate previous loss in same column (Row 2)
    // We always consider PLSC even after a reset
    const prevRowSameCol = entries.find(
      entry => entry.columnNumber === columnNumber && 
              entry.rowNumber === 2 && 
              !entry.isPatternCell
    );
    
    if (prevRowSameCol) {
      if (prevRowSameCol.result === 'L') {
        const plscValue = prevRowSameCol.betAmount + raiseBet;
        result += plscValue;
        console.log(`PLSC (Row 2, Col ${columnNumber}) was a LOSS: Adding (${prevRowSameCol.betAmount} + ${raiseBet}) = ${plscValue}`);
      } else if (prevRowSameCol.result === 'W') {
        console.log(`PLSC (Row 2, Col ${columnNumber}) was a WIN: Not adding PLSC component`);
      } else {
        console.log(`PLSC (Row 2, Col ${columnNumber}) was neither win nor loss (${prevRowSameCol.result}): Not adding PLSC component`);
      }
    } else {
      console.log(`No PLSC (Row 2 entry in Column ${columnNumber}) found: Not adding PLSC component`);
    }
    
    console.log(`Final Row 3 bet calculation: ${result}`);
    return result;
  }

  // Row 5 calculation - CRITICAL FIX: Row 5 should follow the formula even after a reset
  if (rowNumber === 5) {
    console.log(`Row 5 calculation - Formula: (SRPC + RBA) if loss + (PLSC + RBA) if loss + RBA`);
    
    // Start with base RBA
    let result = raiseBet;
    console.log(`Starting with RBA: ${result}`);
    
    // SRPC Component: (Same Row Previous Column loss + RBA)
    // Only consider if not in a reset column or immediately after Row 5 reset
    if (columnNumber > 1 && !shouldZeroSrpc) {
      const sameRowPrevCol = entries.find(
        entry => entry.columnNumber === columnNumber - 1 && 
                entry.rowNumber === 5 && 
                !entry.isPatternCell &&
                entry.result === 'L'
      );
      
      if (sameRowPrevCol) {
        const srpcLoss = sameRowPrevCol.betAmount;
        const srpcComponent = srpcLoss + raiseBet;
        result += srpcComponent;
        console.log(`SRPC component: Found Row 5 loss in previous column (Col ${columnNumber-1}): ${srpcLoss}`);
        console.log(`Adding (SRPC + RBA): ${srpcComponent} (${srpcLoss} + ${raiseBet})`);
      } else {
        console.log(`SRPC component: No loss found for Row 5 in previous column, not adding this component`);
      }
    } else if (shouldZeroSrpc) {
      if (resetOnRow5 && columnNumber === row5ResetColumn + 1) {
        console.log(`SRPC component is zeroed for Row 5 due to being in column immediately after Row 5 reset in column ${row5ResetColumn}`);
      } else {
        console.log(`SRPC component is zeroed for Row 5 due to reset in column ${mostRecentResetColumn}`);
      }
    }
    
    // PLSC Component: (Previous Loss in Same Column + RBA)
    // For Row 5, PLSC specifically looks for Row 3 loss in current column
    const row3Loss = entries.find(
      entry => entry.columnNumber === columnNumber && 
              entry.rowNumber === 3 && 
              entry.result === 'L'
    );
    
    if (row3Loss) {
      const plscLoss = row3Loss.betAmount;
      const plscComponent = plscLoss + raiseBet;
      result += plscComponent;
      console.log(`PLSC component: Found Row 3 loss in current column: ${plscLoss}`);
      console.log(`Adding (PLSC + RBA): ${plscComponent} (${plscLoss} + ${raiseBet})`);
    } else {
      console.log(`PLSC component: No Row 3 loss found in current column, not adding this component`);
    }
    
    console.log(`Final Row 5 bet calculation: ${result}`);
    return result;
  }

  // For other rows (not handled by specific rules)
  console.log(`No specific rule for Column ${columnNumber}, Row ${rowNumber}, using base bet ${baseBet}`);
  return baseBet;
}

/**
 * Get the next bet calculation details for debugging
 */
export function getExtendResetNextBetDetails(
  entries: GameEntry[],
  isGameReset: boolean,
  baseBet: number,
  raiseBet: number,
  rowCount: number,
  extensionFactor: ExtensionFactor,
  lastResetColumn: number | null = null
): any {
  if (isGameReset || entries.length === 0) {
    return {
      srpcValue: 0,
      pclrlValue: 0,
      plscValue: 0,
      resetOccurred: false
    };
  }

  const { columnNumber, rowNumber } = calculateNextPositionForExtendReset(entries, rowCount, extensionFactor);
  
  // Find the most recent reset
  let mostRecentResetColumn = null;
  for (let i = entries.length - 1; i >= 0; i--) {
    if (entries[i].resetPoint === true) {
      mostRecentResetColumn = entries[i].columnNumber;
      break;
    }
  }
  
  // Check if we're in the same column as the most recent reset
  const isInResetColumn = mostRecentResetColumn !== null && columnNumber === mostRecentResetColumn;
  
  // Get SRPC value - zero it if in reset column or column after reset
  let srpcValue = 0;
  if (columnNumber > 1 && !isInResetColumn && !(mostRecentResetColumn !== null && columnNumber === mostRecentResetColumn + 1)) {
    const sameRowPrevCol = entries.find(
      entry => entry.columnNumber === columnNumber - 1 && 
              entry.rowNumber === rowNumber && 
              !entry.isPatternCell &&
              entry.result === 'L'
    );
    if (sameRowPrevCol) {
      srpcValue = sameRowPrevCol.betAmount;
    }
  }
  
  // Get PCLRL value - zero it if in reset column or column after reset
  let pclrlValue = 0;
  if (columnNumber > 1 && !isInResetColumn && !(mostRecentResetColumn !== null && columnNumber === mostRecentResetColumn + 1)) {
    const lastRowInPrevCol = getLastRowNumberInColumn(entries, columnNumber - 1);
    const prevColLastRow = entries.find(
      entry => entry.columnNumber === columnNumber - 1 && 
              entry.rowNumber === lastRowInPrevCol && 
              !entry.isPatternCell &&
              entry.result === 'L'
    );
    if (prevColLastRow) {
      pclrlValue = prevColLastRow.betAmount;
    }
  }
  
  // Get PLSC value
  let plscValue = 0;
  if (rowNumber > 1) {
    const prevRow = entries.find(
      entry => entry.columnNumber === columnNumber && 
              entry.rowNumber === rowNumber - 1 && 
              !entry.isPatternCell &&
              entry.result === 'L'
    );
    if (prevRow) {
      plscValue = prevRow.betAmount;
    }
  }
  
  return {
    srpcValue,
    pclrlValue,
    plscValue,
    resetOccurred: isInResetColumn
  };
}

/**
 * Determine the result of a hand specifically for Extend Reset logic.
 * Using the same logic as Extend but with special handling for reset conditions.
 */
export function determineExtendResetResult(
  handValue: 'P' | 'B',
  columnNumber: number,
  rowNumber: number,
  entries: GameEntry[]
): 'W' | 'L' | 'N' {
  // Use the same result determination as in extend mode
  return determineExtendResult(handValue, columnNumber, rowNumber, entries);
}

/**
 * Calculate the next position for Extend Reset logic
 */
export function calculateNextPositionForExtendReset(
  entries: GameEntry[],
  rowCount: number,
  extensionFactor: ExtensionFactor
): { columnNumber: number; rowNumber: number } {
  return calculateNextPositionForExtend(entries, rowCount, extensionFactor);
}

/**
 * Calculate the next position for Extend logic (helper function)
 */
export function calculateNextPositionForExtend(
  entries: GameEntry[],
  rowCount: number,
  extensionFactor: ExtensionFactor
): { columnNumber: number; rowNumber: number } {
  if (entries.length === 0) {
    return { columnNumber: 1, rowNumber: 1 };
  }

  const lastEntry = entries[entries.length - 1];
  let nextColumnNumber = lastEntry.columnNumber;
  let nextRowNumber = lastEntry.rowNumber + 1;

  const effectiveRowCount = 5;  // Always 5 rows for extend modes
  if (nextRowNumber > effectiveRowCount) {
    nextColumnNumber++;
    nextRowNumber = 1;
  }

  return { columnNumber: nextColumnNumber, rowNumber: nextRowNumber };
}

/**
 * Check if a position has a previous loss in the column
 */
export function hasImmediatePreviousLossInColumn(
  entries: GameEntry[],
  columnNumber: number,
  rowNumber: number
): boolean {
  if (rowNumber <= 1) return false;
  
  // Find entries in the same column with row numbers less than the current row
  const previousRow = entries.find(
    entry => entry.columnNumber === columnNumber && 
            entry.rowNumber === rowNumber - 1 &&
            !entry.isPatternCell
  );
  
  // Check if the immediate previous row has a loss
  return previousRow?.result === 'L';
}

/**
 * Get the previous loss in the column (immediate previous row)
 */
export function getImmediatePreviousLossInColumn(
  entries: GameEntry[],
  columnNumber: number,
  rowNumber: number
): number {
  if (rowNumber <= 1) return 0;
  
  // Get the immediate previous row entry
  const previousRow = entries.find(
    entry => entry.columnNumber === columnNumber && 
            entry.rowNumber === rowNumber - 1 &&
            !entry.isPatternCell
  );
  
  // If it exists and is a loss, return its bet amount
  if (previousRow && previousRow.result === 'L') {
    return previousRow.betAmount;
  }
  
  return 0;
}

/**
 * Check if a row is a pattern cell
 */
export function isPatternCell(rowNumber: number): boolean {
  return rowNumber === 1 || rowNumber === 4;
}

/**
 * Get the last row number in a specific column
 */
export function getLastRowNumberInColumn(
  entries: GameEntry[],
  columnNumber: number
): number {
  const columnEntries = entries.filter(entry => 
    entry.columnNumber === columnNumber
  );
  
  if (columnEntries.length === 0) {
    return 0;
  }
  
  return Math.max(...columnEntries.map(entry => entry.rowNumber));
}

/**
 * Check if profit threshold has been reached
 */
export function shouldResetDueToProfitThreshold(
  entries: GameEntry[],
  baseBet: number
): boolean {
  if (entries.length === 0) return false;
  
  // Find the most recent reset index
  let lastResetIndex = -1;
  for (let i = entries.length - 1; i >= 0; i--) {
    if (entries[i].resetPoint === true) {
      lastResetIndex = i;
      break;
    }
  }
  
  // Get the profit threshold (5 times the base bet)
  const profitThreshold = baseBet * 5;
  
  // Get the starting profit from the last reset point
  const startProfit = lastResetIndex >= 0 ? entries[lastResetIndex].cumulativeProfitLoss : 0;
  
  // Get the current profit
  const currentProfit = entries[entries.length - 1].cumulativeProfitLoss;
  
  // Calculate profit since last reset
  const profitSinceReset = currentProfit - startProfit;
  
  console.log(`Profit check: current ${currentProfit}, start ${startProfit}, diff ${profitSinceReset}, threshold ${profitThreshold}`);
  
  // Check if profit threshold has been reached
  return profitSinceReset >= profitThreshold;
}

/**
 * Check if a reset point is reached due to specified rules
 */
export function shouldResetDueToResetPoint(
  entries: GameEntry[]
): boolean {
  // Not used in Extend Reset logic, but kept for compatibility
  return false;
}

/**
 * Check if a profit reset has occurred in the game history
 */
export function hasProfitResetOccurred(
  entries: GameEntry[]
): boolean {
  return entries.some(entry => entry.resetPoint === true && entry.resetType === 'profit');
}

/**
 * Helper for determining the result - uses the extend logic
 */
export function determineExtendResult(
  handValue: 'P' | 'B',
  columnNumber: number,
  rowNumber: number,
  entries: GameEntry[]
): 'W' | 'L' | 'N' {
  console.log(`determineExtendResult: Checking result for Col ${columnNumber}, Row ${rowNumber}, Hand ${handValue}`);
  
  // For pattern cells (Row 1 and Row 4), result is always 'N' (no bet)
  if (isPatternCell(rowNumber)) {
    console.log(`determineExtendResult: Row ${rowNumber} is a pattern cell - Result is N`);
    return 'N';
  }

  // For Row 2 and Row 3, compare with pattern from Row 1
  if (rowNumber === 2 || rowNumber === 3) {
    const patternValue = entries.find(
      entry => entry.columnNumber === columnNumber && entry.rowNumber === 1
    )?.handValue;

    if (!patternValue) {
      console.log(`determineExtendResult: No pattern value found in Row 1 - Result is N`);
      return 'N';
    }

    const result = handValue === patternValue ? 'W' : 'L';
    console.log(`determineExtendResult: For Row ${rowNumber}, comparing with Row 1 pattern (${patternValue}), hand value ${handValue} - Result is ${result}`);
    return result;
  }

  // For Row 5, compare with pattern from Row 4
  if (rowNumber === 5) {
    const patternValue = entries.find(
      entry => entry.columnNumber === columnNumber && entry.rowNumber === 4
    )?.handValue;

    if (!patternValue) {
      console.log(`determineExtendResult: No pattern value found in Row 4 - Result is N`);
      return 'N';
    }

    const result = handValue === patternValue ? 'W' : 'L';
    console.log(`determineExtendResult: For Row 5, comparing with Row 4 pattern (${patternValue}), hand value ${handValue} - Result is ${result}`);
    return result;
  }

  console.log(`determineExtendResult: No special rule applied for Row ${rowNumber} - Result is N`);
  return 'N';
}
