/**
 * Formats a number as currency with a dollar sign and two decimal places
 * @param value The number to format
 * @returns Formatted currency string
 */
export const formatCurrency = (value: number): string => {
  if (value === undefined || value === null) {
    return '$0.00';
  }
  
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(value);
};

/**
 * Logs detailed bet calculation information for debugging
 * @param message The message to log
 * @param value Optional value to log
 */
export const logBetCalculation = (message: string, value?: unknown): void => {
  if (typeof value !== 'undefined') {
    console.log(`BET CALCULATION: ${message}`, value);
  } else {
    console.log(`BET CALCULATION: ${message}`);
  }
};

/**
 * Debugs calculation processes with verbose logging
 * @param message Debug message
 * @param additionalData Optional additional data to log
 */
export const debugCalculation = (message: string, additionalData?: unknown): void => {
  console.log(`DEBUG CALCULATION: ${message}`, additionalData ?? '');
};

/**
 * Safely formats a number with toLocaleString, handling null/undefined values and Promises
 * @param value Number to format
 * @param defaultValue Value to return if input is null/undefined
 * @returns Formatted string
 */
export const safeFormatNumber = (value: number | string | null | undefined | Promise<number>, defaultValue: string = '0'): string => {
  if (value === undefined || value === null) {
    return defaultValue;
  }
  
  // Handle Promise objects
  if (typeof value === 'object' && value !== null && value.toString() === '[object Promise]') {
    return defaultValue;
  }
  
  // Ensure value is a number
  const numValue = typeof value === 'number' ? value : Number(value);
  
  // Check if conversion resulted in a valid number
  if (isNaN(numValue)) {
    return defaultValue;
  }
  
  return numValue.toLocaleString();
};
