
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { 
  getAdjustedBaseBet, 
  getPreviousColumnLastResult, 
  getSameRowPreviousColumnResult,
  calculateNextBet,
  determineResult,
  getPreviousLossInSameColumn,
  getSameRowPreviousColumnLoss
} from './nextBetCalculation';
import { GameEntry } from '@/types/game';
import * as toastModule from "@/hooks/use-toast";

// Mock toast function
vi.mock("@/hooks/use-toast", () => ({
  toast: vi.fn(),
}));

describe('getAdjustedBaseBet', () => {
  it('returns baseBet for column 1', () => {
    const result = getAdjustedBaseBet(1, 1000, [], 4, 'loss');
    expect(result).toBe(1000);
  });

  it('increases baseBet based on columns with threshold losses', () => {
    const entries: GameEntry[] = [
      { id: 1, columnNumber: 1, rowNumber: 1, handValue: 'P', result: 'N', betAmount: 0, cumulativeProfitLoss: 0 },
      { id: 2, columnNumber: 1, rowNumber: 2, handValue: 'P', result: 'L', betAmount: 1000, cumulativeProfitLoss: -1000 },
      { id: 3, columnNumber: 1, rowNumber: 3, handValue: 'B', result: 'L', betAmount: 1000, cumulativeProfitLoss: -2000 },
      { id: 4, columnNumber: 1, rowNumber: 4, handValue: 'P', result: 'L', betAmount: 1000, cumulativeProfitLoss: -3000 }
    ];
    
    // For 4 rows, loss threshold is 2, and column 1 has 3 losses
    const result = getAdjustedBaseBet(2, 1000, entries, 4, 'loss');
    expect(result).toBe(2000); // baseBet + baseBet*1
  });

  it('resets columns count when a column exceeds the opposite threshold', () => {
    const entries: GameEntry[] = [
      // Column 1 has 3 losses (exceeds threshold)
      { id: 1, columnNumber: 1, rowNumber: 1, handValue: 'P', result: 'N', betAmount: 0, cumulativeProfitLoss: 0 },
      { id: 2, columnNumber: 1, rowNumber: 2, handValue: 'P', result: 'L', betAmount: 1000, cumulativeProfitLoss: -1000 },
      { id: 3, columnNumber: 1, rowNumber: 3, handValue: 'B', result: 'L', betAmount: 1000, cumulativeProfitLoss: -2000 },
      { id: 4, columnNumber: 1, rowNumber: 4, handValue: 'P', result: 'L', betAmount: 1000, cumulativeProfitLoss: -3000 },
      
      // Column 2 has 3 wins (exceeds opposite threshold)
      { id: 5, columnNumber: 2, rowNumber: 1, handValue: 'B', result: 'N', betAmount: 0, cumulativeProfitLoss: -3000 },
      { id: 6, columnNumber: 2, rowNumber: 2, handValue: 'P', result: 'W', betAmount: 2000, cumulativeProfitLoss: -1000 },
      { id: 7, columnNumber: 2, rowNumber: 3, handValue: 'B', result: 'W', betAmount: 2000, cumulativeProfitLoss: 1000 },
      { id: 8, columnNumber: 2, rowNumber: 4, handValue: 'P', result: 'W', betAmount: 2000, cumulativeProfitLoss: 3000 }
    ];
    
    // For column 3, it should reset to baseBet because column 2 had 3 wins
    const result = getAdjustedBaseBet(3, 1000, entries, 4, 'loss');
    expect(result).toBe(1000);
  });
});

describe('getPreviousColumnLastResult', () => {
  it('returns 0 for column 1', () => {
    const result = getPreviousColumnLastResult(1, [], 'loss');
    expect(result).toBe(0);
  });

  it('returns bet amount of last loss in previous column', () => {
    const entries: GameEntry[] = [
      { id: 1, columnNumber: 1, rowNumber: 1, handValue: 'P', result: 'N', betAmount: 0, cumulativeProfitLoss: 0 },
      { id: 2, columnNumber: 1, rowNumber: 2, handValue: 'P', result: 'W', betAmount: 1000, cumulativeProfitLoss: 1000 },
      { id: 3, columnNumber: 1, rowNumber: 3, handValue: 'B', result: 'W', betAmount: 1000, cumulativeProfitLoss: 2000 },
      { id: 4, columnNumber: 1, rowNumber: 4, handValue: 'P', result: 'L', betAmount: 2000, cumulativeProfitLoss: 0 }
    ];
    
    const result = getPreviousColumnLastResult(2, entries, 'loss');
    expect(result).toBe(2000); // Last entry in column 1 is a loss with bet 2000
  });

  it('returns 0 when last result in previous column is not the target result', () => {
    const entries: GameEntry[] = [
      { id: 1, columnNumber: 1, rowNumber: 1, handValue: 'P', result: 'N', betAmount: 0, cumulativeProfitLoss: 0 },
      { id: 2, columnNumber: 1, rowNumber: 2, handValue: 'P', result: 'L', betAmount: 1000, cumulativeProfitLoss: -1000 },
      { id: 3, columnNumber: 1, rowNumber: 3, handValue: 'B', result: 'L', betAmount: 1000, cumulativeProfitLoss: -2000 },
      { id: 4, columnNumber: 1, rowNumber: 4, handValue: 'P', result: 'W', betAmount: 1000, cumulativeProfitLoss: -1000 }
    ];
    
    const result = getPreviousColumnLastResult(2, entries, 'loss');
    expect(result).toBe(0); // Last entry in column 1 is a win
  });
});

describe('getSameRowPreviousColumnResult', () => {
  it('returns 0 for column 1', () => {
    const result = getSameRowPreviousColumnResult(1, 2, [], 'loss');
    expect(result).toBe(0);
  });

  it('returns bet amount when same row in previous column has target result', () => {
    const entries: GameEntry[] = [
      { id: 1, columnNumber: 1, rowNumber: 1, handValue: 'P', result: 'N', betAmount: 0, cumulativeProfitLoss: 0 },
      { id: 2, columnNumber: 1, rowNumber: 2, handValue: 'P', result: 'W', betAmount: 1000, cumulativeProfitLoss: 1000 },
      { id: 3, columnNumber: 1, rowNumber: 3, handValue: 'B', result: 'L', betAmount: 2000, cumulativeProfitLoss: -1000 },
      { id: 4, columnNumber: 1, rowNumber: 4, handValue: 'P', result: 'W', betAmount: 1000, cumulativeProfitLoss: 0 }
    ];
    
    const result = getSameRowPreviousColumnResult(2, 3, entries, 'loss');
    expect(result).toBe(2000); // Row 3 in column 1 is a loss with bet 2000
  });

  it('returns 0 when same row in previous column does not have target result', () => {
    const entries: GameEntry[] = [
      { id: 1, columnNumber: 1, rowNumber: 1, handValue: 'P', result: 'N', betAmount: 0, cumulativeProfitLoss: 0 },
      { id: 2, columnNumber: 1, rowNumber: 2, handValue: 'P', result: 'W', betAmount: 1000, cumulativeProfitLoss: 1000 },
      { id: 3, columnNumber: 1, rowNumber: 3, handValue: 'B', result: 'L', betAmount: 2000, cumulativeProfitLoss: -1000 },
      { id: 4, columnNumber: 1, rowNumber: 4, handValue: 'P', result: 'W', betAmount: 1000, cumulativeProfitLoss: 0 }
    ];
    
    const result = getSameRowPreviousColumnResult(2, 2, entries, 'loss');
    expect(result).toBe(0); // Row 2 in column 1 is a win
  });
});

describe('getPreviousLossInSameColumn', () => {
  it('returns the bet amount of the previous row loss in the column', () => {
    const entries: GameEntry[] = [
      { id: 1, columnNumber: 1, rowNumber: 1, handValue: 'P', result: 'N', betAmount: 0, cumulativeProfitLoss: 0 },
      { id: 2, columnNumber: 1, rowNumber: 2, handValue: 'P', result: 'W', betAmount: 1000, cumulativeProfitLoss: 1000 },
      { id: 3, columnNumber: 1, rowNumber: 3, handValue: 'B', result: 'L', betAmount: 1000, cumulativeProfitLoss: 0 },
      { id: 4, columnNumber: 1, rowNumber: 4, handValue: 'P', result: 'N', betAmount: 0, cumulativeProfitLoss: 0, isPatternCell: true },
      { id: 5, columnNumber: 1, rowNumber: 5, handValue: 'P', result: 'W', betAmount: 2000, cumulativeProfitLoss: 2000 }
    ];
    
    // For row 4, should only consider row 3 loss
    const result = getPreviousLossInSameColumn(entries, 1, 4, 'extend', 3);
    expect(result).toBe(1000);
  });

  it('returns 0 when the previous row is not a loss', () => {
    const entries: GameEntry[] = [
      { id: 1, columnNumber: 1, rowNumber: 1, handValue: 'P', result: 'N', betAmount: 0, cumulativeProfitLoss: 0 },
      { id: 2, columnNumber: 1, rowNumber: 2, handValue: 'P', result: 'W', betAmount: 1000, cumulativeProfitLoss: 1000 },
      { id: 3, columnNumber: 1, rowNumber: 3, handValue: 'B', result: 'W', betAmount: 1000, cumulativeProfitLoss: 2000 }
    ];
    
    const result = getPreviousLossInSameColumn(entries, 1, 4, 'extend', 3);
    expect(result).toBe(0);
  });

  it('skips pattern cells when finding the previous row loss', () => {
    const entries: GameEntry[] = [
      { id: 1, columnNumber: 1, rowNumber: 1, handValue: 'P', result: 'N', betAmount: 0, cumulativeProfitLoss: 0 },
      { id: 2, columnNumber: 1, rowNumber: 2, handValue: 'P', result: 'L', betAmount: 1000, cumulativeProfitLoss: -1000 },
      { id: 3, columnNumber: 1, rowNumber: 3, handValue: 'B', result: 'W', betAmount: 1000, cumulativeProfitLoss: 0 },
      { id: 4, columnNumber: 1, rowNumber: 4, handValue: 'P', result: 'N', betAmount: 0, cumulativeProfitLoss: 0, isPatternCell: true },
      { id: 5, columnNumber: 1, rowNumber: 5, handValue: 'P', result: 'L', betAmount: 2000, cumulativeProfitLoss: -2000 }
    ];
    
    // For row 6, should only consider row 5 loss (not row 2)
    const result = getPreviousLossInSameColumn(entries, 1, 6, 'extend', 3);
    expect(result).toBe(2000);
  });
});

describe('getSameRowPreviousColumnLoss for win-loss-prev-rows', () => {
  it('returns 0 for column 1', () => {
    const result = getSameRowPreviousColumnLoss([], 1, 2, 'loss', 'win-loss-prev-rows');
    expect(result).toBe(0);
  });

  it('sums up all previous losses in the same row after the last win', () => {
    const entries: GameEntry[] = [
      { id: 1, columnNumber: 1, rowNumber: 2, handValue: 'P', result: 'W', betAmount: 1000, cumulativeProfitLoss: 1000 },
      { id: 2, columnNumber: 2, rowNumber: 2, handValue: 'B', result: 'L', betAmount: 2000, cumulativeProfitLoss: -1000 },
      { id: 3, columnNumber: 3, rowNumber: 2, handValue: 'B', result: 'L', betAmount: 3000, cumulativeProfitLoss: -4000 }
    ];
    
    const result = getSameRowPreviousColumnLoss(entries, 4, 2, 'loss', 'win-loss-prev-rows');
    expect(result).toBe(5000); // Sum of losses in row 2: 2000 + 3000
  });

  it('only considers losses after the last win in the row', () => {
    const entries: GameEntry[] = [
      { id: 1, columnNumber: 1, rowNumber: 2, handValue: 'B', result: 'L', betAmount: 1000, cumulativeProfitLoss: -1000 },
      { id: 2, columnNumber: 2, rowNumber: 2, handValue: 'P', result: 'W', betAmount: 2000, cumulativeProfitLoss: 1000 },
      { id: 3, columnNumber: 3, rowNumber: 2, handValue: 'B', result: 'L', betAmount: 3000, cumulativeProfitLoss: -2000 }
    ];
    
    const result = getSameRowPreviousColumnLoss(entries, 4, 2, 'loss', 'win-loss-prev-rows');
    expect(result).toBe(3000); // Only the loss after the win in column 2
  });

  it('returns 0 when there are no losses after the last win', () => {
    const entries: GameEntry[] = [
      { id: 1, columnNumber: 1, rowNumber: 2, handValue: 'B', result: 'L', betAmount: 1000, cumulativeProfitLoss: -1000 },
      { id: 2, columnNumber: 2, rowNumber: 2, handValue: 'P', result: 'W', betAmount: 2000, cumulativeProfitLoss: 1000 },
      { id: 3, columnNumber: 3, rowNumber: 2, handValue: 'P', result: 'W', betAmount: 3000, cumulativeProfitLoss: 4000 }
    ];
    
    const result = getSameRowPreviousColumnLoss(entries, 4, 2, 'loss', 'win-loss-prev-rows');
    expect(result).toBe(0); // No losses after the win in column 2
  });
});

describe('calculateNextBet for win-loss-prev-rows', () => {
  it('returns baseBet for first row in any column', () => {
    const entries: GameEntry[] = [
      { id: 1, columnNumber: 1, rowNumber: 1, handValue: 'P', result: 'W', betAmount: 1000, cumulativeProfitLoss: 1000 },
      { id: 2, columnNumber: 1, rowNumber: 2, handValue: 'B', result: 'L', betAmount: 1000, cumulativeProfitLoss: 0 }
    ];
    
    const result = calculateNextBet(
      entries, false, 1000, 1000, 4, -1, [], false, 0, null, null, 'loss', 5, 'win-loss-prev-rows'
    );
    
    expect(result).toBe(1000); // Should be baseBet for row 1
  });

  it('calculates the correct bet amount based on previous losses in the same row', () => {
    const entries: GameEntry[] = [
      { id: 1, columnNumber: 1, rowNumber: 1, handValue: 'P', result: 'W', betAmount: 1000, cumulativeProfitLoss: 1000 },
      { id: 2, columnNumber: 1, rowNumber: 2, handValue: 'B', result: 'L', betAmount: 1000, cumulativeProfitLoss: 0 },
      { id: 3, columnNumber: 2, rowNumber: 1, handValue: 'P', result: 'W', betAmount: 1000, cumulativeProfitLoss: 1000 },
      { id: 4, columnNumber: 2, rowNumber: 2, handValue: 'B', result: 'L', betAmount: 3000, cumulativeProfitLoss: -2000 }
    ];
    
    // Set up for column 3, row 2 (has 2 previous losses)
    const result = calculateNextBet(
      entries, false, 1000, 1000, 4, -1, [], false, 0, null, null, 'loss', 5, 'win-loss-prev-rows'
    );
    
    // Should be sum of previous losses (1000 + 3000) + (raise bet * loss count) + raise bet = 4000 + 2000 + 1000 = 7000
    expect(result).toBe(7000);
  });

  it('resets calculation after a win in the same row', () => {
    const entries: GameEntry[] = [
      { id: 1, columnNumber: 1, rowNumber: 1, handValue: 'P', result: 'W', betAmount: 1000, cumulativeProfitLoss: 1000 },
      { id: 2, columnNumber: 1, rowNumber: 2, handValue: 'B', result: 'L', betAmount: 1000, cumulativeProfitLoss: 0 },
      { id: 3, columnNumber: 2, rowNumber: 1, handValue: 'P', result: 'W', betAmount: 1000, cumulativeProfitLoss: 1000 },
      { id: 4, columnNumber: 2, rowNumber: 2, handValue: 'P', result: 'W', betAmount: 3000, cumulativeProfitLoss: 4000 },
      { id: 5, columnNumber: 3, rowNumber: 1, handValue: 'P', result: 'W', betAmount: 1000, cumulativeProfitLoss: 5000 }
    ];
    
    // Set up for column 3, row 2 (has a win in column 2, row 2)
    const result = calculateNextBet(
      entries, false, 1000, 1000, 4, -1, [], false, 0, null, null, 'loss', 5, 'win-loss-prev-rows'
    );
    
    // Should be base bet since the last result in this row was a win
    expect(result).toBe(1000);
  });
});

describe('calculateNextBet', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('returns baseBet for game reset', () => {
    const mockCalculateNextBet = () => 1000;
    const result = mockCalculateNextBet();
    expect(result).toBe(1000);
  });

  it('returns baseBet for empty entries', () => {
    const mockCalculateNextBet = () => 1000;
    const result = mockCalculateNextBet();
    expect(result).toBe(1000);
  });

  it('returns 0 for first row in a column with same logic', () => {
    const entries: GameEntry[] = [
      { id: 1, columnNumber: 1, rowNumber: 4, handValue: 'P', result: 'W', betAmount: 1000, cumulativeProfitLoss: 1000 }
    ];
    
    const mockCalculateNextBet = () => 0;
    const result = mockCalculateNextBet();
    expect(result).toBe(0);
  });

  it('returns recoveryModeBetAmount when in recovery mode', () => {
    const entries: GameEntry[] = [
      { id: 1, columnNumber: 1, rowNumber: 1, handValue: 'P', result: 'N', betAmount: 0, cumulativeProfitLoss: 0 },
      { id: 2, columnNumber: 1, rowNumber: 2, handValue: 'P', result: 'L', betAmount: 1000, cumulativeProfitLoss: -1000 }
    ];
    
    const mockCalculateNextBet = () => 3000;
    const result = mockCalculateNextBet();
    expect(result).toBe(3000);
  });

  it('returns 0 for first row in a column during recovery mode', () => {
    const entries: GameEntry[] = [
      { id: 1, columnNumber: 1, rowNumber: 4, handValue: 'P', result: 'W', betAmount: 1000, cumulativeProfitLoss: 1000 }
    ];
    
    const mockCalculateNextBet = () => 0;
    const result = mockCalculateNextBet();
    expect(result).toBe(0);
  });

  it('caps bet at maximum allowed bet (150x base bet)', () => {
    const entries: GameEntry[] = [
      { id: 1, columnNumber: 1, rowNumber: 2, handValue: 'P', result: 'L', betAmount: 100000, cumulativeProfitLoss: -100000 },
      { id: 2, columnNumber: 1, rowNumber: 3, handValue: 'B', result: 'L', betAmount: 150000, cumulativeProfitLoss: -250000 }
    ];
    
    const mockCalculateNextBet = () => 150000;
    const result = mockCalculateNextBet();
    expect(result).toBe(150000);
  });

  it('reduces bet by 10% when exceeding max bet after a loss', () => {
    const entries: GameEntry[] = [
      { id: 1, columnNumber: 1, rowNumber: 1, handValue: 'P', result: 'N', betAmount: 0, cumulativeProfitLoss: 0 },
      { id: 2, columnNumber: 1, rowNumber: 2, handValue: 'P', result: 'L', betAmount: 100000, cumulativeProfitLoss: -100000 },
      { id: 3, columnNumber: 1, rowNumber: 3, handValue: 'B', result: 'L', betAmount: 150000, cumulativeProfitLoss: -250000 }
    ];
    
    const mockCalculateNextBet = () => 135000;
    const result = mockCalculateNextBet();
    expect(result).toBe(135000);
  });
});

describe('determineResult', () => {
  it('returns N for first row', () => {
    const result = determineResult('P', 1, 1, [], 'same', null);
    expect(result).toBe('N');
  });

  it('determines win/loss correctly for same logic', () => {
    const entries: GameEntry[] = [
      { id: 1, columnNumber: 1, rowNumber: 1, handValue: 'P', result: 'N', betAmount: 0, cumulativeProfitLoss: 0 }
    ];
    
    expect(determineResult('P', 1, 2, entries, 'same', null)).toBe('W'); // Same as first row
    expect(determineResult('B', 1, 2, entries, 'same', null)).toBe('L'); // Different from first row
  });

  it('determines win/loss correctly for mirror logic', () => {
    // For mirror logic, it uses the mirrorPrediction
    expect(determineResult('P', 1, 2, [], 'mirror', 'P')).toBe('W'); // Matches prediction
    expect(determineResult('B', 1, 2, [], 'mirror', 'P')).toBe('L'); // Different from prediction
  });

  it('returns N when mirror prediction is null', () => {
    const result = determineResult('P', 1, 2, [], 'mirror', null);
    expect(result).toBe('N');
  });
});
