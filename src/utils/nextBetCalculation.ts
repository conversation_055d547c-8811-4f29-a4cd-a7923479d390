
import { GameEntry, PredictionLogicType, ExtensionFactor } from '@/types/game';
import { toast } from "@/hooks/use-toast";

// Function to determine if a cell is a pattern cell
export const isPatternCell = (
  rowNumber: number,
  predictionLogic: string,
  extensionFactor: number,
  baseRowCount: number
): boolean => {
  if (predictionLogic !== 'extend') return false;
  
  // For extend logic, Row 1 and Row 4 are pattern cells
  return rowNumber === 1 || rowNumber === 4;
};

// Function to get the pattern section 
export const getPatternSection = (
  rowNumber: number,
  baseRowCount: number = 3
): number => {
  if (rowNumber <= baseRowCount) {
    return 0; // First section
  } else if (rowNumber <= 2 * baseRowCount) {
    return 1; // Second section
  } else {
    return 2; // Third section
  }
};

// Function to get the pattern source row
export const getPatternSourceRow = (
  rowNumber: number,
  baseRowCount: number = 3
): number => {
  // For pattern cells in extend mode, use the last row of the previous section
  if (rowNumber <= baseRowCount) {
    return rowNumber;
  } else if (rowNumber === baseRowCount + 1) {
    return baseRowCount; // Use the last row of the first section
  } else if (rowNumber <= 2 * baseRowCount) {
    return rowNumber - (baseRowCount + 1) + 1;
  } else {
    return rowNumber - (2 * baseRowCount + 1) + 1;
  }
};

// Function to get the source row for bet calculation
export const getSourceRowForBet = (
  rowNumber: number,
  baseRowCount: number = 3,
  predictionLogic: string = 'extend',
  extensionFactor: number = 2
): number => {
  if (predictionLogic !== 'extend') return rowNumber;
  
  // Skip pattern cells when determining source row for bet calculation
  if (isPatternCell(rowNumber, predictionLogic, extensionFactor, baseRowCount)) {
    // If it's a pattern cell, use source row
    return getPatternSourceRow(rowNumber, baseRowCount);
  }
  
  // Handle rows after pattern cells
  if (rowNumber > baseRowCount + 1 && rowNumber <= 2 * baseRowCount) {
    // This is in the second section
    return rowNumber - (baseRowCount + 1);
  } else if (rowNumber > 2 * baseRowCount + 1) {
    // This is in the third section
    return rowNumber - (2 * baseRowCount + 1);
  }
  
  // For rows in the first section or default case
  return rowNumber;
};

// Function to calculate the next position
export const calculateNextPosition = (
  entries: GameEntry[],
  rowCount: number,
  predictionLogic: string,
  extensionFactor: number
): { columnNumber: number; rowNumber: number } => {
  if (entries.length === 0) {
    return { columnNumber: 1, rowNumber: 1 };
  }

  const lastEntry = entries[entries.length - 1];
  let nextColumnNumber = lastEntry.columnNumber;
  let nextRowNumber = lastEntry.rowNumber + 1;

  if (predictionLogic === 'extend') {
    const effectiveRowCount = rowCount * extensionFactor;
    if (nextRowNumber > effectiveRowCount) {
      nextColumnNumber++;
      nextRowNumber = 1;
    }
  } else {
    if (nextRowNumber > rowCount) {
      nextColumnNumber++;
      nextRowNumber = 1;
    }
  }

  return { columnNumber: nextColumnNumber, rowNumber: nextRowNumber };
};

// Function to get the loss threshold
export const getLossThreshold = (rowCount: number): number => {
  return Math.floor(rowCount / 2);
};

// Function to get the win threshold
export const getWinThreshold = (rowCount: number): number => {
  return Math.ceil(rowCount / 2);
};

// Function to get the count of losses in a column
export const getColumnLossCount = (entries: GameEntry[], columnNumber: number): number => {
  return entries.filter(entry => entry.columnNumber === columnNumber && entry.result === 'L').length;
};

// Function to get the count of wins in a column
export const getColumnWinCount = (entries: GameEntry[], columnNumber: number): number => {
  return entries.filter(entry => entry.columnNumber === columnNumber && entry.result === 'W').length;
};

// Function to get the adjusted base bet
export const getAdjustedBaseBet = (
  columnNumber: number,
  baseBet: number,
  entries: GameEntry[],
  rowCount: number,
  betStrategy: string
): number => {
  if (columnNumber === 1) {
    return baseBet;
  }

  const lossThreshold = getLossThreshold(rowCount);
  const winThreshold = getWinThreshold(rowCount);
  let multiplier = 0;

  for (let i = 1; i < columnNumber; i++) {
    const columnLossCount = getColumnLossCount(entries, i);
    const columnWinCount = getColumnWinCount(entries, i);

    if (columnLossCount >= lossThreshold) {
      multiplier++;
    }

    // Reset columns count when a column exceeds the opposite threshold
    if (columnWinCount >= winThreshold) {
      multiplier = 0;
    }
  }

  let adjustedBaseBet = baseBet + baseBet * multiplier;
  return adjustedBaseBet;
};

// Function to get the previous column's last result
export const getPreviousColumnLastResult = (
  columnNumber: number,
  entries: GameEntry[],
  resultType: 'loss' | 'win'
): number => {
  if (columnNumber <= 1) {
    return 0;
  }

  const previousColumnEntries = entries.filter(
    entry => entry.columnNumber === columnNumber - 1
  );

  if (previousColumnEntries.length === 0) {
    return 0;
  }

  let lastEntry: GameEntry | undefined;
  let highestRowNumber = 0;
  
  previousColumnEntries.forEach(entry => {
    if (entry.rowNumber > highestRowNumber) {
      highestRowNumber = entry.rowNumber;
      lastEntry = entry;
    }
  });
  
  if (!lastEntry) {
    return 0;
  }

  if (resultType === 'loss' && lastEntry.result === 'L') {
    return lastEntry.betAmount;
  } else if (resultType === 'win' && lastEntry.result === 'W') {
    return lastEntry.betAmount;
  }

  return 0;
};

// Function to get the same row's previous column result
export const getSameRowPreviousColumnResult = (
  columnNumber: number,
  rowNumber: number,
  entries: GameEntry[],
  resultType: 'loss' | 'win'
): number => {
  if (columnNumber <= 1) {
    return 0;
  }

  const entry = entries.find(
    entry => entry.columnNumber === columnNumber - 1 && entry.rowNumber === rowNumber
  );

  if (!entry) {
    return 0;
  }

  if (resultType === 'loss' && entry.result === 'L') {
    return entry.betAmount;
  } else if (resultType === 'win' && entry.result === 'W') {
    return entry.betAmount;
  }

  return 0;
};

// Updated function to get the previous loss in the same column
export function getPreviousLossInSameColumn(
  entries: GameEntry[],
  columnNumber: number,
  rowNumber: number,
  predictionLogic: PredictionLogicType = 'same',
  extensionFactor: ExtensionFactor = 2,
  rowCount: number = 4
): number {
  if (rowNumber <= 1) return 0;
  
  const isExtendMode = predictionLogic === 'extend';
  
  // For Row 5 in Column 1 with extend logic, prioritize Row 3 losses
  if (isExtendMode && columnNumber === 1 && rowNumber === 5) {
    // First check for loss in Row 3
    const row3Entry = entries.find(
      entry => entry.columnNumber === 1 && entry.rowNumber === 3 && entry.result === 'L'
    );
    
    if (row3Entry) {
      return row3Entry.betAmount;
    }
    
    // Then check for loss in Row 4
    const row4Entry = entries.find(
      entry => entry.columnNumber === 1 && entry.rowNumber === 4 && entry.result === 'L'
    );
    
    if (row4Entry) {
      return row4Entry.betAmount;
    }
  }
  
  // Get all entries in the current column before the current row
  const previousEntries = entries.filter(
    entry => entry.columnNumber === columnNumber && 
            entry.rowNumber < rowNumber &&
            !entry.isPatternCell
  );
  
  // Sort by row number descending to get the most recent entries first
  previousEntries.sort((a, b) => b.rowNumber - a.rowNumber);
  
  // Find the first loss
  const previousLoss = previousEntries.find(entry => entry.result === 'L');
  
  return previousLoss ? previousLoss.betAmount : 0;
}

// Function to get the same row's previous column loss
export const getSameRowPreviousColumnLoss = (
  entries: GameEntry[],
  currentColumnNumber: number,
  currentRowNumber: number,
  betStrategy: string = 'loss',
  predictionLogic: string = 'same-no-recovery'
): number => {
  if (currentColumnNumber <= 1) return 0;

  // Implementation specific to win-loss logic
  if (predictionLogic === 'win-loss-prev-rows') {
    // Check if there was a WIN in the same row of the previous column
    const prevColEntry = entries.find(
      entry => entry.columnNumber === currentColumnNumber - 1 && 
              entry.rowNumber === currentRowNumber
    );
    
    // If the previous column in the same row was a WIN, we don't consider any losses
    if (prevColEntry && prevColEntry.handValue === 'P') {
      return 0;
    }
    
    // Find the last column with a win for this row
    let lastWinColumn = 0;
    for (let col = 1; col < currentColumnNumber; col++) {
      const entry = entries.find(e => 
        e.columnNumber === col && 
        e.rowNumber === currentRowNumber &&
        e.handValue === 'P'  // 'P' indicates a win
      );
      
      if (entry) {
        lastWinColumn = col;
      }
    }
    
    // Sum losses that occurred after the last win
    let totalLoss = 0;
    for (let col = lastWinColumn + 1; col < currentColumnNumber; col++) {
      const entry = entries.find(e => 
        e.columnNumber === col && 
        e.rowNumber === currentRowNumber &&
        e.handValue === 'B'  // 'B' indicates a loss
      );
      
      if (entry) {
        totalLoss += entry.betAmount;
      }
    }
    
    return totalLoss;
  } 
  // Implementation specific to win-loss logic
  else if (predictionLogic === 'win-loss') {
    // For win-loss, find the most recent loss in the same row but previous column
    const entry = entries.find(
      entry => entry.columnNumber === currentColumnNumber - 1 && 
              entry.rowNumber === currentRowNumber && 
              entry.handValue === 'B'  // For win-loss, look for 'B' (loss)
    );
    
    return entry ? entry.betAmount : 0;
  }
  
  // Implementation for same-no-recovery and extend
  const targetResult = betStrategy === 'loss' ? 'L' : 'W';
  
  const entry = entries.find(
    entry => entry.columnNumber === currentColumnNumber - 1 && 
            entry.rowNumber === currentRowNumber && 
            entry.result === targetResult
  );
  
  return entry ? entry.betAmount : 0;
};

// Function to get the column loss count
export const getColumnLossCountForThreshold = (entries: GameEntry[], columnNumber: number, rowCount: number, betStrategy: string): number => {
  let lossCount = 0;
  for (let i = 1; i <= rowCount; i++) {
    const entry = entries.find(entry => entry.columnNumber === columnNumber && entry.rowNumber === i);
    if (entry && entry.result === 'L') {
      lossCount++;
    }
  }
  return lossCount;
};

// Function to get the previous column's row 2 loss
export const getPreviousColumnRow2Loss = (
  columnNumber: number,
  entries: GameEntry[]
): number => {
  if (columnNumber <= 1) {
    return 0;
  }

  const row2Entry = entries.find(
    entry => entry.columnNumber === columnNumber - 1 && 
            entry.rowNumber === 2 && 
            !entry.isPatternCell
  );
  
  return row2Entry?.result === 'L' ? row2Entry.betAmount : 0;
};

// Function to calculate the next bet amount
export const calculateNextBet = (
  entries: GameEntry[],
  isGameReset: boolean,
  baseBet: number,
  raiseBet: number,
  rowCount: number,
  lastResetIndex: number,
  resetIndices: number[],
  recoveryModeActive: boolean,
  recoveryModeBetAmount: number,
  currentResetColumn: number | null,
  mirrorPrediction: 'P' | 'B' | null,
  betStrategy: string = 'loss',
  resetBettingHands: number = 5,
  predictionLogic: string = 'same',
  extensionFactor: number = 2
): number => {
  if (isGameReset) {
    return baseBet;
  }

  if (entries.length === 0) {
    return baseBet;
  }

  const { columnNumber, rowNumber } = calculateNextPosition(entries, rowCount, predictionLogic, extensionFactor);

  if (rowNumber === 1 && predictionLogic === 'same') {
    return 0;
  }

  if (recoveryModeActive) {
    if (rowNumber === 1) {
      return 0;
    }

    return recoveryModeBetAmount;
  }

  if (currentResetColumn === columnNumber && rowNumber === 1) {
    return 0;
  }

  if (predictionLogic === 'win-loss-prev-rows') {
    toast({
      title: "Win-Loss-Previous-Rows",
      description: "Win-Loss-Previous-Rows logic is handled on the client side.",
    })
    return 0;
  }

  if (predictionLogic === 'extend') {
    const isPatternCellRow = isPatternCell(rowNumber, predictionLogic, extensionFactor, rowCount);
    
    // Pattern cells don't have bets
    if (isPatternCellRow) {
      return 0;
    }
    
    // Special case for Column 1
    if (columnNumber === 1) {
      // Row 1 and Row 4 are pattern cells (no bet)
      if (rowNumber === 1 || rowNumber === 4) {
        return 0;
      }
      
      // Row 2 and Row 3 use base bet
      if (rowNumber === 2 || rowNumber === 3) {
        console.log(`Column 1, Row ${rowNumber}: Using base bet: ${baseBet}`);
        return baseBet;
      }
      
      // For Row 5, check Row 3 specifically for a loss
      if (rowNumber === 5) {
        const row3Entry = entries.find(
          entry => entry.columnNumber === columnNumber && entry.rowNumber === 3 && entry.result === 'L'
        );
        
        if (row3Entry) {
          const betAmount = row3Entry.betAmount + raiseBet;
          console.log(`Column 1, Row 5: Using Row 3 Loss + RBA: ${betAmount} (${row3Entry.betAmount} + ${raiseBet})`);
          return betAmount;
        }
        
        // If Row 3 wasn't a loss, check Row 4 (pattern cell)
        const row4Entry = entries.find(
          entry => entry.columnNumber === columnNumber && entry.rowNumber === 4 && entry.result === 'L'
        );
        
        if (row4Entry) {
          const betAmount = row4Entry.betAmount + raiseBet;
          console.log(`Column 1, Row 5: Using Row 4 Loss + RBA: ${betAmount} (${row4Entry.betAmount} + ${raiseBet})`);
          return betAmount;
        }
        
        // If no specific row loss, use regular PLSC + RBA
        const previousLoss = getPreviousLossInSameColumn(
          entries, 
          columnNumber, 
          rowNumber, 
          'extend' as PredictionLogicType, 
          extensionFactor as ExtensionFactor, 
          rowCount
        );
        
        if (previousLoss > 0) {
          const betAmount = previousLoss + raiseBet;
          console.log(`Column 1, Row 5: Using PLSC + RBA: ${betAmount} (${previousLoss} + ${raiseBet})`);
          return betAmount;
        }
        
        return baseBet;
      }
      
      // For other rows in Column 1, use PLSC + RBA, skipping pattern cells
      const previousLoss = getPreviousLossInSameColumn(
        entries, 
        columnNumber, 
        rowNumber, 
        'extend' as PredictionLogicType, 
        extensionFactor as ExtensionFactor, 
        rowCount
      );
      
      if (previousLoss > 0) {
        const betAmount = previousLoss + raiseBet;
        console.log(`Column 1, Row ${rowNumber}: Using PLSC + RBA: ${betAmount} (${previousLoss} + ${raiseBet})`);
        return betAmount;
      }
      
      return baseBet;
    }
    
    // For columns 2+, use existing logic
    let result = baseBet;
    
    // Check if previous row in same column was a WIN
    if (rowNumber > 1) {
      const prevRowSameColEntry = entries.find(entry => 
        entry.columnNumber === columnNumber && 
        entry.rowNumber === rowNumber - 1 && 
        !entry.isPatternCell
      );
      
      if (prevRowSameColEntry && prevRowSameColEntry.result === 'W') {
        return baseBet;
      }
    }
    
    // Check if previous column same row was a WIN
    if (columnNumber > 1) {
      const prevColSameRowEntry = entries.find(entry => 
        entry.columnNumber === columnNumber - 1 && 
        entry.rowNumber === rowNumber && 
        !entry.isPatternCell
      );
      
      if (prevColSameRowEntry && prevColSameRowEntry.result === 'W') {
        return baseBet;
      }
    }
    
    // Handle column transition for Row 1 (Column 2+, Row 1)
    if (rowNumber === 1 && columnNumber > 1) {
      // Updated formula: (PCLRL + RBA) + (SRPC + RBA) + RBA
      let result = raiseBet; // Start with RBA
      console.log(`Column transition: Starting with RBA: ${result}`);
      
      // Add Previous Column's last row loss + RBA
      const prevColLastRowLoss = getPreviousColumnLastResult(columnNumber, entries, 'loss');
      if (prevColLastRowLoss > 0) {
        const lossWithRaise = prevColLastRowLoss + raiseBet;
        result += lossWithRaise;
        console.log(`Adding (PCLRL + RBA): ${lossWithRaise} (${prevColLastRowLoss} + ${raiseBet})`);
      }
      
      // Add Same Row Previous Column loss + RBA
      const sameRowPrevColLoss = getSameRowPreviousColumnLoss(entries, columnNumber, rowNumber, betStrategy, predictionLogic);
      if (sameRowPrevColLoss > 0) {
        const lossWithRaise = sameRowPrevColLoss + raiseBet;
        result += lossWithRaise;
        console.log(`Adding (SRPC + RBA): ${lossWithRaise} (${sameRowPrevColLoss} + ${raiseBet})`);
      }
      
      console.log(`Final column transition bet: ${result}`);
      return result;
    }
    
    // Handle Row 5 (after pattern cell)
    if (rowNumber === 5) {
      // Start with RBA
      let result = raiseBet;
      
      // Add Same Row Previous Column loss + RBA
      const sameRowPrevColLoss = getSameRowPreviousColumnLoss(entries, columnNumber, rowNumber, betStrategy, predictionLogic);
      if (sameRowPrevColLoss > 0) {
        result += (sameRowPrevColLoss + raiseBet);
      }
      
      // Special: Find the loss in column 3 regardless of row
      // This is specific to the requirement for Row 5 betting
      const col3Loss = entries.find(e => 
        e.columnNumber === columnNumber - 1 && 
        e.result === 'L'
      );
      
      if (col3Loss) {
        result += (col3Loss.betAmount + raiseBet);
      }
      
      return result;
    }
    
    // Handle Rows 3+ (Column 2+)
    if (rowNumber >= 3 && !isPatternCell(rowNumber, predictionLogic, extensionFactor, rowCount)) {
      // Check for any wins in the current column or previous column that would reset the bet
      const hasPreviousWinInColumn = entries.some(entry => 
        entry.columnNumber === columnNumber && 
        entry.rowNumber < rowNumber && 
        entry.result === 'W' &&
        !entry.isPatternCell
      );
      
      if (hasPreviousWinInColumn) {
        return baseBet;
      }
      
      // Check if the last row of previous column was a win
      const lastRowInPrevCol = entries.filter(e => 
        e.columnNumber === columnNumber - 1 && 
        !e.isPatternCell
      ).sort((a, b) => b.rowNumber - a.rowNumber)[0];
      
      if (lastRowInPrevCol?.result === 'W') {
        return baseBet;
      }
      
      // Use only previous row loss, not any previous loss
      const previousLoss = getPreviousLossInSameColumn(
        entries, columnNumber, rowNumber, 'extend' as PredictionLogicType, extensionFactor as ExtensionFactor, rowCount
      );
      const sameRowPreviousColumnLoss = getSameRowPreviousColumnLoss(entries, columnNumber, rowNumber, betStrategy, predictionLogic);
      
      // Check if previous column, same row was a WIN (not a loss)
      const prevColSameRowEntry = entries.find(entry => 
        entry.columnNumber === columnNumber - 1 && 
        entry.rowNumber === rowNumber && 
        !entry.isPatternCell
      );
      
      const isPrevColSameRowWin = prevColSameRowEntry && prevColSameRowEntry.result === 'W';
      
      if (isPrevColSameRowWin) {
        return baseBet;
      }
      
      // Calculate using the formula (SRPC + RBA) + (PLSC + RBA) + RBA
      result = raiseBet; // Start with RBA
      
      if (sameRowPreviousColumnLoss > 0) {
        result += (sameRowPreviousColumnLoss + raiseBet);
      }
      
      if (previousLoss > 0) {
        result += (previousLoss + raiseBet);
      }
      
      const maxBet = baseBet * 150;
      if (result > maxBet) {
        result = maxBet;
      }
      
      return result;
    }
    
    // Handle pattern cells
    if (isPatternCell(rowNumber, predictionLogic, extensionFactor, rowCount)) {
      return 0; // Pattern cells don't have bets
    }
    
    // Default case for other scenarios
    return baseBet;
  }

  let result = baseBet;

  const maxBet = baseBet * 150;
  if (result > maxBet) {
    // Reduce bet by 10% when exceeding max bet after a loss
    if (entries.length > 0 && entries[entries.length - 1].result === 'L') {
      result = result * 0.9;
    } else {
      result = maxBet;
    }
  }
  
  return result;
};

// Function to determine the result of a hand
export const determineResult = (
  handValue: 'P' | 'B',
  columnNumber: number,
  rowNumber: number,
  entries: GameEntry[],
  predictionLogic: string,
  mirrorPrediction: 'P' | 'B' | null
): 'W' | 'L' | 'N' => {
  if (rowNumber === 1) {
    return 'N';
  }

  if (predictionLogic === 'same') {
    const firstEntry = entries.find(entry => entry.columnNumber === columnNumber && entry.rowNumber === 1);
    return handValue === firstEntry?.handValue ? 'W' : 'L';
  }

  if (predictionLogic === 'mirror') {
    if (mirrorPrediction === null) {
      return 'N';
    }
    return handValue === mirrorPrediction ? 'W' : 'L';
  }
  
  return 'N';
};
