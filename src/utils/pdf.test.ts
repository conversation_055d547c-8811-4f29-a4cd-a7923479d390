
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { exportToPDF } from './pdf';
import { GameEntry } from '@/types/game';
import jsPDF from 'jspdf';

// Create more complete mock for jsPDF
vi.mock('jspdf', () => {
  return {
    default: vi.fn().mockImplementation(() => ({
      setFontSize: vi.fn(),
      setFont: vi.fn(),
      text: vi.fn(),
      setTextColor: vi.fn(),
      setFillColor: vi.fn(),
      rect: vi.fn(),
      line: vi.fn(),
      addPage: vi.fn(),
      save: vi.fn(),
      setProperties: vi.fn(),
      setDrawColor: vi.fn(),
      setLineWidth: vi.fn(),
      circle: vi.fn(),
      internal: {
        pageSize: {
          width: 595.28,
          height: 841.89
        }
      }
    }))
  };
});

describe('exportToPDF', () => {
  let mockEntries: GameEntry[];
  
  beforeEach(() => {
    mockEntries = [
      { id: 1, columnNumber: 1, rowNumber: 1, handValue: 'P', result: 'N', betAmount: 0, cumulativeProfitLoss: 0 },
      { id: 2, columnNumber: 1, rowNumber: 2, handValue: 'B', result: 'L', betAmount: 1000, cumulativeProfitLoss: -1000 },
      { id: 3, columnNumber: 1, rowNumber: 3, handValue: 'P', result: 'W', betAmount: 2000, cumulativeProfitLoss: 1000 }
    ];
    
    // Reset mocks
    vi.clearAllMocks();
  });
  
  it('creates a new PDF document', () => {
    exportToPDF(mockEntries, true);
    expect(jsPDF).toHaveBeenCalled();
  });
  
  it('calls save method to download the PDF', () => {
    exportToPDF(mockEntries, true);
    const mockPdfInstance = (jsPDF as unknown as ReturnType<typeof vi.fn>).mock.results[0].value;
    expect(mockPdfInstance.save).toHaveBeenCalled();
  });
  
  it('creates a detailed PDF when detailed flag is true', () => {
    exportToPDF(mockEntries, true);
    const mockPdfInstance = (jsPDF as unknown as ReturnType<typeof vi.fn>).mock.results[0].value;
    
    // Check that detailed formatting methods were called
    expect(mockPdfInstance.setFontSize).toHaveBeenCalled();
    expect(mockPdfInstance.text).toHaveBeenCalled();
  });
  
  it('creates a simple PDF when detailed flag is false', () => {
    exportToPDF(mockEntries, false);
    const mockPdfInstance = (jsPDF as unknown as ReturnType<typeof vi.fn>).mock.results[0].value;
    
    // Check that simple formatting methods were called
    expect(mockPdfInstance.setFontSize).toHaveBeenCalled();
    expect(mockPdfInstance.text).toHaveBeenCalled();
  });
  
  it('handles extend mode with different extension factors', () => {
    // Test with extend mode and factor 2
    exportToPDF(mockEntries, true, 'extend', 2);
    let mockPdfInstance = (jsPDF as unknown as ReturnType<typeof vi.fn>).mock.results[0].value;
    expect(mockPdfInstance.save).toHaveBeenCalled();
    
    // Reset mocks
    vi.clearAllMocks();
    
    // Test with extend mode and factor 3
    exportToPDF(mockEntries, true, 'extend', 3);
    mockPdfInstance = (jsPDF as unknown as ReturnType<typeof vi.fn>).mock.results[0].value;
    expect(mockPdfInstance.save).toHaveBeenCalled();
  });
  
  it('calculates correct grid dimensions for extend mode', () => {
    // Test with extend mode, base row count 3, and factor 2 (should be 6 rows)
    exportToPDF(mockEntries, true, 'extend', 2);
    let mockPdfInstance = (jsPDF as unknown as ReturnType<typeof vi.fn>).mock.results[0].value;
    expect(mockPdfInstance.rect).toHaveBeenCalled();
    
    // Reset mocks
    vi.clearAllMocks();
    
    // Add a row 4 entry (pattern cell for extend mode)
    const extendedEntries: GameEntry[] = [
      ...mockEntries,
      { id: 4, columnNumber: 1, rowNumber: 4, handValue: 'P', result: 'N', betAmount: 0, cumulativeProfitLoss: 1000, isPatternCell: true }
    ];
    
    // Test with extend mode, base row count 3, and factor 3 (should be 9 rows)
    exportToPDF(extendedEntries, true, 'extend', 3);
    mockPdfInstance = (jsPDF as unknown as ReturnType<typeof vi.fn>).mock.results[0].value;
    expect(mockPdfInstance.rect).toHaveBeenCalled();
  });
  
  it('handles empty entries array', () => {
    exportToPDF([], true);
    const mockPdfInstance = (jsPDF as unknown as ReturnType<typeof vi.fn>).mock.results[0].value;
    
    // Should still create a PDF with headers
    expect(mockPdfInstance.text).toHaveBeenCalled();
    expect(mockPdfInstance.save).toHaveBeenCalled();
  });
  
  it('handles pattern cells in extend mode', () => {
    const extendedEntries: GameEntry[] = [
      { id: 1, columnNumber: 1, rowNumber: 1, handValue: 'P', result: 'N', betAmount: 0, cumulativeProfitLoss: 0 },
      { id: 2, columnNumber: 1, rowNumber: 2, handValue: 'B', result: 'L', betAmount: 1000, cumulativeProfitLoss: -1000 },
      { id: 3, columnNumber: 1, rowNumber: 3, handValue: 'P', result: 'W', betAmount: 2000, cumulativeProfitLoss: 1000 },
      { id: 4, columnNumber: 1, rowNumber: 4, handValue: 'P', result: 'N', betAmount: 0, cumulativeProfitLoss: 1000, isPatternCell: true }
    ];
    
    exportToPDF(extendedEntries, true, 'extend', 2);
    const mockPdfInstance = (jsPDF as unknown as ReturnType<typeof vi.fn>).mock.results[0].value;
    expect(mockPdfInstance.text).toHaveBeenCalled();
  });
});
