
import jsPDF from 'jspdf';
import { GameEntry } from '@/types/game';
import { LossStreak } from './types';
import { calculateLossStreaks, identifyRecoveryModes } from './helpers';
import { drawPageHeader } from './components';

export const drawRecoveryModeAnalysis = (doc: jsPDF, entries: GameEntry[]) => {
  // Identify all recovery modes
  const recoveryModes = identifyRecoveryModes(entries);
  
  if (recoveryModes.startIndices.length === 0) return;
  
  doc.addPage('landscape');
  drawPageHeader(doc, 2, 2); // Page number for recovery analysis
  
  // Add Analysis Title
  doc.setFontSize(16);
  doc.setFont('helvetica', 'bold');
  doc.setTextColor(0, 0, 0);
  doc.text('Recovery Mode Analysis', 20, 60);
  
  // Draw table headers
  doc.setFontSize(12);
  doc.setFillColor(243, 244, 246);
  doc.rect(20, 70, doc.internal.pageSize.width - 40, 20, 'F');
  
  doc.setTextColor(0, 0, 0);
  doc.text('Instance', 30, 82);
  doc.text('Starting Column', 110, 82);
  doc.text('Ending Column', 190, 82);
  doc.text('Duration (Columns)', 270, 82);
  
  // Draw table content
  let yPosition = 100;
  
  recoveryModes.startIndices.forEach((startIdx, index) => {
    const endIdx = recoveryModes.endIndices[index];
    const startEntry = entries[startIdx];
    const endEntry = entries[endIdx];
    
    const isEven = index % 2 === 0;
    if (isEven) {
      doc.setFillColor(249, 250, 251);
      doc.rect(20, yPosition - 8, doc.internal.pageSize.width - 40, 20, 'F');
    }
    
    doc.setFontSize(11);
    doc.setFont('helvetica', 'normal');
    doc.text(`Recovery #${index + 1}`, 30, yPosition);
    doc.text(`Column ${startEntry.columnNumber}`, 110, yPosition);
    doc.text(`Column ${endEntry.columnNumber}`, 190, yPosition);
    doc.text(`${endEntry.columnNumber - startEntry.columnNumber + 1}`, 270, yPosition);
    
    yPosition += 20;
  });
  
  // Add summary
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.text(`Total Recovery Instances: ${recoveryModes.startIndices.length}`, 20, yPosition + 20);
};

export const drawLossStreakAnalysis = (doc: jsPDF, entries: GameEntry[]) => {
  const lossStreaks = calculateLossStreaks(entries);
  if (lossStreaks.length === 0) return;

  doc.addPage('landscape');
  drawPageHeader(doc, 3, 3); // Page number for loss streak analysis
  
  // Add Analysis Title
  doc.setFontSize(16);
  doc.setFont('helvetica', 'bold');
  doc.setTextColor(0, 0, 0);
  doc.text('Continuous Losses Analysis', 20, 60);

  // Draw table headers
  doc.setFontSize(12);
  doc.setFillColor(243, 244, 246);
  doc.rect(20, 70, doc.internal.pageSize.width - 40, 20, 'F');
  
  doc.setTextColor(0, 0, 0);
  doc.text('Continuous Losses', 30, 82);
  doc.text('Occurrences', 200, 82);

  // Draw table content
  let yPosition = 100;
  lossStreaks.forEach((streak, index) => {
    const isEven = index % 2 === 0;
    if (isEven) {
      doc.setFillColor(249, 250, 251);
      doc.rect(20, yPosition - 8, doc.internal.pageSize.width - 40, 20, 'F');
    }

    doc.setFontSize(11);
    doc.setFont('helvetica', 'normal');
    doc.text(`${streak.length} losses in a row`, 30, yPosition);
    doc.text(streak.occurrences.toString(), 200, yPosition);
    
    yPosition += 20;
  });
};
