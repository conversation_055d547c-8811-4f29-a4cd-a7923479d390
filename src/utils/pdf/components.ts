
import jsPDF from 'jspdf';
import { PDFPageConfig, PDFGridOptions } from './types';

export const drawGridHeader = (doc: jsPDF, columns: number[], startX: number, startY: number, cellWidth: number) => {
  doc.setFontSize(10);
  doc.setFont('helvetica', 'bold');
  columns.forEach((column, index) => {
    doc.text(`Col ${column}`, startX + (index * cellWidth) + (cellWidth / 2), startY, { align: 'center' });
  });
};

export const drawPageHeader = (doc: jsPDF, pageNumber: number, totalPages: number) => {
  // Add header with styling
  doc.setFillColor(79, 70, 229);
  doc.rect(0, 0, doc.internal.pageSize.width, 40, 'F');
  
  doc.setTextColor(255, 255, 255);
  doc.setFontSize(24);
  doc.setFont('helvetica', 'bold');
  doc.text('Betting History Report', 15, 25);
  
  // Add timestamp and page number
  doc.setFontSize(10);
  doc.text(`Generated on: ${new Date().toLocaleString()}`, 15, 35);
  doc.text(`Page ${pageNumber} of ${totalPages}`, doc.internal.pageSize.width - 30, 35);
};

export const drawGrid = (config: PDFPageConfig, gridOptions: PDFGridOptions) => {
  const { doc } = config;
  const { startX, startY, columnWidth, rowHeight, entries, rowCount, columnCount } = gridOptions;
  
  // Draw grid cells
  doc.setDrawColor(200, 200, 200);
  doc.setLineWidth(0.5);
  
  for (let col = 0; col < columnCount; col++) {
    for (let row = 0; row < rowCount; row++) {
      const cellX = startX + (col * columnWidth);
      const cellY = startY + (row * rowHeight);
      doc.rect(cellX, cellY, columnWidth, rowHeight);
    }
  }
};
