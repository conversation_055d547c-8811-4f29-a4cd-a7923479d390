
import jsPDF from 'jspdf';
import { GameEntry } from '@/types/game';
import { PDFGridOptions } from './types';

// Helper function to draw a single cell
const drawCell = (
  doc: jsPDF,
  x: number,
  y: number,
  width: number,
  height: number,
  text: string,
  fillColor: string | null = null,
  textColor: string = '#000000'
) => {
  if (fillColor) {
    doc.setFillColor(fillColor);
    doc.rect(x, y, width, height, 'F');
  }
  doc.setTextColor(textColor);
  doc.text(text, x + width / 2, y + height / 2, {
    align: 'center',
    baseline: 'middle'
  });
};

// Helper function to draw column and row labels
const drawAxisLabels = (
  doc: jsPDF,
  startX: number,
  startY: number,
  columnWidth: number,
  rowHeight: number,
  columnCount: number,
  rowCount: number
) => {
  doc.setFontSize(9);
  doc.setTextColor('#666666');

  // Column labels
  for (let i = 1; i <= columnCount; i++) {
    doc.text(i.toString(), startX + i * columnWidth, startY - rowHeight / 2, {
      align: 'center',
      baseline: 'middle'
    });
  }

  // Row labels
  for (let i = 1; i <= rowCount; i++) {
    doc.text(i.toString(), startX - columnWidth / 2, startY + i * rowHeight, {
      align: 'center',
      baseline: 'middle'
    });
  }
};

// Helper function to draw grid lines
const drawGridLines = (
  doc: jsPDF,
  startX: number,
  startY: number,
  columnWidth: number,
  rowHeight: number,
  columnCount: number,
  rowCount: number
) => {
  doc.setLineWidth(0.1);
  doc.setDrawColor('#CCCCCC');

  // Vertical lines
  for (let i = 0; i <= columnCount; i++) {
    doc.line(
      startX + i * columnWidth,
      startY,
      startX + i * columnWidth,
      startY + rowCount * rowHeight
    );
  }

  // Horizontal lines
  for (let i = 0; i <= rowCount; i++) {
    doc.line(
      startX,
      startY + i * rowHeight,
      startX + columnCount * columnWidth,
      startY + i * rowHeight
    );
  }
};

// Helper function to add a key/legend
const addKey = (doc: jsPDF, startX: number, startY: number) => {
  const keyItems = [
    { color: '#F2FCE2', label: 'Correct Prediction' },
    { color: '#FFDEE2', label: 'Incorrect Prediction' },
    { color: '#0d0b85', label: 'Player (P)' },
    { color: '#ea384c', label: 'Banker (B)' },
    { color: '#FFFF00', label: 'Recovery Mode' },
    { color: '#ADD8E6', label: 'Reset Point' },
    { color: '#00CC00', label: 'Profit Threshold Reset' },
    { color: '#CC0000', label: 'Loss Threshold Reset' },
  ];

  let currentY = startY;
  keyItems.forEach((item) => {
    doc.setFillColor(item.color);
    doc.rect(startX, currentY, 5, 5, 'F');
    doc.setTextColor('#000000');
    doc.setFontSize(8);
    doc.text(item.label, startX + 7, currentY + 4);
    currentY += 7;
  });
};

// Main function to draw the grid with entries
export const drawGameGrid = (doc: jsPDF, options: PDFGridOptions) => {
  const {
    startX,
    startY,
    columnWidth,
    rowHeight,
    entries,
    rowCount,
    columnCount,
  } = options;

  // Draw grid lines
  drawGridLines(doc, startX, startY, columnWidth, rowHeight, columnCount, rowCount);

  // Draw axis labels
  drawAxisLabels(doc, startX, startY, columnWidth, rowHeight, columnCount, rowCount);

  doc.setFontSize(10);

  // Iterate through each cell and draw the content
  for (let row = 1; row <= rowCount; row++) {
    for (let col = 1; col <= columnCount; col++) {
      const entry = entries.find(
        (e) => e.columnNumber === col && e.rowNumber === row
      );

      let text = '';
      let fillColor: string | null = null;
      let textColor = '#000000';

      if (entry) {
        text = entry.handValue || '';

        // Determine fill color based on prediction
        if (entry.result === 'W') {
          fillColor = '#F2FCE2'; // Correct prediction
        } else if (entry.result === 'L') {
          fillColor = '#FFDEE2'; // Incorrect prediction
        }

        // Color for Player and Banker
        if (entry.handValue === 'P') {
          fillColor = '#0d0b85'; // Player color
          textColor = '#FFFFFF';
        } else if (entry.handValue === 'B') {
          fillColor = '#ea384c'; // Banker color
          textColor = '#FFFFFF';
        }

        // Indicate recovery mode
        if (entry.recoveryMode) {
          fillColor = '#FFFF00'; // Recovery mode color
        }

        // Indicate reset point - Only use entry.resetPoint property
        if (entry.resetPoint === true) {
          fillColor = entry.resetType === 'profit' ? '#00CC00' : '#CC0000'; // Different colors for profit/loss reset
        }
      }

      drawCell(
        doc,
        startX + (col - 1) * columnWidth,
        startY + (row - 1) * rowHeight,
        columnWidth,
        rowHeight,
        text,
        fillColor,
        textColor
      );
    }
  }

  // Add the key
  addKey(doc, startX + columnCount * columnWidth + 10, startY);
};

// Export the grid layout function that was missing
export const exportGridLayout = (
  doc: jsPDF, 
  entries: GameEntry[], 
  rowCount: number,
  predictionLogic: string,
  extensionFactor: number
) => {
  const margin = 20;
  const columnCount = entries.length > 0 
    ? Math.max(...entries.map(e => e.columnNumber))
    : 8; // Default to 8 columns if no entries
    
  // Check if it's an extend-based logic to override row count to 5
  const isExtendLogic = predictionLogic === 'extend' || predictionLogic === 'extend-reset' || predictionLogic === 'extend-pattern-change';
  const effectiveRowCount = isExtendLogic ? 5 : rowCount;
  
  // Calculate dimensions based on page size
  const availableWidth = doc.internal.pageSize.width - margin * 2;
  const availableHeight = doc.internal.pageSize.height - margin * 2;
  
  // Calculate cell dimensions to fit the grid
  const columnWidth = Math.min(30, availableWidth / (columnCount + 1)); // +1 for row labels
  const rowHeight = Math.min(30, availableHeight / (effectiveRowCount + 1)); // +1 for column labels
  
  // Set the starting position for the grid
  const startX = margin + columnWidth / 2; // Leave space for row labels
  const startY = margin + rowHeight; // Leave space for column labels
  
  // Add title
  doc.setFontSize(16);
  doc.setFont('helvetica', 'bold');
  doc.text('Betting History - Grid Layout', margin, margin);
  
  // Add subtitle with prediction logic info
  doc.setFontSize(10);
  doc.setFont('helvetica', 'normal');
  let predictionText = `Prediction Logic: ${predictionLogic}`;
  if (predictionLogic.includes('extend')) {
    predictionText += ` (Extension Factor: ${extensionFactor}x)`;
  }
  doc.text(predictionText, margin, margin + 10);
  
  // Draw the game grid
  drawGameGrid(doc, {
    startX,
    startY: startY + 15, // Extra space for title
    columnWidth,
    rowHeight,
    entries,
    rowCount: effectiveRowCount,
    columnCount
  });
  
  // Add timestamp at the bottom
  const timestamp = `Generated on: ${new Date().toLocaleString()}`;
  doc.setFontSize(8);
  doc.text(
    timestamp,
    doc.internal.pageSize.width / 2,
    doc.internal.pageSize.height - 10,
    { align: 'center' }
  );
};
