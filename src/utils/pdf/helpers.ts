
import jsPDF from 'jspdf';
import { GameEntry } from '@/types/game';
import { LossStreak, RecoveryModes } from './types';

export const calculateLossStreaks = (entries: GameEntry[]): LossStreak[] => {
  const streaks: { [key: number]: number } = {};
  let currentStreak = 0;
  let maxStreak = 0;

  // Filter out 'N' results and process only betting hands
  const bettingEntries = entries.filter(entry => entry.result !== 'N');

  bettingEntries.forEach(entry => {
    if (entry.result === 'L') {
      currentStreak++;
      maxStreak = Math.max(maxStreak, currentStreak);
    } else {
      if (currentStreak >= 2) {
        streaks[currentStreak] = (streaks[currentStreak] || 0) + 1;
      }
      currentStreak = 0;
    }
  });

  // Handle the last streak if it's a loss streak
  if (currentStreak >= 2) {
    streaks[currentStreak] = (streaks[currentStreak] || 0) + 1;
  }

  return Object.entries(streaks)
    .map(([length, occurrences]) => ({
      length: parseInt(length),
      occurrences
    }))
    .sort((a, b) => a.length - b.length);
};

// Helper to identify recovery modes in the entries
export const identifyRecoveryModes = (entries: GameEntry[]): RecoveryModes => {
  const recoveryModes = {
    startIndices: [] as number[],
    endIndices: [] as number[]
  };
  
  let inRecoveryMode = false;
  
  entries.forEach((entry, index) => {
    if (entry.recoveryMode === 'start') {
      recoveryModes.startIndices.push(index);
      inRecoveryMode = true;
    } else if (entry.recoveryMode === 'end' && inRecoveryMode) {
      recoveryModes.endIndices.push(index);
      inRecoveryMode = false;
    }
  });
  
  // Handle the case where recovery mode is still active at the end
  if (inRecoveryMode) {
    recoveryModes.endIndices.push(entries.length - 1);
  }
  
  return recoveryModes;
};

export const getColumnsPerPage = (rowCount: number): number => {
  // Adjust columns per page based on row count to maintain readability
  if (rowCount <= 3) return 10;
  if (rowCount <= 4) return 8;
  if (rowCount <= 6) return 7;
  return 5; // For 8 rows or more
};
