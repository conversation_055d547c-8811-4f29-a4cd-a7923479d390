import jsPDF from 'jspdf';
import { GameEntry, PredictionLogicType, ExtensionFactor } from '@/types/game';
import { exportGridLayout } from './gridLayout';
import { exportTableFormat } from './tableFormat';

export const exportToPDF = (
  entries: GameEntry[], 
  isDetailedView: boolean = false, 
  predictionLogic: PredictionLogicType = 'same',
  extensionFactor: ExtensionFactor = 2
) => {
  // Create PDF document in appropriate orientation
  const doc = new jsPDF(isDetailedView ? 'landscape' : 'portrait');
  
  // Set document properties
  doc.setProperties({
    title: 'Betting History Report',
    author: 'WhatNext App',
    creator: 'WhatNext App'
  });
  
  if (isDetailedView) {
    // Calculate the correct row count based on prediction logic and extension factor
    let rowCount = 0;
    
    const isExtendMode = predictionLogic === 'extend' || predictionLogic === 'extend-reset' || predictionLogic === 'extend-2-1-1';
    
    if (isExtendMode) {
      // For extend logic, get base row count and apply extension factor
      const baseRowCount = entries.length > 0
        ? Math.max(...entries.filter(e => e.originalRowNumber !== undefined 
            ? e.originalRowNumber <= 4 
            : e.rowNumber <= 4).map(e => (e.originalRowNumber !== undefined) ? e.originalRowNumber : e.rowNumber))
        : 3; // Default to 3 if no entries
      
      // Apply extension factor
      rowCount = baseRowCount * Number(extensionFactor);
      console.log(`PDF Export: Using extended row count ${rowCount} based on base ${baseRowCount} and factor ${extensionFactor}`);
    } else {
      // For non-extend logic, use the maximum row number from entries
      rowCount = entries.length > 0 
        ? Math.max(...entries.map(e => e.rowNumber))
        : 6; // Default to 6 rows if no entries
    }
    
    exportGridLayout(doc, entries, rowCount, predictionLogic, extensionFactor);
  } else {
    // Export in traditional table format
    exportTableFormat(doc, entries);
  }
  
  // Save the PDF
  doc.save('betting-history.pdf');
};
