
import jsPDF from 'jspdf';
import { GameEntry } from '@/types/game';

export const exportTableFormat = (doc: jsPDF, entries: GameEntry[]) => {
  // Set document properties
  doc.setProperties({
    title: 'Betting History Report',
    author: 'WhatNext App',
    creator: 'WhatNext App'
  });
  
  // Add header with styling
  doc.setFillColor(79, 70, 229);
  doc.rect(0, 0, doc.internal.pageSize.width, 40, 'F');
  
  doc.setTextColor(255, 255, 255);
  doc.setFontSize(24);
  doc.setFont('helvetica', 'bold');
  doc.text('Betting History Report', 15, 25);
  
  // Add timestamp
  doc.setFontSize(10);
  doc.text(`Generated on: ${new Date().toLocaleString()}`, 15, 35);
  
  // Set up table headers
  doc.setTextColor(33, 33, 33);
  doc.setFontSize(12);
  doc.setFont('helvetica', 'bold');
  
  const headers = ['Column', 'Row', 'Hand', 'Result', 'Bet Amount', 'P/L', 'Recovery', 'Reset'];
  const columnWidths = [20, 20, 20, 20, 35, 35, 30, 20];
  let startX = 15;
  let yPosition = 50;
  
  // Add header background
  doc.setFillColor(243, 244, 246);
  doc.rect(10, yPosition - 5, doc.internal.pageSize.width - 20, 10, 'F');
  
  // Draw headers
  headers.forEach((header, index) => {
    doc.text(header, startX, yPosition);
    startX += columnWidths[index];
  });
  
  // Add data rows
  doc.setFont('helvetica', 'normal');
  yPosition += 15;
  let alternateRow = false;
  
  entries.forEach((entry, index) => {
    // Add alternate row background
    if (alternateRow) {
      doc.setFillColor(249, 250, 251);
      doc.rect(10, yPosition - 5, doc.internal.pageSize.width - 20, 10, 'F');
    }
    
    // Highlight the last entry with a blue background
    if (index === entries.length - 1) {
      doc.setFillColor(219, 234, 254); // Light blue
      doc.rect(10, yPosition - 5, doc.internal.pageSize.width - 20, 10, 'F');
    }
    
    // Special highlight for reset points
    if (entry.resetPoint) {
      if (entry.resetType === 'profit') {
        doc.setFillColor(242, 252, 226); // Soft green for profit-based reset
      } else {
        doc.setFillColor(255, 248, 225); // Soft yellow for loss-based reset
      }
      doc.rect(10, yPosition - 5, doc.internal.pageSize.width - 20, 10, 'F');
    }
    
    startX = 15;
    
    // Format recovery mode status
    let recoveryStatus = '-';
    if (entry.recoveryMode === 'start') recoveryStatus = 'Started';
    else if (entry.recoveryMode === 'active') recoveryStatus = 'Active';
    else if (entry.recoveryMode === 'end') recoveryStatus = 'Ended';
    
    // Format data - using the exact values from the entry
    const rowData = [
      entry.columnNumber.toString(),
      entry.rowNumber.toString(),
      entry.handValue || 'N/A',
      entry.result || 'N/A',
      entry.betAmount === 0 ? '-' : `$${entry.betAmount.toLocaleString()}`,
      `$${entry.cumulativeProfitLoss.toLocaleString()}`,
      recoveryStatus,
      (entry.resetPoint === true) ? (entry.resetType === 'profit' ? 'P' : 'L') : '-'
    ];
    
    // Set color based on profit/loss using the cumulative value
    if (entry.cumulativeProfitLoss > 0) {
      doc.setTextColor(34, 197, 94);
    } else if (entry.cumulativeProfitLoss < 0) {
      doc.setTextColor(239, 68, 68);
    } else {
      doc.setTextColor(33, 33, 33);
    }
    
    // Draw row data
    rowData.forEach((text, index) => {
      // Use colored text for recovery mode status
      if (index === 6 && text !== '-') {
        if (text === 'Started') doc.setTextColor(245, 158, 11); // Amber
        else if (text === 'Active') doc.setTextColor(139, 92, 246); // Purple
        else if (text === 'Ended') doc.setTextColor(16, 185, 129); // Green
      }
      
      // Use different colors for Reset types
      if (index === 7) {
        if (text === 'P') {
          doc.setTextColor(34, 197, 94); // Green for profit reset
          doc.setFont('helvetica', 'bold');
        } else if (text === 'L') {
          doc.setTextColor(234, 56, 76); // Red for loss reset
          doc.setFont('helvetica', 'bold');
        }
      }
      
      doc.text(text, startX, yPosition);
      startX += columnWidths[index];
      
      // Reset text color after drawing special columns
      if ((index === 6 && text !== '-') || (index === 7 && (text === 'P' || text === 'L'))) {
        doc.setFont('helvetica', 'normal');
        if (entry.cumulativeProfitLoss > 0) {
          doc.setTextColor(34, 197, 94);
        } else if (entry.cumulativeProfitLoss < 0) {
          doc.setTextColor(239, 68, 68);
        } else {
          doc.setTextColor(33, 33, 33);
        }
      }
    });
    
    yPosition += 10;
    alternateRow = !alternateRow;
    
    // Add new page if needed
    if (yPosition > doc.internal.pageSize.height - 20) {
      doc.addPage();
      yPosition = 20;
      alternateRow = false;
    }
  });
  
  // Add summary footer using the last entry's cumulative profit/loss
  const finalProfitLoss = entries.length > 0 ? entries[entries.length - 1].cumulativeProfitLoss : 0;
  const totalBets = entries.filter(entry => entry.result !== 'N').length;
  
  doc.setFont('helvetica', 'bold');
  yPosition += 10;
  
  // Add footer background
  doc.setFillColor(243, 244, 246);
  doc.rect(10, yPosition - 5, doc.internal.pageSize.width - 20, 30, 'F');
  
  // Add summary text
  doc.setTextColor(33, 33, 33);
  doc.text(`Total Bets: ${totalBets}`, 15, yPosition + 5);
  doc.text(`Final Profit/Loss: ${finalProfitLoss >= 0 ? '+' : ''}$${finalProfitLoss.toLocaleString()}`, 15, yPosition + 15);
};
