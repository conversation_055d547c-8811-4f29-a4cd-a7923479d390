
import { GameEntry } from '@/types/game';
import jsPDF from 'jspdf';

export interface LossStreak {
  length: number;
  occurrences: number;
}

export interface RecoveryModes {
  startIndices: number[];
  endIndices: number[];
}

export interface PDFPageConfig {
  doc: jsPDF;
  pageNumber: number;
  totalPages: number;
}

// Adding PDF components types
export interface PDFGridOptions {
  startX: number;
  startY: number;
  columnWidth: number;
  rowHeight: number;
  entries: GameEntry[];
  rowCount: number;
  columnCount: number;
}

// Helper function to safely convert ExtensionFactor to number
export const extensionFactorToNumber = (factor: 2 | 3): number => {
  return Number(factor);
};

// Helper function to get the correct source row for pattern cells
export const getPatternCellSourceRow = (patternRow: number, baseRowCount: number): number => {
  // For the first pattern row (row baseRowCount+1), use the last row of section 1 (row baseRowCount)
  if (patternRow === baseRowCount + 1) {
    return baseRowCount;
  }
  // For the second pattern row (row 2*baseRowCount+1), use the last row of section 2 (row 2*baseRowCount)
  else if (patternRow === 2 * baseRowCount + 1) {
    return 2 * baseRowCount;
  }
  return 1; // Fallback
};
