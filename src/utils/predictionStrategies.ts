
import { GameEntry, PredictionLogicType, ExtensionFactor } from '@/types/game';
import {
  calculateWinLossNextBet,
  determineWinLossResult,
} from './winLossCalculation';
import {
  calculateWinLossPrevRowsNextBet,
  determineWinLossPrevRowsResult,
} from './winLossPrevRowsCalculation';
import {
  calculateSameNoRecoveryNextBet,
  determineSameNoRecoveryResult,
} from './sameNoRecoveryCalculation';
import {
  calculateExtendNextBet,
  determineExtendResult,
} from './extendPredictionCalculation';
import { 
  calculateExtendResetNextBet, 
  determineExtendResetResult 
} from './extendResetPredictionCalculation';
import {
  calculateExtendPatternChangeNextBet,
  determineExtendPatternChangeResult
} from './extendPatternChangePredictionCalculation';
import {
  shouldResetDueToProfitThreshold,
  shouldResetDueToResetPoint
} from './betCalculationService';

interface Strategy {
  calculateBet: (params: any) => number;
  determineResult: (
    handValue: 'P' | 'B',
    columnNumber: number,
    rowNumber: number,
    entries: GameEntry[],
    mirrorPrediction?: 'P' | 'B' | null
  ) => 'W' | 'L' | 'N';
}

const sameStrategy: Strategy = {
  calculateBet: (params) => {
    return params.baseBet;
  },
  determineResult: (handValue, columnNumber, rowNumber, entries) => {
    const firstRowEntry = entries.find(
      entry => entry.columnNumber === columnNumber && entry.rowNumber === 1
    );
    
    if (!firstRowEntry) return 'N';
    
    return handValue === firstRowEntry.handValue ? 'W' : 'L';
  },
};

const winLossStrategy: Strategy = {
  calculateBet: (params) => {
    return calculateWinLossNextBet(
      params.entries,
      params.isGameReset,
      params.baseBet,
      params.raiseBet,
      params.rowCount
    );
  },
  determineResult: (handValue) => determineWinLossResult(handValue),
};

const winLossPrevRowsStrategy: Strategy = {
  calculateBet: (params) => {
    return calculateWinLossPrevRowsNextBet(
      params.entries,
      params.isGameReset,
      params.baseBet,
      params.raiseBet,
      params.rowCount
    );
  },
  determineResult: (handValue) => determineWinLossPrevRowsResult(handValue),
};

const sameNoRecoveryStrategy: Strategy = {
  calculateBet: (params) => {
    return calculateSameNoRecoveryNextBet(
      params.entries,
      params.isGameReset,
      params.baseBet,
      params.raiseBet,
      params.rowCount
    );
  },
  determineResult: (handValue, columnNumber, rowNumber, entries) =>
    determineSameNoRecoveryResult(handValue, columnNumber, rowNumber, entries),
};

const extendStrategy: Strategy = {
  calculateBet: (params) => {
    return calculateExtendNextBet(
      params.entries,
      params.isGameReset,
      params.baseBet,
      params.raiseBet,
      params.rowCount,
      params.extensionFactor
    );
  },
  determineResult: (handValue, columnNumber, rowNumber, entries) =>
    determineExtendResult(handValue, columnNumber, rowNumber, entries),
};

const extendResetStrategy: Strategy = {
  calculateBet: (params) => {
    const lastEntry = params.entries.length > 0 ? params.entries[params.entries.length - 1] : null;
    const isLastEntryReset = lastEntry?.resetPoint === true;
    const resetType = lastEntry?.resetType || null;
    
    console.log(`ExtendResetStrategy: Start calculation for next bet`);
    console.log(`ExtendResetStrategy: Last entry resetPoint = ${isLastEntryReset}, resetType = ${resetType || 'none'}`);
    
    if (params.isGameReset) {
      console.log('ExtendResetStrategy: Game was reset: Using base bet');
      return params.baseBet;
    }
    
    let lastResetColumn = null;
    let lastResetType = null;
    
    if (params.entries.length > 0) {
      // Find the most recent entry that is marked as a reset
      for (let i = params.entries.length - 1; i >= 0; i--) {
        if (params.entries[i].resetPoint === true) {
          lastResetColumn = params.entries[i].columnNumber;
          lastResetType = params.entries[i].resetType;
          break;
        }
      }
    }
    
    if (isLastEntryReset) {
      // Treat all resets the same in terms of returning to base bet
      console.log(`ExtendResetStrategy: ${resetType}-based reset occurred, returning base bet: ${params.baseBet}`);
      return params.baseBet;
    }
    
    console.log(`ExtendResetStrategy: Applying Extend Reset calculation formula`);
    console.log(`ExtendResetStrategy: Last reset column = ${lastResetColumn}, reset type = ${lastResetType || 'none'}`);
    
    return calculateExtendResetNextBet(
      params.entries,
      params.isGameReset,
      params.baseBet,
      params.raiseBet,
      params.rowCount,
      params.extensionFactor,
      isLastEntryReset,
      resetType,
      lastResetColumn
    );
  },
  determineResult: (handValue, columnNumber, rowNumber, entries) =>
    determineExtendResetResult(handValue, columnNumber, rowNumber, entries),
};

const extendPatternChangeStrategy: Strategy = {
  calculateBet: (params) => {
    // Log calculation details to help with debugging
    console.log('ExtendPatternChangeStrategy: Start calculation for next bet');
    
    if (params.isGameReset) {
      console.log('ExtendPatternChangeStrategy: Game was reset: Using base bet');
      return params.baseBet;
    }
    
    return calculateExtendPatternChangeNextBet(
      params.entries,
      params.isGameReset,
      params.baseBet,
      params.raiseBet,
      params.rowCount,
      params.extensionFactor
    );
  },
  determineResult: (handValue, columnNumber, rowNumber, entries) =>
    determineExtendPatternChangeResult(handValue, columnNumber, rowNumber, entries),
};

export const createPredictionStrategy = (predictionLogic: PredictionLogicType): Strategy => {
  switch (predictionLogic) {
    case 'same':
      return sameStrategy;
    case 'win-loss':
      return winLossStrategy;
    case 'win-loss-prev-rows':
      return winLossPrevRowsStrategy;
    case 'same-no-recovery':
      return sameNoRecoveryStrategy;
    case 'extend':
      return extendStrategy;
    case 'extend-reset':
      return extendResetStrategy;
    case 'extend-pattern-change':
      return extendPatternChangeStrategy;
    default:
      return sameStrategy;
  }
};
