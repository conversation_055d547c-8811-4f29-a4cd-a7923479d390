import { GameEntry } from '@/types/game';

// Calculate next bet amount for Same No Recovery logic
export const calculateSameNoRecoveryNextBet = (
  entries: GameEntry[],
  isGameReset: boolean,
  baseBet: number,
  raiseBet: number,
  rowCount: number
): number => {
  console.log(`Same-No-Recovery calculation with ${entries.length} entries`);
  
  if (isGameReset || entries.length === 0) {
    return baseBet;
  }

  // Calculate next position
  let nextColumn = 1;
  let nextRow = 1;
  
  if (entries.length > 0) {
    const lastEntry = entries[entries.length - 1];
    nextColumn = lastEntry.columnNumber;
    nextRow = lastEntry.rowNumber + 1;
    
    if (nextRow > rowCount) {
      nextColumn++;
      nextRow = 1;
    }
  }
  
  console.log(`Calculate next bet for Column ${nextColumn}, Row ${nextRow}`);
  
  // Column 1, Row 1 is base bet
  if (nextColumn === 1 && nextRow === 1) {
    console.log(`Column 1, Row 1: Using base bet: ${baseBet}`);
    return baseBet;
  }
  
  // For Column 1 (not Row 1)
  if (nextColumn === 1 && nextRow > 1) {
    // Check if the previous row in the same column was a WIN
    const prevRowSameColEntry = entries.find(entry => 
      entry.columnNumber === nextColumn && 
      entry.rowNumber === nextRow - 1
    );
    
    if (prevRowSameColEntry && prevRowSameColEntry.result === 'W') {
      console.log(`Previous row in same column was a WIN, using base bet: ${baseBet}`);
      return baseBet;
    }
    
    // Get previous loss in the same column
    const prevLossInSameCol = getPreviousLossInColumn(entries, nextColumn, nextRow);
    
    if (prevLossInSameCol > 0) {
      const betAmount = prevLossInSameCol + raiseBet;
      console.log(`Column 1, Row ${nextRow}: Using PLSC + RBA: ${betAmount} (${prevLossInSameCol} + ${raiseBet})`);
      return betAmount;
    }
    
    return baseBet;
  }
  
  // For Row 1 (observation row, no bet)
  if (nextRow === 1 && nextColumn > 1) {
    return 0; // Row 1 is always observation row with no bet
  }
  
  // For Row 2, Column 2+
  if (nextRow === 2 && nextColumn > 1) {
    console.log(`Row 2 calculation for Column ${nextColumn}`);
    
    // Start with RBA
    let result = raiseBet;
    console.log(`Starting with RBA: ${result}`);
    
    // Add Previous Column's Last Row Loss + RBA
    const prevColLastRowLoss = getPreviousColumnLastRowLoss(entries, nextColumn);
    if (prevColLastRowLoss > 0) {
      const lossWithRaise = prevColLastRowLoss + raiseBet;
      result += lossWithRaise;
      console.log(`Adding (PCLRL + RBA): ${lossWithRaise} (${prevColLastRowLoss} + ${raiseBet})`);
    } else {
      console.log(`Previous column's last row was not a loss, not adding PCLRL+RBA`);
    }
    
    // Add Previous Column's Row 2 Loss + RBA
    const prevColSameRowLoss = getSameRowPreviousColumnLoss(entries, nextColumn, nextRow);
    if (prevColSameRowLoss > 0) {
      const withRBA = prevColSameRowLoss + raiseBet;
      result += withRBA;
      console.log(`Adding (Previous Column Same Row Loss + RBA): ${withRBA} (${prevColSameRowLoss} + ${raiseBet})`);
    } else {
      console.log(`Same row in previous column was not a loss, not adding SRPC+RBA`);
    }
    
    console.log(`Final Row 2 bet amount: ${result}`);
    return result;
  }
  
  // For Rows 3+, Column 2+
  if (nextRow >= 3 && nextColumn > 1) {
    console.log(`Rows 3+ calculation for Column ${nextColumn}, Row ${nextRow}`);
    
    // Start with RBA
    let result = raiseBet;
    console.log(`Starting with RBA: ${result}`);
    
    // Add Same Row Previous Column Loss + RBA
    const sameRowPrevColLoss = getSameRowPreviousColumnLoss(entries, nextColumn, nextRow);
    if (sameRowPrevColLoss > 0) {
      const srpcAmount = sameRowPrevColLoss + raiseBet;
      result += srpcAmount;
      console.log(`Adding (SRPC + RBA): ${srpcAmount} (${sameRowPrevColLoss} + ${raiseBet})`);
    } else {
      console.log(`Same row in previous column was not a loss, not adding SRPC+RBA`);
    }
    
    // CRITICAL FIX: We should check if the previous row was a win AFTER calculating the formula
    // but for Rows 3+, Column 2+, we now keep the calculated amount even if previous row was a win
    
    // Add Previous Loss in Same Column + RBA
    const prevLossInSameCol = getPreviousLossInColumn(entries, nextColumn, nextRow);
    if (prevLossInSameCol > 0) {
      const plscAmount = prevLossInSameCol + raiseBet;
      result += plscAmount;
      console.log(`Adding (PLSC + RBA): ${plscAmount} (${prevLossInSameCol} + ${raiseBet})`);
    } else {
      console.log(`No previous loss in same column, not adding PLSC+RBA`);
    }
    
    // Let's log if there was a win in the previous row, but we're NOT using base bet anymore
    const prevRowSameColEntry = entries.find(entry => 
      entry.columnNumber === nextColumn && 
      entry.rowNumber === nextRow - 1
    );
    
    if (prevRowSameColEntry && prevRowSameColEntry.result === 'W') {
      console.log(`Previous row in same column was a WIN, but for Rows 3+ we're keeping calculated amount: ${result}`);
    }
    
    console.log(`Final Row ${nextRow} bet amount: ${result}`);
    return result;
  }
  
  return baseBet;
};

// Function to get previous column's Row 1 loss
export const getPreviousColumnRow1Loss = (
  columnNumber: number,
  entries: GameEntry[]
): number => {
  if (columnNumber <= 1) {
    return 0;
  }

  const prevColRow1Entry = entries.find(
    entry => entry.columnNumber === columnNumber - 1 && 
            entry.rowNumber === 1 && 
            entry.result === 'L'
  );
  
  if (prevColRow1Entry) {
    console.log(`Found previous column row 1 loss: column ${columnNumber-1}, row 1, amount ${prevColRow1Entry.betAmount}`);
    return prevColRow1Entry.betAmount;
  }
  
  console.log(`Previous column row 1 (col ${columnNumber-1}) was not a loss`);
  return 0;
};

// Get the loss from the same row in the previous column
export const getSameRowPreviousColumnLoss = (
  entries: GameEntry[],
  columnNumber: number,
  rowNumber: number
): number => {
  if (columnNumber <= 1) {
    return 0;
  }

  const entry = entries.find(
    entry => entry.columnNumber === columnNumber - 1 && 
            entry.rowNumber === rowNumber &&
            entry.result === 'L'
  );

  if (entry) {
    // Ensure betAmount is a number, not a Promise
    const betAmount = typeof entry.betAmount === 'number' ? entry.betAmount : 0;
    console.log(`Found same row previous column loss: column ${columnNumber-1}, row ${rowNumber}, amount ${betAmount}`);
    return betAmount;
  }
  
  console.log(`Same row in previous column was not a loss or not found`);
  return 0;
};

// Get previous loss in the same column
export const getPreviousLossInColumn = (
  entries: GameEntry[],
  columnNumber: number,
  rowNumber: number
): number => {
  if (rowNumber <= 1) {
    return 0;
  }
  
  // For rows >= 3, we should only use the directly previous row's loss
  // This fixes the issue where we were using any previous loss in the column
  if (rowNumber >= 3) {
    const previousRowEntry = entries.find(entry => 
      entry.columnNumber === columnNumber && 
      entry.rowNumber === rowNumber - 1 &&
      entry.result === 'L'
    );
    
    if (previousRowEntry) {
      // Ensure betAmount is a number, not a Promise
      const betAmount = typeof previousRowEntry.betAmount === 'number' ? previousRowEntry.betAmount : 0;
      console.log(`Found previous row loss in column ${columnNumber}, row ${previousRowEntry.rowNumber} with bet amount ${betAmount}`);
      return betAmount;
    }
    
    console.log(`Previous row in column ${columnNumber} was not a loss`);
    return 0;
  }
  
  // Get all entries in the current column with row numbers less than the current row
  const previousEntriesInColumn = entries.filter(entry => 
    entry.columnNumber === columnNumber && 
    entry.rowNumber < rowNumber
  );
  
  // Sort by row number descending to get the most recent entries first
  previousEntriesInColumn.sort((a, b) => b.rowNumber - a.rowNumber);
  
  // Find the first loss (the most recent one)
  const previousLoss = previousEntriesInColumn.find(entry => entry.result === 'L');
  
  if (previousLoss) {
    // Ensure betAmount is a number, not a Promise
    const betAmount = typeof previousLoss.betAmount === 'number' ? previousLoss.betAmount : 0;
    console.log(`Found previous loss in column ${columnNumber}, row ${previousLoss.rowNumber} with bet amount ${betAmount}`);
    return betAmount;
  }
  
  console.log(`No previous loss found in column ${columnNumber}`);
  return 0;
};

// Get the last row's loss from the previous column
export const getPreviousColumnLastRowLoss = (
  entries: GameEntry[],
  columnNumber: number
): number => {
  if (columnNumber <= 1) {
    return 0;
  }

  // Get all entries from the previous column
  const previousColumnEntries = entries.filter(
    entry => entry.columnNumber === columnNumber - 1
  );

  if (previousColumnEntries.length === 0) {
    return 0;
  }

  // Find the entry with the highest row number (last row) in the previous column
  let highestRowNumber = 0;
  let lastEntry: GameEntry | undefined;
  
  previousColumnEntries.forEach(entry => {
    if (entry.rowNumber > highestRowNumber) {
      highestRowNumber = entry.rowNumber;
      lastEntry = entry;
    }
  });
  
  if (lastEntry && lastEntry.result === 'L') {
    // Ensure betAmount is a number, not a Promise
    const betAmount = typeof lastEntry.betAmount === 'number' ? lastEntry.betAmount : 0;
    console.log(`Found previous column's last row loss: column ${columnNumber-1}, row ${lastEntry.rowNumber}, amount ${betAmount}`);
    return betAmount;
  } else {
    console.log(`Previous column's last row (col ${columnNumber-1}, row ${highestRowNumber}) was not a loss`);
  }
  
  return 0;
};

// Function to determine result for Same No Recovery logic
export const determineSameNoRecoveryResult = (
  handValue: 'P' | 'B',
  columnNumber: number,
  rowNumber: number,
  entries: GameEntry[]
): 'W' | 'L' | 'N' => {
  if (rowNumber === 1) {
    return 'N'; // Row 1 is observation row
  }

  // Find the first row entry in the current column
  const firstRowEntry = entries.find(entry => entry.columnNumber === columnNumber && entry.rowNumber === 1);
  
  if (!firstRowEntry) {
    console.log(`First row entry not found for column ${columnNumber}`);
    return 'N';
  }
  
  console.log(`Same-No-Recovery result: ${handValue === firstRowEntry.handValue ? 'W' : 'L'} (Current hand: ${handValue}, First row hand: ${firstRowEntry.handValue})`);
  return handValue === firstRowEntry.handValue ? 'W' : 'L';
};
