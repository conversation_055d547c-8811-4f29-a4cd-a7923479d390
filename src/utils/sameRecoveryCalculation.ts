import { GameEntry } from '@/types/game';

// Calculate next bet amount for Same Recovery logic
export const calculateSameRecoveryNextBet = (
  entries: GameEntry[],
  isGameReset: boolean,
  baseBet: number,
  raiseBet: number,
  rowCount: number
): number => {
  console.log(`Same-Recovery calculation with ${entries.length} entries`);
  
  if (isGameReset || entries.length === 0) {
    return baseBet;
  }

  // Check if we have two consecutive wins (excluding No Bet wins)
  // This check should happen first, before any other logic
  const consecutiveWinsInfo = checkConsecutiveWins(entries);
  if (consecutiveWinsInfo.hasConsecutiveWins) {
    console.log(`Found ${consecutiveWinsInfo.count} consecutive wins, resetting to base bet`);
    return baseBet; // Reset to base bet regardless of other conditions
  }

  // Check if the most recent entry is any type of win
  const lastEntry = entries.length > 0 ? entries[entries.length - 1] : null;
  if (lastEntry && lastEntry.result === 'W') {
    // If it's a seed win (No Bet win), handle seed recovery
    if (lastEntry.betAmount === 0) {
      console.log('Last entry is a No Bet win (seed win), entering seed recovery mode');
      const seedRecoveryInfo = checkSeedRecovery(entries, baseBet);
      if (seedRecoveryInfo.inSeedRecovery) {
        console.log(`In seed recovery mode. Total losses: ${seedRecoveryInfo.totalLossAmount}, Count: ${seedRecoveryInfo.lossCount}`);
        return calculateSeedRecoveryBet(entries, baseBet, raiseBet, seedRecoveryInfo);
      }
    } else {
      // Regular win - check if we're still in seed recovery mode from a previous seed win
      const seedRecoveryInfo = checkSeedRecovery(entries, baseBet);
      if (seedRecoveryInfo.inSeedRecovery) {
        console.log(`Still in seed recovery mode after a regular win. Total losses: ${seedRecoveryInfo.totalLossAmount}`);
        return calculateSeedRecoveryBet(entries, baseBet, raiseBet, seedRecoveryInfo);
      }
      // Otherwise, continue with normal calculation
    }
  } else {
    // Only check for consecutive losses if the last entry is not a win
    const consecutiveLossesInfo = checkConsecutiveLosses(entries);
    if (consecutiveLossesInfo.hasConsecutiveLosses) {
      console.log(`Found ${consecutiveLossesInfo.count} consecutive losses, returning No Bet`);
      return 0; // No Bet
    }
    
    // Check if the previous entry was a No Bet loss
    const previousNoBetLossInfo = checkPreviousNoBetLoss(entries);
    if (previousNoBetLossInfo.isPreviousNoBetLoss) {
      console.log(`Previous entry was a No Bet loss, continuing with No Bet`);
      return 0; // Continue No Bet
    }
  }
  
  // Check if we're in seed recovery mode (regardless of last entry type)
  const seedRecoveryInfo = checkSeedRecovery(entries, baseBet);
  if (seedRecoveryInfo.inSeedRecovery) {
    console.log(`In seed recovery mode. Total losses: ${seedRecoveryInfo.totalLossAmount}, Count: ${seedRecoveryInfo.lossCount}`);
    return calculateSeedRecoveryBet(entries, baseBet, raiseBet, seedRecoveryInfo);
  }

  // Check if we're in regular recovery mode
  const recoveryMode = isInRecoveryMode(entries);
  if (recoveryMode.active) {
    console.log(`In recovery mode. Column: ${recoveryMode.column}, Losses: ${recoveryMode.lossAmount}`);
    return calculateRecoveryBet(entries, baseBet, raiseBet, recoveryMode);
  }

  // Calculate next position
  let nextColumn = 1;
  let nextRow = 1;
  
  if (entries.length > 0) {
    const lastEntry = entries[entries.length - 1];
    nextColumn = lastEntry.columnNumber;
    nextRow = lastEntry.rowNumber + 1;
    
    if (nextRow > rowCount) {
      nextColumn++;
      nextRow = 1;
    }
  }
  
  console.log(`Calculate next bet for Column ${nextColumn}, Row ${nextRow}`);
  
  // Column 1, Row 1 is base bet
  if (nextColumn === 1 && nextRow === 1) {
    console.log(`Column 1, Row 1: Using base bet: ${baseBet}`);
    return baseBet;
  }
  
  // For Column 1 (not Row 1)
  if (nextColumn === 1 && nextRow > 1) {
    // Check if the previous row in the same column was a WIN
    const prevRowSameColEntry = entries.find(entry =>
      entry.columnNumber === nextColumn &&
      entry.rowNumber === nextRow - 1
    );

    if (prevRowSameColEntry && prevRowSameColEntry.result === 'W') {
      console.log(`Previous row in same column was a WIN, using base bet: ${baseBet}`);
      return baseBet;
    }

    // Get previous loss in the same column
    const prevLossInSameCol = getPreviousLossInColumn(entries, nextColumn, nextRow);

    if (prevLossInSameCol > 0) {
      // For Same Recovery, use (PLSC + RBA) + RBA instead of just PLSC + RBA
      const betAmount = (prevLossInSameCol + raiseBet) + raiseBet;
      console.log(`Column 1, Row ${nextRow}: Using (PLSC + RBA) + RBA: ${betAmount} ((${prevLossInSameCol} + ${raiseBet}) + ${raiseBet})`);
      return betAmount;
    }

    return baseBet;
  }
  
  // For Row 1 (observation row, no bet)
  if (nextRow === 1 && nextColumn > 1) {
    console.log(`Column ${nextColumn}, Row 1: Observation row (no bet)`);
    return 0; // Row 1 is always observation row with no bet
  }
  
  // For Row 2, Column 2+
  if (nextRow === 2 && nextColumn > 1) {
    console.log(`Row 2 calculation for Column ${nextColumn}`);
    
    // Initialize result to 0 instead of starting with RBA
    let result = 0;
    
    // Check if all entries in the previous column are losses
    const allLossesInPrevColumn = areAllEntriesInColumnLosses(entries, nextColumn - 1);
    
    // Check if there are any wins in the current column
    const hasWinInCurrentColumn = hasAnyWinInColumn(entries, nextColumn);
    
    // If all entries in previous column are losses and there are no wins yet in current column,
    // only consider SRPC+RBA
    const skipPCLRL = allLossesInPrevColumn && !hasWinInCurrentColumn;
    
    // Collect all loss components with single RBA each, then add one final RBA
    let componentCount = 0;

    // Add Previous Column's Last Row Loss + RBA (only if not skipping PCLRL)
    if (!skipPCLRL) {
      const prevColLastRowLoss = getPreviousColumnLastRowLoss(entries, nextColumn);
      if (prevColLastRowLoss > 0) {
        const lossWithRaise = prevColLastRowLoss + raiseBet;
        result += lossWithRaise;
        componentCount++;
        console.log(`Adding (PCLRL + RBA): ${lossWithRaise} (${prevColLastRowLoss} + ${raiseBet})`);
      } else {
        console.log(`Previous column's last row was not a loss, not adding PCLRL+RBA`);
      }
    } else {
      console.log(`All entries in previous column are losses and no wins in current column, skipping PCLRL+RBA`);
    }

    // Add Previous Column's Row 2 Loss + RBA (always consider this)
    const prevColSameRowLoss = getSameRowPreviousColumnLoss(entries, nextColumn, nextRow);
    if (prevColSameRowLoss > 0) {
      const withRBA = prevColSameRowLoss + raiseBet;
      result += withRBA;
      componentCount++;
      console.log(`Adding (Previous Column Same Row Loss + RBA): ${withRBA} (${prevColSameRowLoss} + ${raiseBet})`);
    } else {
      console.log(`Same row in previous column was not a loss, not adding SRPC+RBA`);
    }

    // Add Previous Column's Row 1 Loss + RBA (only if not skipping PCLRL)
    if (!skipPCLRL) {
      const prevColRow1Loss = getPreviousColumnRow1Loss(nextColumn, entries);
      if (prevColRow1Loss > 0) {
        const withRBA = prevColRow1Loss + raiseBet;
        result += withRBA;
        componentCount++;
        console.log(`Adding (Previous Column Row 1 Loss + RBA): ${withRBA} (${prevColRow1Loss} + ${raiseBet})`);
      } else {
        console.log(`Previous column's row 1 was not a loss, not adding PCR1L+RBA`);
      }
    } else {
      console.log(`All entries in previous column are losses and no wins in current column, skipping PCR1L+RBA`);
    }

    // Add one final RBA if we have any components
    if (componentCount > 0) {
      result += raiseBet;
      console.log(`Adding final RBA: ${raiseBet}. Total components: ${componentCount}, Final result: ${result}`);
    }
    
    // If no components were added, use the base bet
    if (result === 0) {
      result = baseBet;
      console.log(`No components added, using base bet: ${baseBet}`);
    }
    
    console.log(`Final Row 2 bet amount: ${result}`);
    return result;
  }
  
  // For Rows 3+, Column 2+
  if (nextRow >= 3 && nextColumn > 1) {
    console.log(`Rows 3+ calculation for Column ${nextColumn}, Row ${nextRow}`);
    
    // Initialize result to 0 instead of starting with RBA
    let result = 0;
    
    // Check if all entries in the previous column are losses
    const allLossesInPrevColumn = areAllEntriesInColumnLosses(entries, nextColumn - 1);
    
    // Check if there are any wins in the current column
    const hasWinInCurrentColumn = hasAnyWinInColumn(entries, nextColumn);
    
    // If all entries in previous column are losses and there are no wins yet in current column,
    // only consider SRPC+RBA
    const skipPCLRL = allLossesInPrevColumn && !hasWinInCurrentColumn;
    
    // Collect all loss components with single RBA each, then add one final RBA
    let componentCount = 0;

    // Add Same Row Previous Column Loss + RBA (always consider this)
    const sameRowPrevColLoss = getSameRowPreviousColumnLoss(entries, nextColumn, nextRow);
    if (sameRowPrevColLoss > 0) {
      const srpcAmount = sameRowPrevColLoss + raiseBet;
      result += srpcAmount;
      componentCount++;
      console.log(`Adding (SRPC + RBA): ${srpcAmount} (${sameRowPrevColLoss} + ${raiseBet})`);
    } else {
      console.log(`Same row in previous column was not a loss, not adding SRPC+RBA`);
    }

    // Check if the previous row in the same column was a WIN
    const prevRowSameColEntry = entries.find(entry =>
      entry.columnNumber === nextColumn &&
      entry.rowNumber === nextRow - 1
    );

    if (prevRowSameColEntry && prevRowSameColEntry.result === 'W') {
      // For rows 3+, we keep the calculated amount even after a win in the previous row
      // This matches the behavior of Same (No Recovery)
      console.log(`Previous row in same column was a WIN, but for Rows 3+ we're keeping calculated amount: ${result}`);
    } else {
      // Add Previous Loss in Same Column + RBA (only if not in skip mode or if there's a win in current column)
      if (!skipPCLRL || hasWinInCurrentColumn) {
        const prevLossInSameCol = getPreviousLossInColumn(entries, nextColumn, nextRow);
        if (prevLossInSameCol > 0) {
          const plscAmount = prevLossInSameCol + raiseBet;
          result += plscAmount;
          componentCount++;
          console.log(`Adding (PLSC + RBA): ${plscAmount} (${prevLossInSameCol} + ${raiseBet})`);
        } else {
          console.log(`No previous loss in same column, not adding PLSC+RBA`);
        }
      } else {
        console.log(`All entries in previous column are losses and no wins in current column, skipping PLSC+RBA`);
      }
    }

    // Add one final RBA if we have any components
    if (componentCount > 0) {
      result += raiseBet;
      console.log(`Adding final RBA: ${raiseBet}. Total components: ${componentCount}, Final result: ${result}`);
    }
    
    // If no components were added, use the base bet
    if (result === 0) {
      result = baseBet;
      console.log(`No components added, using base bet: ${baseBet}`);
    }
    
    console.log(`Final Row ${nextRow} bet amount: ${result}`);
    return result;
  }
  
  // Fallback to base bet
  console.log(`No specific calculation rule matched, using base bet: ${baseBet}`);
  return baseBet;
};

// Function to determine if we should trigger recovery mode
export const shouldTriggerRecoveryMode = (
  entries: GameEntry[],
  columnNumber: number,
  rowNumber: number
): boolean => {
  // Trigger recovery mode if we have 3 consecutive losses in the same column
  const lossesInColumn = entries.filter(entry => 
    entry.columnNumber === columnNumber && 
    entry.rowNumber < rowNumber && 
    entry.result === 'L'
  );
  
  // If we have 3 or more consecutive losses in the same column
  if (lossesInColumn.length >= 3) {
    // Check if they are consecutive
    lossesInColumn.sort((a, b) => a.rowNumber - b.rowNumber);
    
    let consecutiveLosses = 1;
    for (let i = 1; i < lossesInColumn.length; i++) {
      if (lossesInColumn[i].rowNumber === lossesInColumn[i-1].rowNumber + 1) {
        consecutiveLosses++;
      } else {
        consecutiveLosses = 1;
      }
      
      if (consecutiveLosses >= 3) {
        return true;
      }
    }
  }
  
  return false;
};

// Function to check if we're in recovery mode
export const isInRecoveryMode = (
  entries: GameEntry[]
): { active: boolean; column: number; lossAmount: number; startIndex: number } => {
  if (entries.length === 0) {
    return { active: false, column: 0, lossAmount: 0, startIndex: -1 };
  }
  
  // Find the most recent recovery mode start
  let recoveryStartIndex = -1;
  let recoveryEndIndex = -1;
  
  for (let i = entries.length - 1; i >= 0; i--) {
    if (entries[i].recoveryMode === 'start') {
      recoveryStartIndex = i;
      break;
    }
    if (entries[i].recoveryMode === 'end') {
      recoveryEndIndex = i;
      break;
    }
  }
  
  // If we found a recovery end more recently than a start, we're not in recovery mode
  if (recoveryEndIndex > recoveryStartIndex) {
    return { active: false, column: 0, lossAmount: 0, startIndex: -1 };
  }
  
  // If we found a recovery start, we're in recovery mode
  if (recoveryStartIndex >= 0) {
    // Calculate total loss amount that triggered recovery
    const recoveryStartEntry = entries[recoveryStartIndex];
    const columnNumber = recoveryStartEntry.columnNumber;
    
    // Get all losses in the column before the recovery start
    const lossesBeforeRecovery = entries.filter(entry => 
      entry.columnNumber === columnNumber && 
      entry.rowNumber < recoveryStartEntry.rowNumber && 
      entry.result === 'L'
    );
    
    const totalLossAmount = lossesBeforeRecovery.reduce((total, entry) => 
      total + (typeof entry.betAmount === 'number' ? entry.betAmount : 0), 0);
    
    return { 
      active: true, 
      column: columnNumber, 
      lossAmount: totalLossAmount,
      startIndex: recoveryStartIndex
    };
  }
  
  return { active: false, column: 0, lossAmount: 0, startIndex: -1 };
};

// Calculate bet amount when in recovery mode
export const calculateRecoveryBet = (
  entries: GameEntry[],
  baseBet: number,
  raiseBet: number,
  recoveryInfo: { active: boolean; column: number; lossAmount: number; startIndex: number }
): number => {
  if (!recoveryInfo.active || recoveryInfo.startIndex < 0) {
    return baseBet;
  }
  
  // Count consecutive wins in recovery mode
  let consecutiveWins = 0;
  for (let i = recoveryInfo.startIndex + 1; i < entries.length; i++) {
    if (entries[i].result === 'W') {
      consecutiveWins++;
    } else if (entries[i].result === 'L') {
      consecutiveWins = 0;
    }
  }
  
  // If we have 2 consecutive wins, we should exit recovery mode
  // This will be handled when the entry is created
  
  // Calculate recovery bet amount
  // We want to recover the loss amount over 3 bets
  const recoveryBetBase = Math.ceil(recoveryInfo.lossAmount / 3);
  
  // Increase bet amount for each consecutive win
  const recoveryBet = recoveryBetBase + (consecutiveWins * raiseBet);
  
  console.log(`Recovery bet calculation: ${recoveryBetBase} + (${consecutiveWins} * ${raiseBet}) = ${recoveryBet}`);
  
  return recoveryBet;
};

// Function to check for consecutive losses
export const checkConsecutiveLosses = (
  entries: GameEntry[]
): { hasConsecutiveLosses: boolean; count: number } => {
  if (entries.length < 2) {
    return { hasConsecutiveLosses: false, count: 0 };
  }

  // Check if the most recent entry is a win (including No Bet win)
  // If so, we should not trigger the consecutive losses rule
  const lastEntry = entries[entries.length - 1];
  if (lastEntry.result === 'W') {
    console.log('Last entry is a win, skipping consecutive losses check');
    return { hasConsecutiveLosses: false, count: 0 };
  }

  // Get the last two non-win entries that are not No Bet cells
  // We need to find the last two actual bet entries that resulted in losses
  const relevantEntries: GameEntry[] = [];
  for (let i = entries.length - 1; i >= 0 && relevantEntries.length < 2; i--) {
    const entry = entries[i];
    // Only consider entries with actual bets (not No Bet) and that resulted in a loss
    if (entry.betAmount > 0 && entry.result === 'L') {
      relevantEntries.unshift(entry);
    }
    // If we encounter a win, break the sequence
    if (entry.result === 'W') {
      break;
    }
  }

  // Check if we have two consecutive losses
  if (relevantEntries.length === 2) {
    console.log('Found 2 consecutive losses in relevant entries');
    return { hasConsecutiveLosses: true, count: 2 };
  }

  return { hasConsecutiveLosses: false, count: 0 };
};

// Function to check if the previous entry was a No Bet loss
export const checkPreviousNoBetLoss = (
  entries: GameEntry[]
): { isPreviousNoBetLoss: boolean; entry: GameEntry | null } => {
  if (entries.length === 0) {
    return { isPreviousNoBetLoss: false, entry: null };
  }

  const lastEntry = entries[entries.length - 1];
  
  // Check if the last entry was a No Bet loss
  if (lastEntry.betAmount === 0 && lastEntry.result === 'L') {
    return { isPreviousNoBetLoss: true, entry: lastEntry };
  }

  return { isPreviousNoBetLoss: false, entry: null };
};

// Function to check if we're in seed recovery mode (after a No Bet win)
export const checkSeedRecovery = (
  entries: GameEntry[],
  baseBet: number
): { inSeedRecovery: boolean; totalLossAmount: number; lossCount: number; seedWinIndex: number; applySrpcLogic: boolean } => {
  if (entries.length === 0) {
    return { inSeedRecovery: false, totalLossAmount: 0, lossCount: 0, seedWinIndex: -1, applySrpcLogic: false };
  }

  // Find the most recent No Bet win (Seed win)
  let seedWinIndex = -1;
  for (let i = entries.length - 1; i >= 0; i--) {
    if (entries[i].betAmount === 0 && entries[i].result === 'W') {
      seedWinIndex = i;
      break;
    }
  }

  if (seedWinIndex === -1) {
    return { inSeedRecovery: false, totalLossAmount: 0, lossCount: 0, seedWinIndex: -1, applySrpcLogic: false };
  }

  // Check if there's already been a bet win after the seed win
  // If so, seed recovery mode should end
  if (seedWinIndex < entries.length - 1) {
    const entriesAfterSeed = entries.slice(seedWinIndex + 1);
    const hasBetWinAfterSeed = entriesAfterSeed.some(entry => entry.betAmount > 0 && entry.result === 'W');

    if (hasBetWinAfterSeed) {
      // Seed recovery mode ends after the first bet win following a seed win
      console.log(`Found bet win after seed win, ending seed recovery mode`);
      return { inSeedRecovery: false, totalLossAmount: 0, lossCount: 0, seedWinIndex: -1, applySrpcLogic: false };
    }

    // Check if we have any bet entries after seed (but no wins yet)
    const hasBetEntryAfterSeed = entriesAfterSeed.some(entry => entry.betAmount > 0);

    if (hasBetEntryAfterSeed) {
      // We're in a subsequent calculation after the initial seed recovery
      // Apply SRPC logic for this calculation
      console.log(`Found entries after seed win, applying SRPC logic for subsequent calculations`);
      return { inSeedRecovery: true, totalLossAmount: 0, lossCount: 0, seedWinIndex, applySrpcLogic: true };
    }
  }

  // Find the last win (any win, including seed/No Bet wins) before the current seed win
  let lastWinIndex = -1;
  for (let i = seedWinIndex - 1; i >= 0; i--) {
    if (entries[i].result === 'W') {
      lastWinIndex = i;
      break;
    }
  }

  // If no win found, start from the beginning
  const startIndex = lastWinIndex >= 0 ? lastWinIndex + 1 : 0;

  console.log(`Seed recovery: counting losses from index ${startIndex} to ${seedWinIndex - 1}`);

  // Calculate total losses between the last win and the current seed win
  const lossesBeforeSeed = entries.slice(startIndex, seedWinIndex)
    .filter(entry => entry.result === 'L' && entry.betAmount > 0);

  // Log the losses being counted for transparency
  if (lossesBeforeSeed.length > 0) {
    console.log('Losses counted for seed recovery:');
    lossesBeforeSeed.forEach((loss, idx) => {
      console.log(`Loss ${idx + 1}: Column ${loss.columnNumber}, Row ${loss.rowNumber}, Amount: ${loss.betAmount}`);
    });
  }

  const totalLossAmount = lossesBeforeSeed.reduce(
    (total, entry) => total + (typeof entry.betAmount === 'number' ? entry.betAmount : 0),
    0
  );

  return {
    inSeedRecovery: true,
    totalLossAmount,
    lossCount: lossesBeforeSeed.length,
    seedWinIndex,
    applySrpcLogic: false  // For first calculation after seed win, don't apply SRPC logic
  };
};

// Function to calculate bet amount in seed recovery mode
export const calculateSeedRecoveryBet = (
  entries: GameEntry[],
  baseBet: number,
  raiseBet: number,
  seedRecoveryInfo: { inSeedRecovery: boolean; totalLossAmount: number; lossCount: number; seedWinIndex: number; applySrpcLogic: boolean }
): number => {
  if (!seedRecoveryInfo.inSeedRecovery || seedRecoveryInfo.seedWinIndex < 0) {
    return baseBet;
  }

  // Calculate recovery bet amount using Total Losses + (Number of total losses × BBA) + RBA
  const recoveryBet = seedRecoveryInfo.totalLossAmount + (seedRecoveryInfo.lossCount * baseBet) + raiseBet;

  console.log(`Seed recovery bet calculation: ${seedRecoveryInfo.totalLossAmount} + (${seedRecoveryInfo.lossCount} * ${baseBet}) + ${raiseBet} = ${recoveryBet}`);
  
  // Calculate next position
  let nextColumn = 1;
  let nextRow = 1;
  
  if (entries.length > 0) {
    const lastEntry = entries[entries.length - 1];
    nextColumn = lastEntry.columnNumber;
    nextRow = lastEntry.rowNumber + 1;
    
    if (nextRow > 3) { // Assuming rowCount is 3
      nextColumn++;
      nextRow = 1;
    }
  }
  
  // SPECIAL CASE: After two consecutive wins (one No Bet win followed by a regular win),
  // use the highest of the last two losses approach
  
  // Check if we have a No Bet win followed by a regular win
  // We need to look at the most recent entries to see if we have this pattern
  const relevantEntries = entries.filter(entry => entry.result !== 'N'); // Exclude 'N' entries
  const recentWins = relevantEntries.filter(entry => entry.result === 'W').slice(-2);
  
  console.log('Checking for special case: No Bet win followed by regular win');
  if (recentWins.length >= 2) {
    console.log(`Recent win 1: Column ${recentWins[recentWins.length-2].columnNumber}, Row ${recentWins[recentWins.length-2].rowNumber}, Amount: ${recentWins[recentWins.length-2].betAmount}`);
    console.log(`Recent win 2: Column ${recentWins[recentWins.length-1].columnNumber}, Row ${recentWins[recentWins.length-1].rowNumber}, Amount: ${recentWins[recentWins.length-1].betAmount}`);
  }
  
  const hasNoBetWinFollowedByRegularWin = 
    recentWins.length >= 2 &&
    recentWins[recentWins.length-2].betAmount === 0 && // No Bet win
    recentWins[recentWins.length-1].betAmount > 0;     // Regular win
  
  if (hasNoBetWinFollowedByRegularWin) {
    console.log('SPECIAL CASE: No Bet win followed by regular win - Using highest loss approach');
    
    // Find all losses with bet amounts > 0
    const allLosses = entries
      .filter(entry => entry.result === 'L' && entry.betAmount > 0);
    
    // Get the last two losses
    const lastTwoLosses = allLosses.slice(-2);
    
    console.log(`Found ${lastTwoLosses.length} losses for highest loss calculation.`);
    
    if (lastTwoLosses.length >= 2) {
      // Log the losses for debugging
      console.log(`Loss 1: Column ${lastTwoLosses[0].columnNumber}, Row ${lastTwoLosses[0].rowNumber}, Amount: ${lastTwoLosses[0].betAmount}`);
      console.log(`Loss 2: Column ${lastTwoLosses[1].columnNumber}, Row ${lastTwoLosses[1].rowNumber}, Amount: ${lastTwoLosses[1].betAmount}`);
      
      // Get the highest of the last two losses
      const highestLoss = Math.max(
        lastTwoLosses[0].betAmount,
        lastTwoLosses[1].betAmount
      );
      
      const nextBet = (highestLoss + raiseBet) + raiseBet;
      console.log(`USING HIGHEST LOSS APPROACH: (${highestLoss} + ${raiseBet}) + ${raiseBet} = ${nextBet}`);
      return nextBet;
    }
    
    // If we don't have enough losses, continue with normal calculation
    console.log('Not enough losses found for highest loss approach, continuing with normal calculation');
  }
  
  // If we're in a subsequent calculation after the initial seed recovery,
  // apply the corrected SRPC logic with highest loss before seed
  if (seedRecoveryInfo.applySrpcLogic) {
    console.log(`Applying corrected SRPC logic for subsequent calculation after seed recovery`);

    // Start with 0 for the calculation
    let result = 0;
    let componentCount = 0;
    console.log(`Starting with 0 for corrected SRPC calculation`);

    // For Row 2, Column 2+
    if (nextRow === 2 && nextColumn > 1) {
      // Formula: (PCLRL + RBA) + (Highest loss before seed + RBA) + RBA

      let pclrlComponent = 0;
      let highestLossComponent = 0;

      // Component 1: PCLRL + RBA
      const prevColLastRowLoss = getPreviousColumnLastRowLoss(entries, nextColumn);
      if (prevColLastRowLoss > 0) {
        pclrlComponent = prevColLastRowLoss + raiseBet;
        console.log(`PCLRL + RBA: ${pclrlComponent} (${prevColLastRowLoss} + ${raiseBet})`);
      }

      // Component 2: Highest loss from seed recovery period + RBA
      // Use the losses that were counted for seed recovery (from the seed recovery calculation)
      const seedRecoveryLosses = [];

      // Find the seed win entry to get the proper range
      let seedWinEntryIndex = -1;
      for (let i = entries.length - 1; i >= 0; i--) {
        if (entries[i].betAmount === 0 && entries[i].result === 'W') {
          seedWinEntryIndex = i;
          break;
        }
      }

      if (seedWinEntryIndex >= 0) {
        // Get losses from the same range used in seed recovery calculation
        for (let i = seedWinEntryIndex - 1; i >= 0; i--) {
          if (entries[i].result === 'L' && entries[i].betAmount > 0) {
            seedRecoveryLosses.push(entries[i]);
          }
          // Stop when we find a win (end of the loss sequence)
          if (entries[i].result === 'W' && entries[i].betAmount > 0) {
            break;
          }
        }
      }

      if (seedRecoveryLosses.length > 0) {
        const highestLossFromSeedRecovery = Math.max(...seedRecoveryLosses.map(entry => entry.betAmount));
        highestLossComponent = highestLossFromSeedRecovery + raiseBet;
        console.log(`Highest loss from seed recovery period + RBA: ${highestLossComponent} (${highestLossFromSeedRecovery} + ${raiseBet})`);
        console.log(`Seed recovery losses found: ${seedRecoveryLosses.map(l => `${l.columnNumber},${l.rowNumber}:${l.betAmount}`).join(', ')}`);
      }

      // Final calculation: (PCLRL + RBA) + (Highest loss before seed + RBA) + RBA
      result = pclrlComponent + highestLossComponent + raiseBet;
      console.log(`Final Row 2 calculation: (${pclrlComponent}) + (${highestLossComponent}) + ${raiseBet} = ${result}`);

      // If no components found, use base bet
      if (pclrlComponent === 0 && highestLossComponent === 0) {
        result = baseBet;
        console.log(`No PCLRL or highest loss found, using base bet: ${baseBet}`);
      }

      return result;
    }

    // For Rows 3+, Column 2+ - use the regular SRPC logic
    if (nextRow >= 3 && nextColumn > 1) {
      // Add SRPC + RBA
      const sameRowPrevColLoss = getSameRowPreviousColumnLossForRecovery(entries, nextColumn, nextRow);
      if (sameRowPrevColLoss > 0) {
        const srpcAmount = sameRowPrevColLoss + raiseBet;
        result += srpcAmount;
        componentCount++;
        console.log(`Adding (SRPC + RBA): ${srpcAmount} (${sameRowPrevColLoss} + ${raiseBet})`);
      }

      // Add PLSC + RBA
      const prevLossInSameCol = getPreviousLossInColumn(entries, nextColumn, nextRow);
      if (prevLossInSameCol > 0) {
        const plscAmount = prevLossInSameCol + raiseBet;
        result += plscAmount;
        componentCount++;
        console.log(`Adding (PLSC + RBA): ${plscAmount} (${prevLossInSameCol} + ${raiseBet})`);
      }

      // Add one final RBA if we have any components
      if (componentCount > 0) {
        result += raiseBet;
        console.log(`Adding final RBA: ${raiseBet}. Total components: ${componentCount}, Final result: ${result}`);
      }

      // If no losses found, use base bet
      if (result === 0) {
        result = baseBet;
        console.log(`No SRPC or PLSC losses found, using base bet: ${baseBet}`);
      }

      console.log(`Final Row ${nextRow} corrected SRPC calculation: ${result}`);
      return result;
    }

    return baseBet; // Default to base bet if no specific logic applies
  }
  
  // For the first calculation after a seed win, just find the first loss before no bet cell
  const srpcLoss = findFirstLossBeforeNoBet(entries, nextColumn, nextRow);
  
  if (srpcLoss > 0) {
    const srpcAmount = (srpcLoss + raiseBet) + raiseBet;
    console.log(`Adding SRPC logic: ${srpcAmount} ((${srpcLoss} + ${raiseBet}) + ${raiseBet})`);
    return Math.max(recoveryBet, srpcAmount); // Use the larger of the two amounts
  }
  
  return recoveryBet;
};

// Function to get Same Row Previous Column Loss specifically for recovery scenarios
// When looking for first loss before No Bet, find the highest loss amount
export const getSameRowPreviousColumnLossForRecovery = (
  entries: GameEntry[],
  columnNumber: number,
  rowNumber: number
): number => {
  if (columnNumber <= 1) return 0;

  // First check if previous column same row is a No Bet
  const prevColSameRow = entries.find(
    entry => entry.columnNumber === columnNumber - 1 &&
            entry.rowNumber === rowNumber &&
            entry.betAmount === 0
  );

  if (prevColSameRow) {
    console.log(`Found No Bet cell in same row of previous column, looking for highest loss before No Bet`);

    // Find all losses before the No Bet
    const lossesBefore = entries.filter(
      entry => entry.result === 'L' &&
              entry.betAmount > 0 &&
              (entry.columnNumber < columnNumber - 1 ||
               (entry.columnNumber === columnNumber - 1 && entry.rowNumber < rowNumber))
    );

    // Find the highest loss amount
    let highestLoss = 0;
    let highestLossEntry = null;

    for (const loss of lossesBefore) {
      if (loss.betAmount > highestLoss) {
        highestLoss = loss.betAmount;
        highestLossEntry = loss;
      }
    }

    if (highestLossEntry) {
      console.log(`Found highest loss before No Bet in column ${highestLossEntry.columnNumber}, row ${highestLossEntry.rowNumber} with bet amount ${highestLoss}`);
      return highestLoss;
    }
  }

  // Regular SRPC logic if no No Bet found
  return getSameRowPreviousColumnLoss(entries, columnNumber, rowNumber);
};

// Function to find the first loss before a No Bet cell
export const findFirstLossBeforeNoBet = (
  entries: GameEntry[],
  columnNumber: number,
  rowNumber: number
): number => {
  // For Row 1 (observation row, no bet)
  if (rowNumber === 1) {
    return 0;
  }

  // For Row 2, Column 2+
  if (rowNumber === 2 && columnNumber > 1) {
    // Add Previous Column's Row 2 Loss + RBA
    const prevColSameRowLoss = getSameRowPreviousColumnLoss(entries, columnNumber, rowNumber);
    if (prevColSameRowLoss > 0) {
      return prevColSameRowLoss;
    }
  }

  // For Rows 3+, Column 2+
  if (rowNumber >= 3 && columnNumber > 1) {
    // Add Same Row Previous Column Loss + RBA
    const sameRowPrevColLoss = getSameRowPreviousColumnLoss(entries, columnNumber, rowNumber);
    if (sameRowPrevColLoss > 0) {
      return sameRowPrevColLoss;
    }
  }

  return 0;
};

// Function to check for consecutive wins (excluding No Bet wins)
export const checkConsecutiveWins = (
  entries: GameEntry[]
): { hasConsecutiveWins: boolean; count: number } => {
  if (entries.length < 2) {
    return { hasConsecutiveWins: false, count: 0 };
  }

  // Get all entries with results (excluding No Bet entries)
  const relevantEntries = entries.filter(entry => 
    entry.result !== 'N' && entry.betAmount > 0
  );

  if (relevantEntries.length < 2) {
    return { hasConsecutiveWins: false, count: 0 };
  }

  // Count consecutive wins at the end
  let consecutiveWins = 0;
  for (let i = relevantEntries.length - 1; i >= 0; i--) {
    if (relevantEntries[i].result === 'W') {
      consecutiveWins++;
    } else {
      break;
    }
  }

  return { 
    hasConsecutiveWins: consecutiveWins >= 2, 
    count: consecutiveWins 
  };
};

// Function to get previous column's Row 1 loss
export const getPreviousColumnRow1Loss = (
  columnNumber: number,
  entries: GameEntry[]
): number => {
  if (columnNumber <= 1) {
    return 0;
  }
  
  const previousColumnRow1 = entries.find(entry => 
    entry.columnNumber === columnNumber - 1 && 
    entry.rowNumber === 1 &&
    entry.result === 'L'
  );
  
  if (previousColumnRow1) {
    // Ensure betAmount is a number, not a Promise
    const betAmount = typeof previousColumnRow1.betAmount === 'number' ? previousColumnRow1.betAmount : 0;
    console.log(`Found previous column's Row 1 loss with bet amount ${betAmount}`);
    return betAmount;
  }
  
  console.log(`Previous column's Row 1 was not a loss or not found`);
  return 0;
};

// Function to get the same row, previous column loss amount
export const getSameRowPreviousColumnLoss = (
  entries: GameEntry[],
  currentColumn: number,
  currentRow: number
): number => {
  if (currentColumn <= 1) {
    return 0; // No previous column for column 1
  }

  // Find the entry in the same row but previous column, skipping No Bet cells
  let prevColumnIndex = -1;
  
  // First, find the most recent entry in the same row of the previous column (even if it's No Bet)
  for (let i = entries.length - 1; i >= 0; i--) {
    if (entries[i].columnNumber === currentColumn - 1 && entries[i].rowNumber === currentRow) {
      prevColumnIndex = i;
      break;
    }
  }
  
  // If we found a No Bet cell, we need to look for the first loss before this No Bet cell
  if (prevColumnIndex >= 0 && entries[prevColumnIndex].betAmount === 0) {
    console.log(`Found No Bet cell in same row of previous column, looking for first loss before No Bet`);
    
    // Look for the first loss before this No Bet cell
    for (let col = currentColumn - 2; col >= 1; col--) {
      for (let i = entries.length - 1; i >= 0; i--) {
        if (
          entries[i].columnNumber === col &&
          entries[i].rowNumber === currentRow &&
          entries[i].result === 'L' &&
          entries[i].betAmount > 0
        ) {
          console.log(`Found loss before No Bet in column ${col}, row ${currentRow} with bet amount ${entries[i].betAmount}`);
          return entries[i].betAmount;
        }
      }
    }
    
    console.log('No loss found before No Bet cell in the same row');
    return 0;
  }
  // If we found a regular cell (not No Bet) and it's a loss, return its bet amount
  else if (prevColumnIndex >= 0 && entries[prevColumnIndex].result === 'L' && entries[prevColumnIndex].betAmount > 0) {
    console.log(`Found same row in previous column loss with bet amount ${entries[prevColumnIndex].betAmount}`);
    return entries[prevColumnIndex].betAmount;
  }

  console.log('Same row in previous column was not a loss or not found');
  return 0;
};

// Get previous loss in the same column
export const getPreviousLossInColumn = (
  entries: GameEntry[],
  columnNumber: number,
  rowNumber: number
): number => {
  if (rowNumber <= 1) {
    return 0;
  }
  
  // For rows >= 3, we should only use the directly previous row's loss
  if (rowNumber >= 3) {
    const previousRowEntry = entries.find(entry => 
      entry.columnNumber === columnNumber && 
      entry.rowNumber === rowNumber - 1 &&
      entry.result === 'L'
    );
    
    if (previousRowEntry) {
      // Ensure betAmount is a number, not a Promise
      const betAmount = typeof previousRowEntry.betAmount === 'number' ? previousRowEntry.betAmount : 0;
      console.log(`Found previous row loss in column ${columnNumber}, row ${previousRowEntry.rowNumber} with bet amount ${betAmount}`);
      return betAmount;
    }
    
    console.log(`Previous row in column ${columnNumber} was not a loss`);
    return 0;
  }
  
  // Get all entries in the current column with row numbers less than the current row
  const previousEntriesInColumn = entries.filter(entry => 
    entry.columnNumber === columnNumber && 
    entry.rowNumber < rowNumber
  );
  
  // Sort by row number descending to get the most recent entries first
  previousEntriesInColumn.sort((a, b) => b.rowNumber - a.rowNumber);
  
  // Find the first loss (the most recent one)
  const previousLoss = previousEntriesInColumn.find(entry => entry.result === 'L');
  
  if (previousLoss) {
    // Ensure betAmount is a number, not a Promise
    const betAmount = typeof previousLoss.betAmount === 'number' ? previousLoss.betAmount : 0;
    console.log(`Found previous loss in column ${columnNumber}, row ${previousLoss.rowNumber} with bet amount ${betAmount}`);
    return betAmount;
  }
  
  console.log(`No previous loss found in column ${columnNumber}`);
  return 0;
};

// Get the last row's loss from the previous column
export const getPreviousColumnLastRowLoss = (
  entries: GameEntry[],
  columnNumber: number
): number => {
  if (columnNumber <= 1) {
    return 0;
  }

  // Get all entries from the previous column
  const previousColumnEntries = entries.filter(
    entry => entry.columnNumber === columnNumber - 1
  );

  if (previousColumnEntries.length === 0) {
    return 0;
  }

  // Find the entry with the highest row number (last row) in the previous column
  let highestRowNumber = 0;
  let lastEntry: GameEntry | undefined;
  
  previousColumnEntries.forEach(entry => {
    if (entry.rowNumber > highestRowNumber) {
      highestRowNumber = entry.rowNumber;
      lastEntry = entry;
    }
  });
  
  // If the last entry is a No Bet cell, look for the first loss before this No Bet cell
  if (lastEntry && lastEntry.betAmount === 0) {
    console.log(`Found No Bet cell in last row of previous column, looking for first loss before No Bet`);
    
    // Look for the first loss in the last row of earlier columns
    for (let col = columnNumber - 2; col >= 1; col--) {
      const earlierColLastRowEntries = entries.filter(
        entry => entry.columnNumber === col && entry.rowNumber === highestRowNumber
      );
      
      const lossEntry = earlierColLastRowEntries.find(
        entry => entry.result === 'L' && entry.betAmount > 0
      );
      
      if (lossEntry) {
        const betAmount = typeof lossEntry.betAmount === 'number' ? lossEntry.betAmount : 0;
        console.log(`Found loss before No Bet in column ${col}, last row with bet amount ${betAmount}`);
        return betAmount;
      }
    }
    
    console.log('No loss found before No Bet cell in the last row');
    return 0;
  }
  // If the last entry is a regular loss, return its bet amount
  else if (lastEntry && lastEntry.result === 'L' && lastEntry.betAmount > 0) {
    // Ensure betAmount is a number, not a Promise
    const betAmount = typeof lastEntry.betAmount === 'number' ? lastEntry.betAmount : 0;
    console.log(`Found previous column's last row loss: column ${columnNumber-1}, row ${lastEntry.rowNumber}, amount ${betAmount}`);
    return betAmount;
  } else {
    console.log(`Previous column's last row (col ${columnNumber-1}, row ${highestRowNumber}) was not a loss`);
  }
  
  return 0;
};

// Function to determine result for Same Recovery logic
export const determineSameRecoveryResult = (
  handValue: 'P' | 'B',
  columnNumber: number,
  rowNumber: number,
  entries: GameEntry[]
): 'W' | 'L' | 'N' => {
  if (rowNumber === 1) {
    return 'N'; // Row 1 is observation row
  }

  // Find the first row entry in the current column
  const firstRowEntry = entries.find(entry => entry.columnNumber === columnNumber && entry.rowNumber === 1);
  
  if (!firstRowEntry) {
    console.log(`First row entry not found for column ${columnNumber}`);
    return 'N';
  }
  
  console.log(`Same-Recovery result: ${handValue === firstRowEntry.handValue ? 'W' : 'L'} (Current hand: ${handValue}, First row hand: ${firstRowEntry.handValue})`);
  return handValue === firstRowEntry.handValue ? 'W' : 'L';
};

// Function to check if recovery mode should end
export const shouldEndRecoveryMode = (
  entries: GameEntry[]
): boolean => {
  const recoveryInfo = isInRecoveryMode(entries);
  
  if (!recoveryInfo.active || recoveryInfo.startIndex < 0) {
    return false;
  }
  
  // Count consecutive wins in recovery mode (excluding No Bet wins)
  let consecutiveWins = 0;
  for (let i = recoveryInfo.startIndex + 1; i < entries.length; i++) {
    // Skip No Bet wins (Seed wins)
    if (entries[i].result === 'W' && entries[i].betAmount > 0) {
      consecutiveWins++;
    } else if (entries[i].result === 'L') {
      consecutiveWins = 0;
    }
  }
  
  // End recovery mode if we have 2 consecutive wins
  return consecutiveWins >= 2;
};

// Function to get detailed bet calculation information for the UI
export const getSameRecoveryNextBetDetails = (
  entries: GameEntry[],
  isGameReset: boolean,
  baseBet: number,
  raiseBet: number,
  rowCount: number
): {
  srpcValue: number;
  pclrlValue: number;
  plscValue: number;
  row1Value: number;
  lossCount: number;
  inRecoveryMode: boolean;
  hasConsecutiveLosses?: boolean;
  isPreviousNoBetLoss?: boolean;
  inSeedRecovery?: boolean;
  seedRecoveryDetails?: {
    totalLossAmount: number;
    lossCount: number;
    recoveryBetAmount: number;
  };
  hasConsecutiveWins?: boolean;
  recoveryDetails?: {
    lossAmount: number;
    consecutiveWins: number;
    recoveryBetAmount: number;
    winsNeededToExit: number;
  };
} => {
  if (isGameReset || entries.length === 0) {
    return {
      srpcValue: 0,
      pclrlValue: 0,
      plscValue: 0,
      row1Value: 0,
      lossCount: 0,
      inRecoveryMode: false,
      hasConsecutiveLosses: false,
      isPreviousNoBetLoss: false,
      inSeedRecovery: false,
      hasConsecutiveWins: false
    };
  }

  // Check for consecutive losses or No Bet cells
  const consecutiveLossesInfo = checkConsecutiveLosses(entries);
  if (consecutiveLossesInfo.hasConsecutiveLosses) {
    return {
      srpcValue: 0,
      pclrlValue: 0,
      plscValue: 0,
      row1Value: 0,
      lossCount: consecutiveLossesInfo.count,
      inRecoveryMode: false,
      hasConsecutiveLosses: true,
      isPreviousNoBetLoss: false,
      inSeedRecovery: false,
      hasConsecutiveWins: false
    };
  }

  // Check if the previous entry was a No Bet loss
  const previousNoBetLossInfo = checkPreviousNoBetLoss(entries);
  if (previousNoBetLossInfo.isPreviousNoBetLoss) {
    return {
      srpcValue: 0,
      pclrlValue: 0,
      plscValue: 0,
      row1Value: 0,
      lossCount: 0,
      inRecoveryMode: false,
      hasConsecutiveLosses: false,
      isPreviousNoBetLoss: true,
      inSeedRecovery: false,
      hasConsecutiveWins: false
    };
  }

  // Check if we're in seed recovery mode (after a No Bet win)
  const seedRecoveryInfo = checkSeedRecovery(entries, baseBet);
  if (seedRecoveryInfo.inSeedRecovery) {
    // Calculate next position
    let nextColumn = 1;
    let nextRow = 1;
    
    if (entries.length > 0) {
      const lastEntry = entries[entries.length - 1];
      nextColumn = lastEntry.columnNumber;
      nextRow = lastEntry.rowNumber + 1;
      
      if (nextRow > rowCount) {
        nextColumn++;
        nextRow = 1;
      }
    }
    
    // Find the first loss before no bet cell
    const srpcLoss = findFirstLossBeforeNoBet(entries, nextColumn, nextRow);
    const srpcAmount = srpcLoss > 0 ? (srpcLoss + raiseBet) + raiseBet : 0;
    
    // Calculate recovery bet amount
    const recoveryBet = seedRecoveryInfo.totalLossAmount + (seedRecoveryInfo.lossCount * baseBet) + raiseBet;
    const finalBetAmount = Math.max(recoveryBet, srpcAmount);
    
    return {
      srpcValue: srpcLoss,
      pclrlValue: 0,
      plscValue: 0,
      row1Value: 0,
      lossCount: seedRecoveryInfo.lossCount,
      inRecoveryMode: false,
      hasConsecutiveLosses: false,
      isPreviousNoBetLoss: false,
      inSeedRecovery: true,
      seedRecoveryDetails: {
        totalLossAmount: seedRecoveryInfo.totalLossAmount,
        lossCount: seedRecoveryInfo.lossCount,
        recoveryBetAmount: finalBetAmount
      },
      hasConsecutiveWins: false
    };
  }

  // Check if we have two consecutive wins (excluding No Bet wins)
  const consecutiveWinsInfo = checkConsecutiveWins(entries);
  if (consecutiveWinsInfo.hasConsecutiveWins) {
    return {
      srpcValue: 0,
      pclrlValue: 0,
      plscValue: 0,
      row1Value: 0,
      lossCount: 0,
      inRecoveryMode: false,
      hasConsecutiveLosses: false,
      isPreviousNoBetLoss: false,
      inSeedRecovery: false,
      hasConsecutiveWins: true
    };
  }

  // Check if we're in recovery mode
  const recoveryMode = isInRecoveryMode(entries);
  
  // Calculate next position
  let nextColumn = 1;
  let nextRow = 1;
  
  if (entries.length > 0) {
    const lastEntry = entries[entries.length - 1];
    nextColumn = lastEntry.columnNumber;
    nextRow = lastEntry.rowNumber + 1;
    
    if (nextRow > rowCount) {
      nextColumn++;
      nextRow = 1;
    }
  }
  
  // If we're in recovery mode, return recovery details
  if (recoveryMode.active) {
    // Count consecutive wins in recovery mode (excluding No Bet wins)
    let consecutiveWins = 0;
    for (let i = recoveryMode.startIndex + 1; i < entries.length; i++) {
      if (entries[i].result === 'W' && entries[i].betAmount > 0) {
        consecutiveWins++;
      } else if (entries[i].result === 'L') {
        consecutiveWins = 0;
      }
    }
    
    // Calculate recovery bet amount
    const recoveryBetBase = Math.ceil(recoveryMode.lossAmount / 3);
    const recoveryBet = recoveryBetBase + (consecutiveWins * raiseBet);
    
    return {
      srpcValue: 0,
      pclrlValue: 0,
      plscValue: 0,
      row1Value: 0,
      lossCount: 0,
      inRecoveryMode: true,
      hasConsecutiveLosses: false,
      isPreviousNoBetLoss: false,
      inSeedRecovery: false,
      hasConsecutiveWins: false,
      recoveryDetails: {
        lossAmount: recoveryMode.lossAmount,
        consecutiveWins,
        recoveryBetAmount: recoveryBet,
        winsNeededToExit: 2 - consecutiveWins
      }
    };
  }
  
  // For Row 1 (observation row, no bet)
  if (nextRow === 1) {
    return {
      srpcValue: 0,
      pclrlValue: 0,
      plscValue: 0,
      row1Value: 0,
      lossCount: 0,
      inRecoveryMode: false
    };
  }
  
  // Get values for bet calculation details
  let sameRowPrevColLoss = 0;
  let prevColLastRowLoss = 0;
  let prevLossInSameCol = 0;
  let prevColRow1Loss = 0;
  let lossCount = 0;
  const hasConsecutiveLosses = false;
  const isPreviousNoBetLoss = false;
  const inSeedRecovery = false;
  const seedRecoveryDetails = null;
  const hasConsecutiveWins = false;
  
  // Check for seed recovery mode first
  const seedRecoveryCheck = checkSeedRecovery(entries, baseBet);
  if (seedRecoveryCheck.inSeedRecovery) {
    const srpcValue = 0;
    const pclrlValue = 0;
    const plscValue = 0;
    const finalBetAmount = 0;
    
    // If we're in a subsequent calculation after the initial seed recovery,
    // get the full SRPC values
    let updatedSrpcValue = 0;
    let updatedPclrlValue = 0;
    let updatedPlscValue = 0;
    
    if (seedRecoveryCheck.applySrpcLogic) {
      // For Row 2, Column 2+
      if (nextRow === 2 && nextColumn > 1) {
        updatedPclrlValue = getPreviousColumnLastRowLoss(entries, nextColumn);
        updatedSrpcValue = getSameRowPreviousColumnLossForRecovery(entries, nextColumn, nextRow);
        // Not used for Row 2
      }
      // For Rows 3+, Column 2+
      else if (nextRow >= 3 && nextColumn > 1) {
        updatedSrpcValue = getSameRowPreviousColumnLossForRecovery(entries, nextColumn, nextRow);
        updatedPlscValue = getPreviousLossInColumn(entries, nextColumn, nextRow);
        // Not used for Rows 3+
      }
    } else {
      // For the first calculation after a seed win, just find the first loss before no bet cell
      updatedSrpcValue = findFirstLossBeforeNoBet(entries, nextColumn, nextRow);
    }
    
    // Calculate recovery bet amount
    const recoveryBet = seedRecoveryCheck.totalLossAmount + (seedRecoveryCheck.lossCount * baseBet) + raiseBet;
    
    // Calculate SRPC amount if applicable
    let srpcAmount = 0;
    if (seedRecoveryCheck.applySrpcLogic) {
      // Start with 0 and collect all loss components, then add one final RBA
      srpcAmount = 0;
      let componentCount = 0;

      // Add (SRPC + RBA) if applicable
      if (updatedSrpcValue > 0) {
        srpcAmount += (updatedSrpcValue + raiseBet);
        componentCount++;
      }

      // Add (PLSC + RBA) if applicable
      if (updatedPlscValue > 0) {
        srpcAmount += (updatedPlscValue + raiseBet);
        componentCount++;
      }

      // Add (PCLRL + RBA) if applicable
      if (updatedPclrlValue > 0) {
        srpcAmount += (updatedPclrlValue + raiseBet);
        componentCount++;
      }

      // Add one final RBA if we have any components
      if (componentCount > 0) {
        srpcAmount += raiseBet;
      }

      // For subsequent calculations after seed win, use only SRPC logic
      // and not the recovery bet amount
      console.log(`Using only SRPC logic for subsequent calculation after seed recovery: ${srpcAmount}`);
    } else if (updatedSrpcValue > 0) {
      srpcAmount = (updatedSrpcValue + raiseBet) + raiseBet;
    }
    
    // For the first calculation after seed win, use recoveryBet
    // For subsequent calculations, use only srpcAmount
    const updatedFinalBetAmount = seedRecoveryCheck.applySrpcLogic ? srpcAmount : Math.max(recoveryBet, srpcAmount);
    
    const hasConsecutiveLosses = false;
    const isPreviousNoBetLoss = false;
    const inSeedRecovery = true;
    const seedRecoveryDetails = {
      totalLossAmount: seedRecoveryCheck.totalLossAmount,
      lossCount: seedRecoveryCheck.lossCount,
      recoveryBetAmount: updatedFinalBetAmount
    };
    const hasConsecutiveWins = false;
    
    return {
      srpcValue: updatedSrpcValue,
      pclrlValue: updatedPclrlValue,
      plscValue: updatedPlscValue,
      row1Value: 0,
      lossCount: seedRecoveryCheck.lossCount,
      inRecoveryMode: false,
      hasConsecutiveLosses,
      isPreviousNoBetLoss,
      inSeedRecovery,
      seedRecoveryDetails,
      hasConsecutiveWins
    };
  }
  
  // Check for consecutive losses
  const consecutiveLossesCheck = checkConsecutiveLosses(entries);
  if (consecutiveLossesCheck.hasConsecutiveLosses) {
    return {
      srpcValue: 0,
      pclrlValue: 0,
      plscValue: 0,
      row1Value: 0,
      lossCount: consecutiveLossesCheck.count,
      inRecoveryMode: false,
      hasConsecutiveLosses: true,
      isPreviousNoBetLoss: false,
      inSeedRecovery: false,
      hasConsecutiveWins: false
    };
  }
  
  // Check for No Bet loss
  const previousNoBetLossCheck = checkPreviousNoBetLoss(entries);
  if (previousNoBetLossCheck.isPreviousNoBetLoss) {
    return {
      srpcValue: 0,
      pclrlValue: 0,
      plscValue: 0,
      row1Value: 0,
      lossCount: 0,
      inRecoveryMode: false,
      hasConsecutiveLosses: false,
      isPreviousNoBetLoss: true,
      inSeedRecovery: false,
      hasConsecutiveWins: false
    };
  }
  
  // Get standard calculation values
  sameRowPrevColLoss = getSameRowPreviousColumnLoss(entries, nextColumn, nextRow);
  prevColLastRowLoss = getPreviousColumnLastRowLoss(entries, nextColumn);
  prevLossInSameCol = getPreviousLossInColumn(entries, nextColumn, nextRow);
  prevColRow1Loss = getPreviousColumnRow1Loss(nextColumn, entries);
  
  // Count total losses
  lossCount = entries.filter(entry => entry.result === 'L').length;
  
  return {
    srpcValue: sameRowPrevColLoss,
    pclrlValue: prevColLastRowLoss,
    plscValue: prevLossInSameCol,
    row1Value: prevColRow1Loss,
    lossCount,
    inRecoveryMode: false,
    hasConsecutiveLosses,
    isPreviousNoBetLoss,
    inSeedRecovery,
    hasConsecutiveWins
  };
};

// Check if all entries in a column are losses
export const areAllEntriesInColumnLosses = (
  entries: GameEntry[],
  columnNumber: number
): boolean => {
  // Get all entries from the specified column
  const columnEntries = entries.filter(
    entry => entry.columnNumber === columnNumber && entry.result !== 'N'
  );

  if (columnEntries.length === 0) {
    return false;
  }

  // Check if all entries are losses
  const allLosses = columnEntries.every(entry => entry.result === 'L');
  
  if (allLosses) {
    console.log(`All entries in column ${columnNumber} are losses`);
  }
  
  return allLosses;
};

// Check if there are any wins in a column
export const hasAnyWinInColumn = (
  entries: GameEntry[],
  columnNumber: number
): boolean => {
  // Get all entries from the specified column
  const columnEntries = entries.filter(
    entry => entry.columnNumber === columnNumber && entry.result !== 'N'
  );

  if (columnEntries.length === 0) {
    return false;
  }

  // Check if there is at least one win
  const hasWin = columnEntries.some(entry => entry.result === 'W');
  
  if (hasWin) {
    console.log(`Column ${columnNumber} has at least one win`);
  }
  
  return hasWin;
};


