import { GameEntry, ExtensionFactor } from '@/types/game';

// Get previous loss in the same column for Win Loss logic
export const getPreviousLossInColumn = (
  entries: GameEntry[],
  columnNumber: number,
  rowNumber: number
): number => {
  if (rowNumber <= 1) {
    return 0;
  }
  
  let previousLoss = 0;
  
  for (let i = rowNumber - 1; i >= 1; i--) {
    const entry = entries.find(e => 
      e.columnNumber === columnNumber && 
      e.rowNumber === i &&
      e.handValue === 'B'  // For win-loss, look for 'B' (loss)
    );
    
    if (entry) {
      // Ensure betAmount is a number, not a Promise
      previousLoss = typeof entry.betAmount === 'number' ? entry.betAmount : 0;
      break;
    }
  }
  
  return previousLoss;
};

// Get same row previous column loss for Win Loss logic
export const getSameRowPreviousColumnLoss = (
  entries: GameEntry[],
  columnNumber: number,
  rowNumber: number
): number => {
  if (columnNumber <= 1) return 0;
  
  // For win-loss, find the most recent loss in the same row but previous column
  const entry = entries.find(
    entry => entry.columnNumber === columnNumber - 1 && 
            entry.rowNumber === rowNumber && 
            entry.handValue === 'B'  // For win-loss, look for 'B' (loss)
  );
  
  // Ensure betAmount is a number, not a Promise
  return entry ? (typeof entry.betAmount === 'number' ? entry.betAmount : 0) : 0;
};

// Get the last row loss from the previous column
export const getPreviousColumnLastRowLoss = (
  columnNumber: number,
  entries: GameEntry[]
): number => {
  if (columnNumber <= 1) {
    return 0;
  }

  // Get all entries from the previous column
  const previousColumnEntries = entries.filter(
    entry => entry.columnNumber === columnNumber - 1
  );

  if (previousColumnEntries.length === 0) {
    return 0;
  }

  // Find the entry with the highest row number (last row) that is a loss (handValue === 'B')
  let lastRowLoss = 0;
  let highestRowNumber = 0;
  
  previousColumnEntries.forEach(entry => {
    if (entry.rowNumber > highestRowNumber) {
      highestRowNumber = entry.rowNumber;
      if (entry.handValue === 'B') {
        // Ensure betAmount is a number, not a Promise
        lastRowLoss = typeof entry.betAmount === 'number' ? entry.betAmount : 0;
      } else {
        lastRowLoss = 0; // Reset if it's not a loss
      }
    }
  });
  
  return lastRowLoss;
};

// Get consecutive losses for Column 1
export const getConsecutiveLosses = (entries: GameEntry[]): number => {
  if (entries.length === 0) {
    return 0;
  }
  
  let consecutiveLosses = 0;
  for (let i = entries.length - 1; i >= 0; i--) {
    if (entries[i].handValue === 'B') {
      consecutiveLosses++;
    } else {
      break;
    }
  }
  
  return consecutiveLosses;
};

// Dedicated function to calculate next bet amount for Win Loss logic
export const calculateWinLossNextBet = (
  entries: GameEntry[],
  isGameReset: boolean,
  baseBet: number,
  raiseBet: number,
  rowCount: number
): number => {
  if (isGameReset || entries.length === 0) {
    return baseBet;
  }

  // Calculate next position
  let nextColumn = 1;
  let nextRow = 1;
  
  if (entries.length > 0) {
    const lastEntry = entries[entries.length - 1];
    nextColumn = lastEntry.columnNumber;
    nextRow = lastEntry.rowNumber + 1;
    
    if (nextRow > rowCount) {
      nextColumn++;
      nextRow = 1;
    }
  }
  
  console.log(`Calculating for "Win-Loss" logic: Column ${nextColumn}, Row ${nextRow}`);
  
  // For Column 1
  if (nextColumn === 1) {
    // For Column 1 Row 1
    if (nextRow === 1) {
      return baseBet;
    }
    
    // For Column 1, Row 2+, use consecutive losses calculation
    const consecutiveLosses = getConsecutiveLosses(entries);
    if (consecutiveLosses > 0) {
      const betAmount = baseBet + (raiseBet * consecutiveLosses);
      console.log(`Column 1, Row ${nextRow}: Based on ${consecutiveLosses} consecutive losses = ${betAmount}`);
      return betAmount;
    }
    return baseBet;
  }
  
  // For Column 2+, Row 1 - use SRPC + RBA + PCLRL
  if (nextRow === 1) {
    console.log(`Column ${nextColumn}, Row 1 calculation`);
    
    // Start with RBA
    let result = raiseBet;
    console.log(`Starting with RBA: ${result}`);
    
    // Add Same Row Previous Column Loss directly
    const sameRowPrevColLoss = getSameRowPreviousColumnLoss(entries, nextColumn, nextRow);
    if (sameRowPrevColLoss > 0) {
      result += sameRowPrevColLoss;
      console.log(`Adding SRPC directly: ${sameRowPrevColLoss}`);
    }
    
    // Add Previous Column's Last Row Loss + RBA
    const prevColLastRowLoss = getPreviousColumnLastRowLoss(nextColumn, entries);
    if (prevColLastRowLoss > 0) {
      const withRBA = prevColLastRowLoss + raiseBet;
      result += withRBA;
      console.log(`Adding (PCLRL + RBA): ${withRBA} (${prevColLastRowLoss} + ${raiseBet})`);
    }
    
    console.log(`Final Row 1 bet: ${result}`);
    return result;
  }
  
  // Check if previous row in the same column is a win
  const prevRowSameColumn = entries.find(e => 
    e.columnNumber === nextColumn && 
    e.rowNumber === nextRow - 1
  );
  
  // For Column 2+, Row 2+ calculations
  console.log(`Column ${nextColumn}, Row ${nextRow} calculation`);
  
  // Always include SRPC regardless of whether previous row was a win or not
  const sameRowPrevColLoss = getSameRowPreviousColumnLoss(entries, nextColumn, nextRow);
  
  // If previous row in same column was a win, return baseBet + SRPC
  if (prevRowSameColumn && prevRowSameColumn.handValue === 'P') {
    let result = baseBet;
    
    // Add SRPC to result
    if (sameRowPrevColLoss > 0) {
      result += sameRowPrevColLoss;
      console.log(`Previous row win, but still adding SRPC: ${sameRowPrevColLoss}`);
    }
    
    console.log(`Previous row (${nextRow-1}) in Column ${nextColumn} was a win, returning baseBet + SRPC: ${result}`);
    return result;
  }
  
  // Start with RBA
  let result = raiseBet;
  console.log(`Starting with RBA: ${result}`);
  
  // Add Same Row Previous Column Loss directly
  if (sameRowPrevColLoss > 0) {
    result += sameRowPrevColLoss;
    console.log(`Adding SRPC directly: ${sameRowPrevColLoss}`);
  }
  
  // Add Previous Loss in Same Column + RBA (if there is a loss)
  const prevLossInSameCol = getPreviousLossInColumn(entries, nextColumn, nextRow);
  if (prevLossInSameCol > 0) {
    const withRBA = prevLossInSameCol + raiseBet;
    result += withRBA;
    console.log(`Adding (PLSC + RBA): ${withRBA} (${prevLossInSameCol} + ${raiseBet})`);
  }
  
  console.log(`Final bet for Column ${nextColumn}, Row ${nextRow}: ${result}`);
  return result;
};

// Determine result for Win Loss logic
export const determineWinLossResult = (handValue: 'P' | 'B'): 'W' | 'L' | 'N' => {
  return handValue === 'P' ? 'W' : 'L';
};

// Add getWinLossNextBetDetails function for useBetCalculationDetails.ts
export const getWinLossNextBetDetails = (
  entries: GameEntry[],
  isGameReset: boolean,
  baseBet: number,
  raiseBet: number,
  rowCount: number
) => {
  if (isGameReset || entries.length === 0) {
    return {
      baseBet,
      raiseBet,
      sameRowPreviousLoss: 0,
      previousColumnLastLoss: 0,
      previousLossInColumn: 0,
      previousColumnRow1Loss: 0,
      previousColumnRow2Loss: 0,
      lossCount: 0
    };
  }

  // Calculate next position
  let nextColumn = 1;
  let nextRow = 1;
  
  if (entries.length > 0) {
    const lastEntry = entries[entries.length - 1];
    nextColumn = lastEntry.columnNumber;
    nextRow = lastEntry.rowNumber + 1;
    
    if (nextRow > rowCount) {
      nextColumn++;
      nextRow = 1;
    }
  }

  // Get the relevant calculation values
  const sameRowPreviousLoss = getSameRowPreviousColumnLoss(entries, nextColumn, nextRow);
  const previousColumnLastLoss = getPreviousColumnLastRowLoss(nextColumn, entries);
  const previousLossInColumn = getPreviousLossInColumn(entries, nextColumn, nextRow);
  const lossCount = getConsecutiveLosses(entries);

  return {
    baseBet,
    raiseBet,
    sameRowPreviousLoss,
    previousColumnLastLoss,
    previousLossInColumn,
    previousColumnRow1Loss: 0, // Not used in this logic
    previousColumnRow2Loss: 0, // Not used in this logic
    lossCount
  };
};
