import { GameEntry } from '@/types/game';

/**
 * Calculate the next bet amount specifically for Win-Loss-Previous-Rows logic.
 * This function is completely isolated from other prediction logics.
 */
export function calculateWinLossPrevRowsNextBet(
  entries: GameEntry[],
  isGameReset: boolean,
  baseBet: number,
  raiseBet: number,
  rowCount: number
): number {
  // For empty game or reset game, return base bet
  if (isGameReset || entries.length === 0) {
    return baseBet;
  }

  // Calculate the next position
  const nextPosition = calculateNextPositionForWinLossPrevRows(entries, rowCount);
  const { columnNumber, rowNumber } = nextPosition;

  // For any row in column 1
  if (columnNumber === 1) {
    return baseBet;
  }

  // Check if the previous column in the same row was a WIN
  const prevColSameRowEntry = entries.find(
    entry => entry.columnNumber === columnNumber - 1 && 
            entry.rowNumber === rowNumber &&
            entry.handValue === 'P'  // 'P' indicates a win
  );

  // If the previous column in the same row was a WIN, return base bet
  if (prevColSameRowEntry) {
    console.log(`Previous column same row was a WIN, returning base bet: ${baseBet}`);
    return baseBet;
  }

  // Special handling for Row 5 when Row 3 in same column is a loss
  if (rowNumber === 5) {
    // Check for Row 3 loss in current column
    const row3Entry = entries.find(
      entry => entry.columnNumber === columnNumber && 
              entry.rowNumber === 3 && 
              entry.handValue === 'B'  // 'B' indicates a loss
    );
    
    if (row3Entry) {
      console.log(`Row 3 in column ${columnNumber} was a LOSS, calculating special Row 5 bet`);
      // Formula: (Row 3 Loss + RBA) + RBA
      // Ensure betAmount is a number, not a Promise
      const betAmount = typeof row3Entry.betAmount === 'number' ? row3Entry.betAmount : 0;
      const calculatedAmount = betAmount + raiseBet + raiseBet;
      console.log(`Row 5 special calculation: (${betAmount} + ${raiseBet}) + ${raiseBet} = ${calculatedAmount}`);
      return calculatedAmount;
    }
  }

  // Get previous losses in the same row
  const previousRowLosses = getSameRowPreviousColumnLossesForWinLossPrevRows(
    entries, 
    columnNumber, 
    rowNumber
  );

  if (previousRowLosses > 0) {
    // Count losses in this row after the last win
    const lossCount = countLossesAfterLastWinInRow(entries, columnNumber, rowNumber);
    
    // Formula: Sum of previous losses + (RaiseBet * loss count) + RaiseBet
    console.log(`Win-Loss-Prev-Rows calculation: ${previousRowLosses} + (${raiseBet} * ${lossCount}) + ${raiseBet}`);
    return previousRowLosses + (raiseBet * lossCount) + raiseBet;
  }

  return baseBet;
}

/**
 * Calculate the next position specifically for Win-Loss-Previous-Rows logic.
 * This is isolated from the common function used by other prediction logics.
 */
export function calculateNextPositionForWinLossPrevRows(
  entries: GameEntry[],
  rowCount: number
): { columnNumber: number; rowNumber: number } {
  if (entries.length === 0) {
    return { columnNumber: 1, rowNumber: 1 };
  }

  const lastEntry = entries[entries.length - 1];
  let nextColumnNumber = lastEntry.columnNumber;
  let nextRowNumber = lastEntry.rowNumber + 1;

  if (nextRowNumber > rowCount) {
    nextColumnNumber++;
    nextRowNumber = 1;
  }

  return { columnNumber: nextColumnNumber, rowNumber: nextRowNumber };
}

/**
 * Get the sum of previous column losses for the same row,
 * specifically for Win-Loss-Previous-Rows logic.
 */
export function getSameRowPreviousColumnLossesForWinLossPrevRows(
  entries: GameEntry[],
  currentColumnNumber: number,
  currentRowNumber: number
): number {
  if (currentColumnNumber <= 1) return 0;

  // Find the last column with a win for this row
  let lastWinColumn = 0;
  for (let col = 1; col < currentColumnNumber; col++) {
    const entry = entries.find(e => 
      e.columnNumber === col && 
      e.rowNumber === currentRowNumber &&
      e.handValue === 'P'  // 'P' indicates a win
    );
    
    if (entry) {
      lastWinColumn = col;
    }
  }
  
  // Sum losses that occurred after the last win
  let totalLoss = 0;
  for (let col = lastWinColumn + 1; col < currentColumnNumber; col++) {
    const entry = entries.find(e => 
      e.columnNumber === col && 
      e.rowNumber === currentRowNumber &&
      e.handValue === 'B'  // 'B' indicates a loss
    );
    
    if (entry) {
      // Ensure betAmount is a number, not a Promise
      const betAmount = typeof entry.betAmount === 'number' ? entry.betAmount : 0;
      totalLoss += betAmount;
    }
  }
  
  return totalLoss;
}

/**
 * Count the number of losses after the last win in the same row,
 * specifically for Win-Loss-Previous-Rows logic.
 */
export function countLossesAfterLastWinInRow(
  entries: GameEntry[],
  currentColumnNumber: number,
  currentRowNumber: number
): number {
  let lastWinColumn = 0;
  for (let col = 1; col < currentColumnNumber; col++) {
    const entry = entries.find(e => 
      e.columnNumber === col && 
      e.rowNumber === currentRowNumber &&
      e.handValue === 'P'  // 'P' indicates a win
    );
    
    if (entry) {
      lastWinColumn = col;
    }
  }
  
  // Count losses after the last win
  let lossCount = 0;
  for (let col = lastWinColumn + 1; col < currentColumnNumber; col++) {
    const entry = entries.find(e => 
      e.columnNumber === col && 
      e.rowNumber === currentRowNumber &&
      e.handValue === 'B'  // Count only losses
    );
    
    if (entry) {
      lossCount++;
    }
  }
  
  return lossCount;
}

/**
 * Determine the result for Win-Loss-Previous-Rows logic.
 * This function is completely isolated from other prediction logics.
 */
export function determineWinLossPrevRowsResult(handValue: 'P' | 'B'): 'W' | 'L' | 'N' {
  // Simple determination: P = Win, B = Loss
  return handValue === 'P' ? 'W' : 'L';
}

// Additional helper function for detailed calculations - needed for useBetCalculationDetails.ts
export function getWinLossPrevRowsNextBetDetails(
  entries: GameEntry[],
  isGameReset: boolean,
  baseBet: number,
  raiseBet: number,
  rowCount: number
) {
  if (isGameReset || entries.length === 0) {
    return {
      baseBet,
      raiseBet,
      sameRowPreviousLoss: 0,
      previousColumnLastLoss: 0,
      previousLossInColumn: 0,
      previousColumnRow1Loss: 0,
      previousColumnRow2Loss: 0,
      lossCount: 0
    };
  }

  // Calculate the next position
  const nextPosition = calculateNextPositionForWinLossPrevRows(entries, rowCount);
  const { columnNumber, rowNumber } = nextPosition;

  // Get same row previous column losses
  const sameRowPreviousLoss = getSameRowPreviousColumnLossesForWinLossPrevRows(
    entries, 
    columnNumber, 
    rowNumber
  );

  // Count losses after last win
  const lossCount = countLossesAfterLastWinInRow(entries, columnNumber, rowNumber);

  // For detailed calculation display
  return {
    baseBet,
    raiseBet,
    sameRowPreviousLoss,
    previousColumnLastLoss: 0, // Not used in this logic
    previousLossInColumn: 0,   // Not used in this logic
    previousColumnRow1Loss: 0, // Not used in this logic
    previousColumnRow2Loss: 0, // Not used in this logic
    lossCount
  };
}
